OCR Log Column Configuration Feature – Product Requirements Document
Feature Overview

Enable users to customize the Excel log structure by defining column mappings, renaming headers, and selecting which fields to display in their OCR processing log.

User Stories
Primary User Story

As an OCR log user, I want to configure which columns my data appears in and customize field names so that the log structure matches my workflow and organizational standards.

Secondary User Story

As an OCR log user, I want the system to detect when I manually rearrange columns in Excel so that my configuration stays synchronized without requiring manual updates.

Feature Components
1. Column Configuration User Form

Purpose: Provide a visual interface for users to configure log column settings.

Form Structure:

Essential Fields Frame (fraEssential):
From field configuration
To field configuration
Letter Reference configuration
Letter Date configuration
Subject configuration
References configuration
Body configuration
Helper Fields Frame (fraHelper):
Summary field configuration
Topics/Tags configuration
Tracing Fields Frame (fraTracing):
Response Letter configuration
Response Date configuration
Referring Letter configuration
Referring Date configuration

Control Elements per Field:

Checkbox: Enable/disable the field in the log
RefEdit Control: Select the target column
TextBox: Display/edit the column header name
2. Configuration Storage

Configuration JSON Structure:

{
  "logConfiguration": {
    "essentialFields": {
      "from": {
        "enabled": true,
        "column": "A",
        "headerName": "From",
        "defaultName": "From"
      },
      "to": {
        "enabled": true,
        "column": "B",
        "headerName": "To",
        "defaultName": "To"
      },
      "letterReference": {
        "enabled": true,
        "column": "C",
        "headerName": "Reference",
        "defaultName": "Letter Reference"
      },
      "letterDate": {
        "enabled": true,
        "column": "D",
        "headerName": "Date",
        "defaultName": "Letter Date"
      },
      "subject": {
        "enabled": true,
        "column": "E",
        "headerName": "Subject",
        "defaultName": "Subject"
      },
      "references": {
        "enabled": true,
        "column": "F",
        "headerName": "References",
        "defaultName": "References"
      },
      "body": {
        "enabled": true,
        "column": "G",
        "headerName": "Body",
        "defaultName": "Body"
      }
    },
    "helperFields": {
      "summary": {
        "enabled": false,
        "column": "H",
        "headerName": "Summary",
        "defaultName": "Summary"
      },
      "tags": {
        "enabled": false,
        "column": "I",
        "headerName": "Tags",
        "defaultName": "Topics / Tags"
      }
    },
    "tracingFields": {
      "responseLetter": {
        "enabled": false,
        "column": "J",
        "headerName": "Response Ref",
        "defaultName": "Response Letter"
      },
      "responseDate": {
        "enabled": false,
        "column": "K",
        "headerName": "Response Date",
        "defaultName": "Response Date"
      },
      "referringLetter": {
        "enabled": false,
        "column": "L",
        "headerName": "Referring Ref",
        "defaultName": "Referring Letter"
      },
      "referringDate": {
        "enabled": false,
        "column": "M",
        "headerName": "Referring Date",
        "defaultName": "Referring Date"
      }
    },
    "lastModified": "2024-01-01T00:00:00",
    "version": "1.0"
  }
}

Implementation Tasks
Task 1: Form Initialization
Load configuration from JSON file
Read existing configuration or create default
Populate checkboxes with enabled/disabled states
Set RefEdit controls to show current column assignments
Display current header names in textboxes
Set default values
Pre-populate textboxes with default field names
Set essential fields as checked by default
Set helper and tracing fields as unchecked by default
Task 2: User Interaction Handlers
Checkbox Change Events
Enable/disable associated RefEdit and TextBox when checkbox state changes
Update configuration object in memory
RefEdit Control Events
Validate selected range is a single column
Prevent duplicate column assignments
Show warning if column already assigned to another field
Update configuration object with new column assignment
TextBox Change Events
Allow custom header names
Validate against empty strings
Update configuration object with new header name
Task 3: Form Action Buttons
OK Button Implementation
Validate all enabled fields have column assignments
Validate no duplicate column assignments
Save configuration to JSON file
Apply configuration to active log worksheet
Update headers in Excel based on configuration
Close form and return success status
Cancel Button Implementation
Discard any changes made
Close form without saving
Return to previous state
Refresh Button Implementation
Re-read current Excel structure
Update form to reflect current state
Detect any manual changes made to columns
Task 4: Configuration Application
Apply Configuration to Worksheet
Create/update headers based on enabled fields
Arrange columns according to configuration
Hide columns for disabled fields
Format headers consistently
Update Existing Data
Move existing data to new column positions if changed
Preserve data integrity during column rearrangement
Handle empty columns gracefully
Task 5: Configuration Persistence
Save Configuration
Write configuration to JSON file in application directory
Include timestamp of last modification
Maintain backup of previous configuration
Load Configuration
Read JSON file on application startup
Handle missing configuration file gracefully
Validate configuration structure
Apply default configuration if file is corrupted
Task 6: Auto-Detection Feature (Investigation Phase)
Column Change Detection
Monitor worksheet change events
Identify when headers are renamed
Detect when columns are moved
Track when columns are inserted or deleted
Synchronization Logic
Compare current worksheet structure with saved configuration
Identify discrepancies
Prompt user to update configuration or revert changes
Option to auto-update configuration based on manual changes
Implementation Approaches to Investigate
Worksheet_Change event monitoring
Periodic structure validation
Header text comparison
Column order tracking
Data pattern recognition
Task 7: Validation and Error Handling
Input Validation
Ensure column selections are valid
Prevent circular references
Validate header names (no special characters that Excel doesn't allow)
Check for minimum required fields
Error Messages
Clear user feedback for validation failures
Helpful suggestions for resolving conflicts
Confirmation dialogs for destructive actions
Recovery Mechanisms
Ability to reset to default configuration
Undo recent configuration changes
Import/export configuration for sharing
Task 8: User Experience Enhancements
Visual Feedback
Highlight conflicts in real-time
Show preview of log structure
Color-code field categories
Indicate required vs optional fields
Tooltips and Help
Add tooltips to explain each field's purpose
Provide examples of valid column selections
Include help documentation
Keyboard Navigation
Tab order optimization
Keyboard shortcuts for common actions
Enter key to confirm, Escape to cancel
Technical Considerations
Data Integrity
Ensure no data loss during column reconfiguration
Maintain referential integrity for linked data
Preserve formulas and formatting where possible
Performance
Efficient configuration loading and saving
Minimal impact on Excel performance
Batch updates to reduce screen flicker
Compatibility
Support for different Excel versions
Handle protected worksheets appropriately
Work with filtered and sorted data
Success Criteria
Users can successfully map any field to any column
Custom header names persist across sessions
Disabled fields do not appear in the log
Configuration changes are applied without data loss
The system provides clear feedback for all user actions
Manual worksheet changes can be detected (investigation outcome)
Future Enhancements
Configuration templates for common log formats
Bulk enable/disable options
Column width and formatting preferences
Advanced validation rules per field
Multi-language support for headers
Configuration versioning and rollback
Detailed Implementation Reference
OCR Log Column Configuration Implementation Guide
Control Reference and Implementation Details
FORM CONTROL MAPPING

Essential Fields Controls

chkLogFrom – Enable/Disable From field
refFrom – Column selection for From field
txtFrom – Header name for From field
chkLogTo – Enable/Disable To field
refTo – Column selection for To field
txtTo – Header name for To field
chkLogReference – Enable/Disable Letter Reference field
refReference – Column selection for Letter Reference
txtReference – Header name for Letter Reference
chkLogDate – Enable/Disable Letter Date field
refDate – Column selection for Letter Date
txtDate – Header name for Letter Date
chkLogSubject – Enable/Disable Subject field
refSubject – Column selection for Subject
txtSubject – Header name for Subject
chkLogReferences – Enable/Disable References field
refReferences – Column selection for References
txtReferences – Header name for References
chkLogBody – Enable/Disable Body field
refBody – Column selection for Body
txtBody – Header name for Body

Helper Fields Controls

chkLogSummary – Enable/Disable Summary field
refSummary – Column selection for Summary
txtSummary – Header name for Summary
chkLogTags – Enable/Disable Topics/Tags field
refTags – Column selection for Tags
txtTags – Header name for Tags

Tracing Fields Controls

chkLogResponse – Enable/Disable Response Letter field
refResponse – Column selection for Response Letter
txtResponse – Header name for Response Letter
chkLogResponseDate – Enable/Disable Response Date field
refResponseDate – Column selection for Response Date
txtResponseDate – Header name for Response Date
chkLogRefering – Enable/Disable Referring Letter field
refRefering – Column selection for Referring Letter
txtRefering – Header name for Referring Letter
chkLogReferingDate – Enable/Disable Referring Date field
refReferingDate – Column selection for Referring Date
txtReferingDate – Header name for Referring Date
IMPLEMENTATION FUNCTIONS
' Configuration Type Definition
Type FieldConfig
    Enabled As Boolean
    Column As String
    HeaderName As String
    DefaultName As String
End Type

Type LogConfiguration
    FromField As FieldConfig
    ToField As FieldConfig
    ReferenceField As FieldConfig
    DateField As FieldConfig
    SubjectField As FieldConfig
    ReferencesField As FieldConfig
    BodyField As FieldConfig
    SummaryField As FieldConfig
    TagsField As FieldConfig
    ResponseField As FieldConfig
    ResponseDateField As FieldConfig
    ReferringField As FieldConfig
    ReferringDateField As FieldConfig
End Type

' Global configuration variable
Dim CurrentConfig As LogConfiguration

' FORM INITIALIZATION
Private Sub UserForm_Initialize()
    ' Load configuration from JSON
    LoadConfiguration
    
    ' Populate form controls with configuration
    PopulateFormControls
    
    ' Set initial control states
    UpdateControlStates
End Sub

' CHECKBOX EVENT HANDLERS
Private Sub chkLogFrom_Click()
    refFrom.Enabled = chkLogFrom.Value
    txtFrom.Enabled = chkLogFrom.Value
End Sub

' ... (repeat for each field as in your original)

' REFEDIT VALIDATION
Private Function ValidateColumnSelection(refControl As Control, fieldName As String) As Boolean
    Dim selectedRange As Range
    Dim col As String
    
    On Error Resume Next
    Set selectedRange = Range(refControl.Value)
    On Error GoTo 0
    
    If selectedRange Is Nothing Then
        MsgBox "Please select a valid column for " & fieldName, vbExclamation
        ValidateColumnSelection = False
        Exit Function
    End If
    
    ' Check if selection is a single column
    If selectedRange.Columns.Count > 1 Then
        MsgBox "Please select only one column for " & fieldName, vbExclamation
        ValidateColumnSelection = False
        Exit Function
    End If
    
    ' Check for duplicate column assignments
    col = Split(selectedRange.Address, "$")(1)
    If IsDuplicateColumn(col, refControl.Name) Then
        MsgBox "Column " & col & " is already assigned to another field", vbExclamation
        ValidateColumnSelection = False
        Exit Function
    End If
    
    ValidateColumnSelection = True
End Function

' CONFIGURATION MANAGEMENT
Private Sub LoadConfiguration()
    Dim fso As Object
    Dim jsonFile As Object
    Dim jsonText As String
    Dim configPath As String
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    configPath = ThisWorkbook.Path & "\OCRLogConfig.json"
    
    If fso.FileExists(configPath) Then
        Set jsonFile = fso.OpenTextFile(configPath, 1)
        jsonText = jsonFile.ReadAll
        jsonFile.Close
        
        ' Parse JSON and populate CurrentConfig
        ParseJSONConfig jsonText
    Else
        ' Load default configuration
        LoadDefaultConfiguration
    End If
End Sub

Private Sub SaveConfiguration()
    Dim fso As Object
    Dim jsonFile As Object
    Dim jsonText As String
    Dim configPath As String
    
    ' Build configuration from form controls
    BuildConfigurationFromForm
    
    ' Convert to JSON
    jsonText = ConvertConfigToJSON()
    
    ' Save to file
    Set fso = CreateObject("Scripting.FileSystemObject")
    configPath = ThisWorkbook.Path & "\OCRLogConfig.json"
    
    Set jsonFile = fso.CreateTextFile(configPath, True)
    jsonFile.Write jsonText
    jsonFile.Close
    
    MsgBox "Configuration saved successfully!", vbInformation
End Sub

' AUTO-DETECTION FUNCTIONS
Private Sub DetectManualChanges()
    Dim ws As Worksheet
    Dim headerRow As Range
    Dim cell As Range
    Dim detectedChanges As Collection
    Dim change As Variant
    
    Set ws = ActiveSheet
    Set headerRow = ws.Rows(1) ' Assuming headers are in row 1
    Set detectedChanges = New Collection
    
    ' Check each configured field
    For Each cell In headerRow.Cells
        If Not IsEmpty(cell.Value) Then
            ' Compare with saved configuration
            If IsFieldModified(cell) Then
                detectedChanges.Add Array(cell.Column, cell.Value)
            End If
        End If
    Next cell
    
    ' If changes detected, prompt user
    If detectedChanges.Count > 0 Then
        If MsgBox("Manual changes detected in the log structure. " & _
                  "Would you like to update the configuration?", _
                  vbYesNo + vbQuestion) = vbYes Then
            UpdateConfigurationFromWorksheet
        End If
    End If
End Sub

' UTILITY FUNCTIONS
Private Function GetColumnLetter(colNumber As Integer) As String
    Dim dividend As Integer
    Dim modulo As Integer
    Dim columnName As String
    
    dividend = colNumber
    columnName = ""
    
    Do While dividend > 0
        modulo = (dividend - 1) Mod 26
        columnName = Chr(65 + modulo) & columnName
        dividend = Int((dividend - modulo) / 26)
    Loop
    
    GetColumnLetter = columnName
End Function

Private Sub ApplyConfigurationToWorksheet()
    Dim ws As Worksheet
    Dim col As Integer
    
    Set ws = ActiveSheet
    
    ' Clear existing headers
    ws.Rows(1).ClearContents
    
    ' Apply enabled fields with their custom headers
    If chkLogFrom.Value Then
        col = GetColumnNumber(refFrom.Value)
        ws.Cells(1, col).Value = txtFrom.Text
    End If
    
    If chkLogTo.Value Then
        col = GetColumnNumber(refTo.Value)
        ws.Cells(1, col).Value = txtTo.Text
    End If
    
    ' Continue for all other fields...
    
    ' Format headers
    With ws.Rows(1)
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .Borders.LineStyle = xlContinuous
    End With
End Sub

' BUTTON HANDLERS
Private Sub cmdOK_Click()
    ' Validate all selections
    If Not ValidateAllSelections() Then Exit Sub
    
    ' Save configuration
    SaveConfiguration
    
    ' Apply to worksheet
    ApplyConfigurationToWorksheet
    
    ' Close form
    Unload Me
End Sub

Private Sub cmdCancel_Click()
    ' Discard changes and close
    Unload Me
End Sub

Private Sub cmdRefresh_Click()
    ' Detect manual changes
    DetectManualChanges
    
    ' Refresh form display
    PopulateFormControls
End Sub
