# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

VBAOCR is a VBA-based OCR system for Microsoft Excel that extracts text from PDFs and images using multiple AI/ML providers. The system is specifically tailored for construction project documentation, extracting structured fields like letter references, dates, subjects, and body content.

## Commands

### Development Commands

This is a VBA project with XVBA tooling support. The project uses:
- `@localsmart/xvba-cli` for development support
- No build/test/lint commands are defined in package.json
- Development is done directly in Excel VBA IDE or through XVBA tooling

### External Dependencies

Before running the OCR system, ensure these services are available:
- **Ghostscript**: Required for PDF to image conversion (used by Ollama/OpenRouter vision modes)
  - **Preferred**: Install full Ghostscript with DLL support (`gsdll64.dll`/`gsdll32.dll`) for silent operation
  - **Fallback**: Command-line Ghostscript (`gswin64c.exe`/`gswin32c.exe`) - causes screen flashing
- **Docling Server**: Run on localhost:5001 for document processing
- **Ollama**: Run locally for vision language models (auto-detects available models)
- **LM Studio**: Run locally for additional vision language models (auto-detects available models)

## Architecture

### Core Processing Flow

The system implements a flexible two-step workflow:

```
User Selection → Primary OCR Source → [Optional] Field Extraction → Excel Output
```

**Workflow Options:**
1. **OCR Only**: Extract text from PDFs/images using either:
   - Direct OCR (Docling Server) when toggle is ON
   - VLM providers (Ollama/LM Studio/OpenRouter) when toggle is OFF
2. **Fields Only**: Extract structured fields from existing OCR text in Body column
3. **OCR + Fields**: Perform both OCR and field extraction in sequence

### Workflow Diagram

```
                    ┌─────────────────────────────┐
                    │   Ribbon Workflow Buttons   │
                    └──────────┬──────────────────┘
         ┌─────────────────────┼─────────────────────┐
         ▼                     ▼                     ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   OCR Only      │  │  Fields Only    │  │  OCR + Fields   │
└────────┬────────┘  └────────┬────────┘  └────────┬────────┘
         │                     │                     │
         ▼                     ▼                     ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│ Direct OCR      │  │ Read Existing   │  │ Check OCR Source│
│ Toggle Status?  │  │ Text from Body  │  │ (Toggle Status) │
└────────┬────────┘  └────────┬────────┘  └────────┬────────┘
    ┌────┴────┐                │              ┌─────┴─────┐
    ▼         ▼                │              ▼           ▼
┌───ON───┐ ┌──OFF──┐           │         ┌───ON───┐  ┌──OFF──┐
│Docling │ │  VLM  │           │         │Docling │  │  VLM  │
└────┬───┘ └───┬───┘           │         └────┬───┘  └───┬───┘
     │         │               │              │           │
     ▼         ▼               ▼              ▼           ▼
┌─────────────────┐  ┌─────────────────┐  ┌──────────────────┐
│Perform OCR with │  │Extract Fields   │  │1. Perform OCR    │
│Docling Server   │  │from Text using  │  │2. Extract Fields │
│(Direct OCR)     │  │Selected Provider│  │   if Enabled     │
└─────────────────┘  └─────────────────┘  └──────────────────┘
         │                     │                     │
         ▼                     ▼                     ▼
┌─────────────────────────────────────────────────────────┐
│                    Write Results to Excel               │
│  - OCR Only: Text → Body column                         │
│  - Fields Only: Extracted fields → Selected columns     │
│  - OCR + Fields: Both OCR text and extracted fields     │
└─────────────────────────────────────────────────────────┘

Key Components:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Direct OCR Toggle (ON)  → Uses Docling Server for OCR
Direct OCR Toggle (OFF) → Uses VLM provider from Settings
VLM Providers: Ollama, LM Studio, OpenRouter (configured in Settings)
Field Extraction: Always uses extraction provider from Settings
```

### Key Modules

1. **OCRManager** (`vba-files/Module/OCRManager.bas`): Main orchestrator.
   - Entry point: `ProcessPDFOCR` - processes PDFs from Excel worksheet with workflow mode control.
   - Core functions:
     - `ProcessSinglePDF()` - Main two-step workflow orchestrator with mode parameter support
     - `PerformOCRStep()` - Handles Step 1 (OCR text extraction)
     - `PerformExtractionStep()` - Handles Step 2 (field extraction from OCR text)
   - **Workflow Modes**: OCR Only, Fields Only, OCR + Fields (controlled via ribbon buttons)
   - **Direct OCR Support**: Seamless integration with Docling provider routing
   - Supports independent provider/model selection for each step
   - Reads paths from column A, writes results to relevant columns based on selected fields.

2. **Provider Modules** (in `vba-files/Module/`):
   - `Prov_Ollama`: Local VLM using Ollama.
   - `Prov_OpenRouter`: Cloud VLM/OCR service.
   - `Prov_DoclingOCR`/`Prov_DoclingOCRWeb`: Docling server integration.
   - `Prov_LMStudio`: Local VLM using LM Studio with full OpenAI-compatible API support.
   - **Standardized Interface**: All provider modules implement:
     - `IsProviderAvailable() As Boolean`
     - `GetAvailableModels() As Collection`
     - `GetProviderType() As String`
     - `ProcessPDFWith[Provider](pdfPath, prompt, config, Optional modelName, Optional extractionMode) As OCRResult`
     - `SendTextTo[Provider](textContent, prompt, config, Optional modelName) As OCRResult` - For text-only field extraction

3. **Configuration** (`vba-files/Module/OCRConfig.bas`):
   - Loads settings from `config/ocr_project_config.json`.
   - **Key Functions**:
     - `GetWorkflowSetting(KeyName, defaultValue)` - Supports nested keys like "extraction.provider"
     - `SetWorkflowSetting(KeyName, Value)` - Supports nested keys for configuration updates
     - `GetCurrentWorkflowSettingsObject()` / `RestoreWorkflowSettingsObject()` - Backup/restore workflow state
     - `RefreshProviderModels(providerID As String)` - Dynamic model discovery
     - `GetFieldSelectionState(fieldName)` / `SetFieldSelectionState(fieldName, Value)` - Field selection management
     - `GetAllFieldSelectionStates()` - Returns Dictionary of all field states
     - `GetProviderDetails(providerID)` - Returns complete provider configuration
   - **Provider Constants**: `PID_OLLAMA`, `PID_OPENROUTER`, `PID_LMSTUDIO`, `PID_DOCLING`
   - Handles automatic model discovery and default setup on initial run or config reset.
   - New workflow configuration structure separates OCR and extraction settings.

4. **Response Parsing** (`vba-files/Module/ResponseParser.bas`):
   - Unified parsing for all providers.
   - Extracts structured fields from OCR results based on user selection in `FrmSettings`.
   - Supports two-step extraction from raw OCR text.

5. **User Interface**:
   - **Ribbon** (`ribbons/customUI14.xml`, `vba-files/Module/RibbonCallbacks.bas`): 
     - **Direct Action Buttons**: "OCR Only", "Fields Only", and "OCR + Fields" for clear workflow control
     - **Direct OCR Toggle**: Switch between Docling (Direct OCR) and AI Vision Models 
     - **Quick Access**: Configuration and Information buttons for immediate settings access
     - Provides main entry points with enhanced visual organization
   - **Settings UserForm** (`vba-files/Form/FrmSettings.frm`, `vba-files/Module/UserFromSettings.bas`): 
     - Comprehensive dialog for configuring OCR providers, models, extraction settings, and field selection.
     - Separate provider/model selection for OCR and field extraction steps.
     - Dynamic model loading with refresh capability.
     - Field selection checkboxes (From, To, Letter Ref, Letter Date, Subject, References, Body, Summary, Topics/Tags).
     - Workflow source control moved to ribbon (Direct OCR toggle).

6. **Progress Feedback** (`vba-files/Module/ProgressHelpers.bas`, `vba-files/Form/FrmProgress.frm`):
   - **Key Functions**:
     - `ShowProgress(statusText)` - Display modeless progress dialog
     - `HideProgress()` - Close progress dialog
   - Prevents UI freezing during model discovery and processing.
   - Used extensively during provider initialization and long-running operations.

7. **Ghostscript API Integration** (`vba-files/Class/clsGhostscriptAPI.cls`):
   - **Class-Based Design**: Encapsulated as a class module for better resource management and state handling
   - **Direct DLL Integration**: Replaces terminal-based Ghostscript commands to eliminate screen flashing
   - **Key Properties**:
     - `IsAvailable` (read-only) - Lazy loads DLL and checks availability
     - `DllPath` (read-only) - Returns the loaded DLL path
     - `LastError` (read-only) - Stores last error description
   - **Key Methods**:
     - `ConvertPDFPageToImage(pdfPath, pageNum)` - Silent PDF to image conversion
     - `ConvertPDFToImages()` - Batch conversion with custom parameters
     - `GetPDFPageCount(pdfPath)` - Get page count using PostScript commands (falls back to terminal method if needed)
     - `TestAPI()` - Integration testing and verification
   - **Cross-Platform Support**: 32-bit and 64-bit VBA compatibility (`gsdll32.dll`/`gsdll64.dll`)
   - **Enhanced OCRUtils Integration**: `OCRUtils.ConvertPDFPageToImage()` uses singleton instance, tries API first, falls back to terminal
   - **Memory Management**: Proper C-style string array allocation/deallocation with automatic cleanup in Class_Terminate
   - **Error Handling**: Comprehensive error codes and graceful fallbacks with LastError property

### Processing Workflows

1. **VLM Direct**: Vision models (Ollama/OpenRouter/LM Studio) directly OCR images/PDFs
2. **Docling + VLM**: Docling OCR → Optional VLM field extraction
3. **OpenRouter OCR**: Direct text extraction via OpenRouter
4. **Two-Step Processing**: Any OCR provider → Different provider for field extraction

### Module Dependencies

- **VBA-Web**: HTTP client library (WebClient, WebRequest, WebResponse)
- **VBA-JSON**: JSON parsing (JsonConverter)
- **Custom Modules**: MultipartFormData, PromptTemplates, OCRUtils, ProgressHelpers
- **Custom Classes**: clsGhostscriptAPI, clsWebClient, clsWebRequest, clsWebResponse, clsWebAsyncWrapper, clsWebCrypto
- **System DLLs**: Ghostscript DLLs (`gsdll64.dll`/`gsdll32.dll`) for silent PDF processing

### Configuration Structure

Configuration is stored in `config/ocr_project_config.json` with new workflow structure:
```json
{
  "ocr_settings": {
    "current_workflow": {
      "primary_ocr_source": "VLM",
      "ocr_provider": "LMStudio", 
      "ocr_model": "olmocr-7b-0225-preview",
      "extraction": {
        "enabled": true,
        "provider": "LMStudio",
        "model": "llama-3.3-70b-instruct",
        "fields": {
          "From": true,
          "To": true,
          "Letter Reference": true,
          "Letter Date": true,
          "Subject": true,
          "References": true,
          "Body": true,
          "Summary": true,
          "Topics / Tags": true
        }
      }
    }
  },
  "providers": {
    "Ollama": {
      "base_url": "http://localhost:11434",
      "api_key": "",
      "models": []
    },
    "OpenRouter": {
      "base_url": "https://openrouter.ai/api/v1",
      "api_key": "",
      "models": []
    },
    "LMStudio": {
      "base_url": "http://localhost:1234",
      "api_key": "",
      "models": []
    },
    "Docling": {
      "base_url": "http://localhost:5001",
      "api_key": "",
      "models": []
    }
  }
}
```

### Important Patterns

1. **Standardized Provider Interface**: All provider modules (`Prov_*.bas`) implement:
   - `IsProviderAvailable() As Boolean`
   - `GetAvailableModels() As Collection`
   - `GetProviderType() As String`
   - `ProcessPDFWith[Provider](pdfPath, prompt, config, Optional modelName, Optional extractionMode) As OCRResult`
   - `SendTextTo[Provider](textContent, prompt, config, Optional modelName) As OCRResult`
2. **Two-Step Workflow Support**: 
   - Step 1: OCR using `ocr_provider` + `ocr_model` (PDF/image → text)
   - Step 2: Field extraction using `extraction.provider` + `extraction.model` (text → structured fields)
   - Independent provider/model selection for each step
3. **OCRResult Type** (defined in `OCRTypes.bas`):
   - Extended with new fields: `FromText`, `ToText`, `Summary`, `Tags`
   - Unified structure across all providers
4. **Error Handling & Logging**:
   - `OCRUtils.LogToFile` with toggleable logging via `SetLogging(enabled)`
   - Enhanced error recovery (Step 1 results returned if Step 2 fails)
5. **Configuration Management**:
   - Nested configuration structure in JSON
   - Workflow settings accessible via dot notation (e.g., "extraction.provider")
   - Dynamic model discovery and caching
6. **UI State Management**: Excel UI state (screen updating, status bar) managed primarily in `OCRManager.bas` and `UserFromSettings.bas`.

### Working with the Code

- **Provider Development**: Modify provider behavior in respective `Prov_*` modules
- **New Providers**: Follow existing pattern and implement standardized interface:
  - Required: `IsProviderAvailable()`, `GetAvailableModels()`, `GetProviderType()`
  - Processing: `ProcessPDFWith[Provider]()`, `SendTextTo[Provider]()`
- **UI Changes**: Update RibbonCallbacks and ribbons/customUI14.xml
- **Workflow Control**: 
  - **Ribbon Interface**: Use ribbon buttons for workflow mode (OCR Only, Fields Only, OCR + Fields)
  - **Direct OCR Toggle**: Controls primary OCR source (Docling vs AI Vision Models)
  - **Settings Form**: Configure provider preferences and field selections (workflow source control moved to ribbon)
- **Configuration**: Use `OCRConfig.bas` functions for all configuration management:
  - `GetWorkflowSetting()` / `SetWorkflowSetting()` for nested settings
  - `GetFieldSelectionState()` / `SetFieldSelectionState()` for field management
  - `RefreshProviderModels()` for dynamic model discovery
- **Testing**: Use test functions in OCRManager or individual provider modules
- **Ghostscript Integration**: 
  - Use `OCRUtils.TestGhostscriptAPI()` to verify DLL availability and functionality
  - `OCRUtils.ConvertPDFPageToImage()` automatically tries API first, falls back to terminal
  - The clsGhostscriptAPI class handles all DLL operations with proper resource management
- **Settings UI**: Use `FrmSettings` and `UserFromSettings.bas` for comprehensive configuration management
- **Progress Feedback**: Use `ProgressHelpers.ShowProgress()` / `HideProgress()` for long operations

## Recent Architectural Improvements (December 2024 - June 2025)

### Core Workflow Enhancements
- **Two-Step Workflow Implementation**: Complete separation of OCR and field extraction steps with independent provider/model selection.
- **Improved Provider Handling**: Fixed base URL and endpoint handling for all providers (LM Studio, Ollama, OpenRouter).
- **Enhanced JSON Configuration**: Reorganized structure with nested extraction settings and cleaner field organization.
- **Dynamic Model Discovery**: Automatic detection of available models from local providers on startup.

### Major UI/UX Refactor (June 2025)
- **Ribbon Interface Redesign**: 
  - Replaced workflow dropdown with 3 direct action buttons: "OCR Only", "Fields Only", and "OCR + Fields"
  - Added Direct OCR toggle button to switch between Docling (Direct OCR) and AI Vision Models
  - Added Configuration and Information buttons for quick access to settings
  - Enhanced ribbon layout with better grouping and visual organization
- **Workflow Mode Control**: 
  - Ribbon buttons now directly control workflow execution mode
  - Direct OCR toggle separates OCR source control from provider selection
  - Removed extraction.enabled from config - now controlled by button selection
- **Class Naming Standardization**: Renamed Web* classes to cls* prefix following VBA conventions

### Provider Updates
- **LM Studio Integration**: Added full support with dynamic model detection and proper OpenAI-compatible JSON response parsing.
- **Provider Interface Standardization**: All providers now support optional `modelName` and `extractionMode` parameters.
- **Unified Docling Provider**: Added proper PID_DOCLING integration with config structure and routing.
- **Improved Error Handling**: Enhanced logging and error recovery throughout the OCR pipeline.

### UI/UX Improvements
- **Settings Form Redesign**: 
  - Separate sections for OCR and Extraction configuration
  - Dynamic model refresh capability
  - Clear visual separation of workflow steps
  - Removed workflow source dropdown (now controlled via ribbon)
- **Progress Feedback**: Added modeless progress dialog for long-running operations.
- **Enhanced Validation**: Fields Only mode validates Body column has existing text before processing.
- **Performance Optimization**: 
  - Consolidated provider health checks
  - Cached availability status
  - Suppressed unnecessary message boxes during initialization

### Technical Improvements
- **Prompt Templates**: Added `BuildFieldExtractionPrompt(ocrText)` for two-step workflow field extraction.
- **Enhanced Prompt Engineering** (`vba-files/Module/PromptTemplates.bas`):
  - **Structured Prompts**: Clear sections for Document Type, Data Extraction Requirements, Special Instructions, and Output Format
  - **Model-Aware Prompting**: Automatic detection of small models (1B-8B) with simplified prompts via `IsSmallModel()` function
  - **Concrete Examples**: Real construction letter examples instead of generic placeholders
  - **Field-Specific Instructions**: Clear guidance for problematic fields (From/To organizations, Letter Reference vs Subject, References section)
  - **Minimal Prompt Mode**: `BuildMinimalJSONStructureString()` for resource-constrained models
  - **Targeted Extraction**: `BuildTargetedExtractionPrompt()` for extracting only specific problematic fields
- **Logging Control**: Implemented toggleable logging with `SetLogging(enabled)` routine in `OCRUtils.bas`.
- **Memory Management**: Proper form unloading/reloading to prevent memory buildup.
- **Configuration Migration**: Automatic migration from legacy config structure to new nested format.
- **Provider Constants**: Standardized provider identifiers (`PID_OLLAMA`, `PID_OPENROUTER`, `PID_LMSTUDIO`, `PID_DOCLING`).
- **Form Loading Optimization**: 
  - Added `FormLoadingHelpers.bas` module for optimized Settings form initialization
  - Implemented quick loading with cached provider status and lazy background updates
  - Fixed model list population timing issues for both OCR and Extraction providers
  - Sequential model list updates to prevent VBA conflicts when updating multiple lists
- **Ghostscript API Integration**: 
  - Replaced terminal-based commands with direct DLL API calls
  - Eliminated screen flashing during PDF to image conversion and page counting
  - Added cross-platform 32/64-bit VBA support
  - Implemented graceful fallback to terminal method when DLL unavailable
  - Enhanced memory management for C-style string arrays
  - Full page counting support with PostScript commands and binary search fallback

## Critical Bug Fixes (June 2025)

### Enhanced Prompt Engineering for Small Models
- **Problem Solved**: Poor extraction accuracy with small models (1B-8B) due to vague prompts
- **Root Causes**: Generic placeholders, no concrete examples, ambiguous field definitions
- **Solution**:
  1. **Structured Prompt Sections**: Separated prompts into clear sections (Document Type, Extraction Requirements, Instructions, Output Format)
  2. **Field-Specific Guidance**: Added explicit instructions for problematic fields:
     - From/To: "The company/organization sending/receiving the letter (check letterhead and signature block)"
     - Letter Reference: "The alphanumeric reference code (look for 'Our Reference:', 'Our Ref:', etc. - NOT the subject line)"
     - References: "Any documents mentioned in a 'References:' section (usually numbered 1., 2., etc.)"
  3. **Concrete Examples**: Replaced generic placeholders with real construction letter examples
  4. **Model-Aware Prompting**: Automatic detection of small models with simplified prompts
  5. **Minimal Prompt Mode**: Condensed version for very small models with essential information only
- **Key Functions Added**:
  - `IsSmallModel(modelName)`: Detects small models by name patterns
  - `GetOptimizedJSONStructure()`: Selects appropriate prompt based on model size
  - `BuildMinimalJSONStructureString()`: Simplified prompts for small models
  - `BuildTargetedExtractionPrompt()`: Focused extraction for problematic fields only
- **Testing**: Use `GenerateTestPrompt()` to compare standard vs minimal prompt versions

### Universal JSON Response Handling
- **Problem Solved**: Fixed persistent JSON parsing errors and structural mismatches across providers
- **Root Cause**: Models returning nested JSON objects (e.g., `"from": {"name": "...", "title": "..."}`) when flat strings were expected
- **Solution**: 
  1. **Smart Field Extraction**: Implemented `GetSmartJsonValue()` in ResponseParser that intelligently handles:
     - Flat string values: `"from": "John Doe"`
     - Nested objects: `"from": {"name": "John Doe", "title": "Manager"}`
     - Arrays and complex structures
     - Automatic extraction of common sub-fields (`name`, `value`, `text`)
  2. **Simplified JSON Cleaning**: Reduced `CleanJsonFromMarkdown()` to only:
     - Extract JSON from markdown code blocks
     - Remove leading text before JSON
     - Trust JsonConverter for parsing (no manual sanitization)
  3. **Consistent Parsing**: All providers now use `ParseOCRFields()` with field selection
  - **Key Improvements**:
    - No provider-specific JSON handling needed.
    - Works with various JSON structures from different models (flat strings, nested objects, arrays).
    - Eliminates issues from overly aggressive "sanitization" or double-cleaning.
    - Correctly handles common LLM quirks like using backticks for strings or returning triple-escaped quotes.
    - Specifically ensures the "body" field extracts readable text even from complex structures like `{"text": [array_of_paragraphs]}`.
    - Ensures that the "OCR-only" step correctly extracts plain text even if the OCR model returns a JSON object (e.g., `{"natural_text": "..."}`).
- **Cross-Provider Benefits**: Single, robust parsing logic handles variations from all providers more effectively.

## Completed Features ✅

### 1. Ribbon Integration - Excel ribbon with OCR controls ✅
- Custom Excel ribbon with OCR processing controls
- Settings form integration
- Progress feedback during operations

### 2. VBA-Web Fix - Fixed AddFile method for proper HTTP file uploads ✅
- Resolved HTTP file upload issues
- Enhanced multipart form data handling
- Improved API communication reliability

### 3. Dynamic Model Discovery - Automatic model detection for Ollama with configuration persistence ✅
- Automatic detection of available models from local providers
- Configuration persistence across sessions
- Dynamic refresh capability in settings form

### 4. Workflow Stability Fixes - Fixed empty cells issue and general workflow improvements ✅
- Resolved empty cell processing issues
- Enhanced error handling throughout the workflow
- Improved overall system stability

### 5. LM Studio Integration - Added as a full provider with extraction capabilities ✅
- Complete LM Studio provider implementation
- Full OpenAI-compatible API support
- Dynamic model detection and configuration

### 6. Enhanced Prompts - Improved extraction accuracy with better prompting ✅
- Model-aware prompting for small models (1B-8B)
- Concrete construction letter examples
- Field-specific extraction guidance
- Structured prompt sections for clarity

### 7. Two-Step OCR/Extraction - Independent configuration of OCR and extraction providers/models ✅
- Separate provider/model selection for OCR and extraction steps
- Flexible workflow configuration
- Enhanced settings form with dedicated sections

### 8. Ghostscript DLL Integration - Silent PDF processing via direct DLL calls ✅
- Direct DLL API integration replacing terminal commands
- Eliminated screen flashing during PDF conversion
- Cross-platform 32/64-bit VBA support
- Graceful fallback to terminal method when DLL unavailable

### 9. Universal JSON Response Handling - Standardized parsing across all providers ✅
- Smart field extraction handling nested JSON objects
- Consistent parsing logic across all providers
- Enhanced error handling for various JSON structures
- Simplified JSON cleaning and validation

### 10. OpenRouter API Enhancement - **COMPLETED** ✅
- Fixed file upload functionality for OpenRouter OCR
- Implemented model choice between mistral-ocr (default) and pdf-text (native)
- Added error handling for API limits and rate limiting
- Created dedicated configuration section in settings form
- Fixed model list corruption issues
- Implemented proper separation between general models and file-compatible models
- Enhanced retry logic for intermittent failures
- API key validation improvements

### 11. Ribbon Interface Redesign - **COMPLETED** ✅
- **Direct Action Buttons**: Replaced workflow dropdown with "OCR Only", "Fields Only", and "OCR + Fields" buttons
- **Direct OCR Toggle**: Added toggle to switch between Docling (Direct OCR) and AI Vision Models
- **Quick Access Controls**: Configuration and Information buttons for immediate settings access
- **Enhanced Visual Organization**: Better grouping and iconography for improved UX
- **Workflow Mode Integration**: Ribbon buttons directly control processing workflow
- **Class Naming Standardization**: Renamed Web* classes to cls* prefix following VBA conventions

## In Progress Features 🔄

(No features currently in progress)

## Planned Features (Priority Order)

### 12. Document Viewer Panel
- **Create modeless form or task pane for viewing full letter content**
- Add navigation between documents (prev/next)
- Include copy/export functionality
- Implement basic text search within current document
- Add direct link from Excel cell to open corresponding document

### 13. Enhanced Field Extraction
- **Add configurable field order on worksheet**
- Implement parent letter reference extraction (first reference mentioned)
- Improve summary generation with focused prompts
- Create topic tagging system with master list to prevent duplicates

### 14. Document Management
- **Add configurable source folder paths**
- Implement letter reference pattern recognition (regex-based)
- Create folder-based bulk processing with smart filtering
- Add optional file organization based on extracted metadata

### 15. Smart Page Detection
- **Analyze PDF structure to identify main letter vs. attachments**
- Implement configurable page limits with intelligent defaults
- Add preview option to verify page selection before processing
- Create heuristics for detecting signature pages

### 16. Docling Configuration Interface
- **Add dedicated settings tab for Docling parameters**
- Implement OCR engine selection (Tesseract/other options)
- Add image quality preprocessing controls
- Create test function to verify Docling configuration

### 17. RAG System & Knowledge Graph (Future)
- **Build document indexing foundation**
- Implement basic semantic search across processed documents
- Create simple knowledge graph from extracted relationships
- Add exploration interface for document connections
- **Special Improvements for Features 12-13**

**For Feature 12 (Document Viewer Panel):**
1. Design considerations:
   - Use modeless form to allow interaction with Excel
   - Support both plain text and formatted markdown display
   - Include image placeholders from Docling processing

2. Integration with workflow:
   - Add right-click menu option on cells to "View Full Content"
   - Create keyboard shortcut to open current document
   - Add "Copy to Clipboard" functionality
   - Include option to export current document as text/markdown

3. Navigation features:
   - Add document browser with filtering capabilities
   - Implement search across loaded documents
   - Create document comparison view (side-by-side)
