{"ocr_settings": {"current_workflow": {"primary_ocr_source_id": "LMStudio", "extract_fields": false, "vlm_provider_id_for_extraction": "OpenRouter", "vlm_model_for_extraction": "model-name-here", "docling_transport_id": "DoclingCustom", "extraction_provider_id": "OpenRouter", "field_selection": {"From": true, "To": true, "LetterRef": true, "LetterDate": true, "Subject": true, "References": true, "Body": true, "Summary": true, "Tags": true}, "extraction_model": "model-name-here"}, "providers": {"Ollama": {"id": "Ollama", "name": "Ollama", "type": "VLM", "api_url": "http://localhost:11434/api/generate", "models": [], "default_model": ""}, "OpenRouter": {"id": "OpenRouter", "name": "OpenRouter", "type": "VLM", "api_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "sk-or-v1-your-api-key-here", "models": ["google/gemma-3-27b-it:free", "google/gemini-2.0-flash-exp:free", "meta-llama/llama-3.2-11b-vision-instruct:free", "qwen/qwen2.5-vl-72b-instruct:free"], "default_model": "qwen/qwen2.5-vl-72b-instruct:free"}, "DoclingCustom": {"id": "DoclingCustom", "name": "Docling Custom HTTP", "type": "<PERSON><PERSON>", "api_url": "http://localhost:5001/convert", "models": ["text", "markdown", "json"], "default_model": "markdown"}, "LMStudio": {"id": "LMStudio", "name": "LM Studio", "type": "VLM", "api_url": "http://localhost:1234/v1/chat/completions", "models": [], "default_model": ""}}, "general_settings": {"image_dpi": 150, "log_file_path": "logs/OCR_Debug_Log.txt", "temp_cleanup_delay_ms": 2000}}}