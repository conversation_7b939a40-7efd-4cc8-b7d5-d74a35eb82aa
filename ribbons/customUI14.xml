<customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui" onLoad="Ribbon_OnLoad">
  <ribbon>
    <tabs>
      <tab id="tabVBAOCR" label="VBA OCR">
        
        <!-- Group: Processing -->
        <group id="grpProcessing" label="Processing">
          <button id="btnProcessOCROnly" 
                  label="OCR Only" 
                  size="large" 
                  onAction="ProcessOCROnly_Clicked" 
                  imageMso="TextBoxInsert"
                  screentip="Extract text from PDFs without field extraction" />
          <button id="btnProcessFieldsOnly" 
                  label="Fields Only" 
                  size="large" 
                  onAction="ProcessFieldsOnly_Clicked" 
                  imageMso="TableOfContentsAddTextGallery"
                  screentip="Extract fields from existing text" />
          <button id="btnProcessOCRAndFields" 
                  label="OCR + Fields" 
                  size="large" 
                  onAction="ProcessOCRAndFields_Clicked" 
                  imageMso="PostcardWizard"
                  screentip="Extract text and fields in one operation" />
          <separator id="sepProcessingConfig" />
          <toggleButton id="tglDirectOCR" 
                  label="Direct OCR" 
                  imageMso="ResolveBusinessDataEntityInstanceConflict" 
                  onAction="OnAction_DirectOCR" 
                  getPressed="GetPressed_DirectOCR"
                  screentip="Toggle between Direct OCR (Docling) and AI Vision Models" />
          <button id="btnOpenProcessingConfiguration" 
                  label="Configuration" 
                  onAction="OpenSettingsForm_Clicked" 
                  imageMso="ComAddInsDialog" />
          <button id="btnCurrentConfiguration" 
                  label="Information" 
                  onAction="ShowConfiguration_Clicked" 
                  imageMso="InformationDialog"
                  screentip="Show Current OCR and Extraction Configration" />
        </group>

        <!-- Group: Utilities -->
        <group id="grpUtilities" label="Utilities">
          <button id="btnSetupWorksheet" 
                  label="Setup Log" 
                  size="large"
                  onAction="SetupWorksheet_Clicked" 
                  imageMso="TablePropertiesDialog" />
          <button id="btnSource" 
                  label="Setup Source" 
                  size="large"
                  onAction="SetupSource_Clicked" 
                  imageMso="CopyToFolder" />
        </group>

        <!-- Group: Settings -->
        <group id="grpSettings" label="Settings">
        <!-- Settings Dialogue -->
        <button id="btnOpenSettingsUForm" 
                  label="Settings" 
                  size="large" 
                  onAction="OpenSettingsForm_Clicked" 
                  imageMso="CurrentViewSettings" />
          <!-- Message Box Controls -->
          <separator id="sepMessageBoxControls" />
          <checkBox id="chkMessageBoxToggle" 
                    label="Enable Message Boxes" 
                    getPressed="GetPressed_MessageBoxToggle"
                    onAction="OnAction_MessageBoxToggle" />
          <button id="btnSetMessageBoxTimeout" 
                  label="Set Timeout" 
                  onAction="SetMessageBoxTimeout_Clicked" 
                  imageMso="TimeInsert" />
          
          <!-- Other Advanced Functions -->
          <separator id="sepAdvancedFunctions" />
          <button id="btnCompareAllMethods" 
                  label="Compare All Methods" 
                  onAction="CompareAllMethods_Clicked" 
                  imageMso="CompareSideBySide" />
          <button id="btnTestConnections" 
                  label="Test Connections" 
                  onAction="TestConnections_Clicked" 
                  imageMso="NetworkConnections" />
          <button id="btnViewDebugLog" 
                  label="View Debug Log" 
                  onAction="ViewDebugLog_Clicked" 
                  imageMso="FileDiagnostics" />
        </group>
          
        <!-- Help -->
        <group id="grpHelp" label="Help">
          <button id="btnHelp" 
                  label="Help" 
                  size="large" 
                  onAction="DisplayHelp_Clicked" 
                  imageMso="Help" />
        </group>

      </tab>
    </tabs>
  </ribbon>
</customUI>
