Attribute VB_Name = "OCRConfig"
'
' OCR Configuration Module
' Manages API providers, settings, and configuration from config.json
' Author: AI Assistant
'

Option Explicit

' --- Configuration State Variables ---
Private configData As Object ' Parsed JSON data from config.json
Private Const CONFIG_FILE_PATH As String = "config/ocr_project_config.json" ' Relative to workbook path

' --- Provider Status Cache Variables ---
Private m_ProviderStatusCache As Object ' Dictionary storing provider availability status
Private m_LastStatusCheck As Object ' Dictionary storing last check timestamps
Private m_ModelListCache As Object ' Dictionary storing cached model lists
Private m_LastModelCheck As Object ' Dictionary storing last model check timestamps
Private Const CACHE_LIFETIME_SECONDS As Long = 300 ' 5 minutes cache lifetime

' --- Default values (used if config.json is missing or invalid) ---
Private Const DEFAULT_PRIMARY_OCR_SOURCE_ID As String = "Ollama" ' Default provider ID
Private Const DEFAULT_EXTRACT_FIELDS As Boolean = True
Private Const DEFAULT_PRIMARY_OCR_MODEL As String = "" ' Default model - will be set by RefreshProviderModels
Private Const DEFAULT_VLM_PROVIDER_ID_FOR_EXTRACTION_LEGACY As String = "OpenRouterOCR"
Private Const DEFAULT_VLM_MODEL_FOR_EXTRACTION_LEGACY As String = "" ' Default model - will be set by RefreshProviderModels
Private Const DEFAULT_DOCLING_TRANSPORT_ID As String = "DoclingCustom"

' --- New Default values for dedicated extraction provider ---
Private Const DEFAULT_EXTRACTION_PROVIDER_ID As String = "OpenRouterOCR"
Private Const DEFAULT_EXTRACTION_MODEL As String = "" ' Default model - will be set by RefreshProviderModels

' Constants for provider IDs (matching config.json)
Public Const PID_OLLAMA As String = "Ollama"
Public Const PID_OPENROUTER As String = "OpenRouter"
Public Const PID_OPENROUTER_OCR As String = "OpenRouterOCR"
Public Const PID_DOCLING As String = "Docling"
Public Const PID_DOCLING_CUSTOM As String = "DoclingCustom" ' Legacy - kept for backward compatibility
Public Const PID_DOCLING_WEB As String = "DoclingWeb" ' Legacy - kept for backward compatibility
Public Const PID_LMSTUDIO As String = "LMStudio"


'===============================================================================
' INITIALIZATION & CONFIG FILE HANDLING
'===============================================================================

' Load configuration from JSON file when workbook opens or module initializes
Public Sub AutoExec_Open() ' This can be called from Workbook_Open
    InitializeOCRConfig
End Sub

Public Sub InitializeOCRConfig()
    Dim isNewConfig As Boolean
    isNewConfig = False ' Flag to check if config was newly created

    LoadConfigurationFromJSON isNewConfig ' Pass flag by reference
    
    If configData Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: CRITICAL - configData is Nothing after Load. Cannot proceed with dynamic refresh."
        ' Minimal fallback if everything failed
        SetCurrentPrimaryOCRSourceID DEFAULT_PRIMARY_OCR_SOURCE_ID
        SetPrimaryOCRModel "" ' No specific model
        ' Extraction fields setting removed - controlled by ribbon buttons
        SetExtractionProviderID DEFAULT_EXTRACTION_PROVIDER_ID
        SetExtractionModel "" ' No specific model
        SetDoclingTransportID DEFAULT_DOCLING_TRANSPORT_ID
        SetVLMProviderIDForExtraction DEFAULT_VLM_PROVIDER_ID_FOR_EXTRACTION_LEGACY
        SetVLMModelForExtraction ""
    Else
        ' If the config was newly created by LoadConfigurationFromJSON,
        ' or if critical workflow settings are missing, refresh dynamic models.
        Dim refreshNeeded As Boolean
        refreshNeeded = isNewConfig
        
        ' Also check if essential workflow models are empty, suggesting a need for refresh
        If GetPrimaryOCRModel() = "" And (GetCurrentPrimaryOCRSourceID() = PID_OLLAMA Or GetCurrentPrimaryOCRSourceID() = PID_LMSTUDIO Or GetCurrentPrimaryOCRSourceID() = PID_OPENROUTER Or GetCurrentPrimaryOCRSourceID() = PID_OPENROUTER_OCR) Then
            refreshNeeded = True
        End If
        If GetExtractionModel() = "" And (GetExtractionProviderID() = PID_OLLAMA Or GetExtractionProviderID() = PID_LMSTUDIO Or GetExtractionProviderID() = PID_OPENROUTER Or GetExtractionProviderID() = PID_OPENROUTER_OCR) Then
            refreshNeeded = True
        End If

        ' Check if unified Docling provider is missing and add it
        If GetProviderDetails(PID_DOCLING) Is Nothing Then
            OCRUtils.LogToFile "OCRConfig: Adding missing unified Docling provider to existing config"
            AddMissingProvider PID_DOCLING, "Docling", "Docling", "http://localhost:5001/convert", Array()
            SaveConfigurationToJSON
        End If
        
        If refreshNeeded Then
            OCRUtils.LogToFile "OCRConfig: New or incomplete config detected. Refreshing dynamic provider models..."
            Dim providersToRefresh As Variant
            Dim providerID As Variant
            providersToRefresh = Array(PID_OLLAMA, PID_LMSTUDIO, PID_OPENROUTER, PID_OPENROUTER_OCR)
            For Each providerID In providersToRefresh
                RefreshProviderModels CStr(providerID)
            Next providerID
            ' After refreshing, the default models in current_workflow should be updated by RefreshProviderModels
            ' if they were initially empty and the provider is active.
            ' No need to explicitly set them here again unless RefreshProviderModels failed to do so.
            OCRUtils.LogToFile "OCRConfig: Dynamic provider model refresh attempt completed."
        Else
             OCRUtils.LogToFile "OCRConfig: Existing config seems complete. Skipping initial dynamic model refresh."
        End If
    End If
    
    OCRUtils.LogToFile "OCRConfig: Initialization complete. Current OCR Source: " & GetCurrentPrimaryOCRSourceName() & ", Model: " & GetPrimaryOCRModel()
End Sub

Public Sub LoadConfigurationFromJSON(Optional ByRef wasNewlyCreated As Boolean)
    Dim fso As Object
    Dim fileStream As Object
    Dim jsonString As String
    Dim fullPath As String
    
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "OCRConfig: Entering LoadConfigurationFromJSON"

    fullPath = GetConfigFilePath()
    OCRUtils.LogToFile "OCRConfig: Attempting to load configuration from: " & fullPath

    Set fso = CreateObject("Scripting.FileSystemObject")
    wasNewlyCreated = False ' Initialize

    If fso.FileExists(fullPath) Then
        Set fileStream = fso.OpenTextFile(fullPath, 1) ' 1 = ForReading
        jsonString = fileStream.ReadAll
        fileStream.Close
        OCRUtils.LogToFile "OCRConfig: Read content from existing file: " & fullPath
        
        Set configData = JsonConverter.ParseJson(jsonString)
        
        If configData Is Nothing Then
            OCRUtils.LogToFile "OCRConfig: ERROR - Failed to parse existing config file: " & fullPath & ". Creating default structure."
            CreateDefaultConfigStructure
            wasNewlyCreated = True ' Treat as new if parsing failed
            ' SaveConfigurationToJSON ' Save will be handled by InitializeOCRConfig if needed after refresh
        ElseIf Not configData.Exists("ocr_settings") Then
            OCRUtils.LogToFile "OCRConfig: WARNING - 'ocr_settings' not found in existing config file. Re-initializing with defaults."
            CreateDefaultConfigStructure ' This will create ocr_settings
            wasNewlyCreated = True ' Treat as new if structure was bad
            ' SaveConfigurationToJSON
        Else
            OCRUtils.LogToFile "OCRConfig: Configuration loaded successfully from " & fullPath
            wasNewlyCreated = False
        End If
    Else
        OCRUtils.LogToFile "OCRConfig: WARNING - Config file not found at " & fullPath & ". Creating with default values."
        CreateDefaultConfigStructure
        wasNewlyCreated = True
        ' SaveConfigurationToJSON ' Save will be handled by InitializeOCRConfig after refresh
    End If
    
    ' Ensure essential structures exist even if file was partially valid or just created
    If configData Is Nothing Then CreateDefaultConfigStructure ' Ensure configData is an object
    
    ' Ensure ocr_settings exists
    If Not configData.Exists("ocr_settings") Then
        OCRUtils.LogToFile "OCRConfig: 'ocr_settings' key missing, creating."
        Set configData("ocr_settings") = CreateObject("Scripting.Dictionary")
    End If
    
    ' Ensure current_workflow exists
    If Not configData("ocr_settings").Exists("current_workflow") Then
        OCRUtils.LogToFile "OCRConfig: 'current_workflow' key missing, creating."
        Set configData("ocr_settings")("current_workflow") = CreateDefaultWorkflowConfig()
    End If
    
    ' Ensure providers exists and is a collection (or dictionary if that's how we handle it internally from default)
    If Not configData("ocr_settings").Exists("providers") Then
        OCRUtils.LogToFile "OCRConfig: 'providers' key missing, creating."
        Set configData("ocr_settings")("providers") = CreateDefaultProvidersConfig()
    End If
    
    ' Ensure general_settings exists
    If Not configData("ocr_settings").Exists("general_settings") Then
        OCRUtils.LogToFile "OCRConfig: 'general_settings' key missing, creating."
        Set configData("ocr_settings")("general_settings") = CreateDefaultGeneralSettings()
    End If
    
    OCRUtils.LogToFile "OCRConfig: Exiting LoadConfigurationFromJSON"
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRConfig: CRITICAL ERROR in LoadConfigurationFromJSON: " & Err.Description & " (Err.Number: " & Err.Number & "). Forcing default structure."
    CreateDefaultConfigStructure ' This sets configData to a new default dictionary
    OCRUtils.LogToFile "OCRConfig: Exiting LoadConfigurationFromJSON after error."
End Sub

Private Sub CreateDefaultConfigStructure()
    OCRUtils.LogToFile "OCRConfig: Entering CreateDefaultConfigStructure"
    Set configData = CreateObject("Scripting.Dictionary")
    Set configData("ocr_settings") = CreateObject("Scripting.Dictionary")
    Set configData("ocr_settings")("current_workflow") = CreateDefaultWorkflowConfig()
    Set configData("ocr_settings")("providers") = CreateDefaultProvidersConfig()
    Set configData("ocr_settings")("general_settings") = CreateDefaultGeneralSettings()
    OCRUtils.LogToFile "OCRConfig: Created default config structure in memory."
End Sub

Private Function CreateDefaultWorkflowConfig() As Object
    Dim workflow As Object
    Set workflow = CreateObject("Scripting.Dictionary")
    workflow("primary_ocr_source") = DEFAULT_PRIMARY_OCR_SOURCE_ID
    workflow("ocr_provider") = DEFAULT_PRIMARY_OCR_SOURCE_ID
    workflow("ocr_model") = DEFAULT_PRIMARY_OCR_MODEL
    workflow("docling_transport_id") = DEFAULT_DOCLING_TRANSPORT_ID

    ' Create extraction nested object
    Dim extraction As Object
    Set extraction = CreateObject("Scripting.Dictionary")
    extraction("provider") = DEFAULT_EXTRACTION_PROVIDER_ID
    extraction("model") = DEFAULT_EXTRACTION_MODEL

    ' Create fields nested object
    Dim fields As Object
    Set fields = CreateObject("Scripting.Dictionary")
    fields("From") = True
    fields("To") = True
    fields("LetterRef") = True ' Assuming "Letter Reference" maps to "LetterRef"
    fields("LetterDate") = True
    fields("Subject") = True
    fields("References") = True
    fields("Body") = True
    fields("Summary") = True
    fields("Tags") = True ' Assuming "Topics / Tags" maps to "Tags"
    extraction("fields") = fields

    workflow("extraction") = extraction

    Set CreateDefaultWorkflowConfig = workflow
End Function

Private Function CreateDefaultProvidersConfig() As Object
    Dim providers As Object, provider As Object
    Set providers = CreateObject("Scripting.Dictionary") ' Should be an array in JSON, but Dictionary for VBA ease
    
    ' Ollama Provider
    Set provider = CreateObject("Scripting.Dictionary")
    provider("id") = PID_OLLAMA
    provider("name") = "Ollama"
    provider("type") = "VLM" ' Will be updated by GetProviderType from Prov_Ollama
    provider("api_url") = "http://localhost:11434"
    provider("models") = Array() ' Initialize empty, to be populated by RefreshProviderModels
    Set providers(PID_OLLAMA) = provider
    
    ' OpenRouter Provider (Unified Vision-capable)
    Set provider = CreateObject("Scripting.Dictionary")
    provider("id") = PID_OPENROUTER
    provider("name") = "OpenRouter"
    provider("type") = "VLM" ' Will be updated by GetProviderType from Prov_OpenRouter
    provider("api_url") = "https://openrouter.ai"
    provider("api_key") = "YOUR_OPENROUTER_API_KEY_HERE"
    provider("models") = Array() ' Initialize empty, user configures in JSON or fetched if API allows
    Set providers(PID_OPENROUTER) = provider
    
    ' OpenRouter OCR Provider (Dedicated OCR Service)
    Set provider = CreateObject("Scripting.Dictionary")
    provider("id") = PID_OPENROUTER_OCR
    provider("name") = "OpenRouter OCR"
    provider("type") = "VLM" ' Will be updated
    provider("api_url") = "https://openrouter.ai"
    provider("api_key") = "YOUR_OPENROUTER_API_KEY_HERE"
    ' Note: OpenRouterOCR uses file_compatible_models instead of models
    Set providers(PID_OPENROUTER_OCR) = provider
    
    ' Docling Provider (Unified)
    Set provider = CreateObject("Scripting.Dictionary")
    provider("id") = PID_DOCLING
    provider("name") = "Docling"
    provider("type") = "Docling"
    provider("api_url") = "http://localhost:5001/convert"
    provider("models") = Array() ' No models for Docling - it's just an OCR provider
    Set providers(PID_DOCLING) = provider
    
    ' Docling Custom Provider (Legacy - kept for backward compatibility)
    Set provider = CreateObject("Scripting.Dictionary")
    provider("id") = PID_DOCLING_CUSTOM
    provider("name") = "Docling Custom HTTP"
    provider("type") = "Docling"
    provider("api_url") = "http://localhost:5001/convert"
    provider("models") = Array("text", "markdown", "json")
    Set providers(PID_DOCLING_CUSTOM) = provider
    
    ' Docling Web Provider (Legacy - kept for backward compatibility)
    Set provider = CreateObject("Scripting.Dictionary")
    provider("id") = PID_DOCLING_WEB
    provider("name") = "Docling VBA-Web"
    provider("type") = "Docling"
    provider("api_url") = "http://localhost:5001/convert"
    provider("models") = Array("text", "markdown", "json")
    Set providers(PID_DOCLING_WEB) = provider
    
    ' LM Studio Provider
    Set provider = CreateObject("Scripting.Dictionary")
    provider("id") = PID_LMSTUDIO
    provider("name") = "LM Studio"
    provider("type") = "VLM" ' Will be updated
    provider("api_url") = "http://localhost:1234"
    provider("models") = Array() ' Initialize empty
    Set providers(PID_LMSTUDIO) = provider
    
    Set CreateDefaultProvidersConfig = providers
    OCRUtils.LogToFile "OCRConfig: Created complete default providers structure with all 7 providers."
End Function

' Helper function to add missing providers to existing config
Private Sub AddMissingProvider(ByVal providerID As String, ByVal providerName As String, ByVal providerType As String, ByVal apiURL As String, ByVal models As Variant)
    On Error GoTo ErrorHandler
    
    If configData Is Nothing Then Exit Sub
    If Not configData.Exists("ocr_settings") Then Exit Sub
    If Not configData("ocr_settings").Exists("providers") Then Exit Sub
    
    Dim providers As Object
    Set providers = configData("ocr_settings")("providers")
    
    ' Only add if not already exists
    If Not providers.Exists(providerID) Then
        Dim provider As Object
        Set provider = CreateObject("Scripting.Dictionary")
        provider("id") = providerID
        provider("name") = providerName
        provider("type") = providerType
        provider("api_url") = apiURL
        provider("models") = models
        Set providers(providerID) = provider
        OCRUtils.LogToFile "OCRConfig: Added missing provider: " & providerName & " (" & providerID & ")"
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "OCRConfig: Error adding missing provider " & providerID & ": " & Err.Description
End Sub

Private Function CreateDefaultGeneralSettings() As Object
    OCRUtils.LogToFile "OCRConfig: Entering CreateDefaultGeneralSettings"
    Dim settings As Object
    Set settings = CreateObject("Scripting.Dictionary")
    settings("image_dpi") = 150
    settings("log_file_path") = "logs/VBAOCR.log"
    settings("temp_cleanup_delay_ms") = 2000
    Set CreateDefaultGeneralSettings = settings
    OCRUtils.LogToFile "OCRConfig: Created default general settings structure."
End Function

Public Sub SaveConfigurationToJSON()
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "OCRConfig: Entering SaveConfigurationToJSON"
    Dim fso As Object
    Dim fileStream As Object
    Dim jsonString As String
    Dim fullPath As String
    
    On Error GoTo ErrorHandler
    
    If configData Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: ERROR - Cannot save, configData is Nothing."
        Exit Sub
    End If
    If Not configData.Exists("ocr_settings") Then
        OCRUtils.LogToFile "OCRConfig: ERROR - Cannot save, 'ocr_settings' key is missing."
        Exit Sub
    End If

    fullPath = GetConfigFilePath()
    jsonString = JsonConverter.ConvertToJson(configData, Whitespace:=2) ' Pretty print
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set fileStream = fso.CreateTextFile(fullPath, True) ' True = Overwrite if exists
    fileStream.Write jsonString
    fileStream.Close
    
    OCRUtils.LogToFile "OCRConfig: Configuration saved successfully to " & fullPath
    If Not RibbonCallbacks.gRibbonUI Is Nothing Then RibbonCallbacks.gRibbonUI.Invalidate ' Refresh ribbon
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRConfig: ERROR saving config.json: " & Err.Description
End Sub

Private Function GetConfigFilePath() As String
    On Error Resume Next ' Handle ThisWorkbook being Nothing in some contexts (e.g. unit testing)
    If ThisWorkbook Is Nothing Then
        GetConfigFilePath = CONFIG_FILE_PATH ' Relative path
    Else
        GetConfigFilePath = ThisWorkbook.path & "\" & CONFIG_FILE_PATH
    End If
    If Err.Number <> 0 Then
         GetConfigFilePath = CONFIG_FILE_PATH ' Fallback
    End If
End Function

Public Function IsConfigLoaded() As Boolean
    IsConfigLoaded = Not (configData Is Nothing)
End Function

Public Function GetCurrentWorkflowSettingsObject() As Object
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then
        Set GetCurrentWorkflowSettingsObject = CreateDefaultWorkflowConfig() ' Return a default if load failed
        Exit Function
    End If
    ' Return a copy to prevent direct modification outside of Setters
    Dim copiedWorkflow As Object, Key As Variant
    Set copiedWorkflow = CreateObject("Scripting.Dictionary")
    For Each Key In configData("ocr_settings")("current_workflow").keys
        copiedWorkflow(Key) = configData("ocr_settings")("current_workflow")(Key)
    Next Key
    Set GetCurrentWorkflowSettingsObject = copiedWorkflow
End Function

Public Sub RestoreWorkflowSettingsObject(workflowObject As Object)
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then Exit Sub ' Cannot restore if load failed
    
    Dim Key As Variant
    For Each Key In workflowObject.keys
        If configData("ocr_settings")("current_workflow").Exists(Key) Then
            configData("ocr_settings")("current_workflow")(Key) = workflowObject(Key)
        End If
    Next Key
    OCRUtils.LogToFile "OCRConfig: Workflow settings restored."
    ' SaveConfigurationToJSON ' Optionally save immediately
    If Not RibbonCallbacks.gRibbonUI Is Nothing Then RibbonCallbacks.gRibbonUI.Invalidate ' Refresh ribbon
End Sub


'===============================================================================
' WORKFLOW SETTINGS GETTERS/SETTERS
'===============================================================================

' --- Primary OCR Source ---
Public Function GetCurrentPrimaryOCRSourceID() As String
    GetCurrentPrimaryOCRSourceID = GetWorkflowSetting("primary_ocr_source", DEFAULT_PRIMARY_OCR_SOURCE_ID)
End Function
Public Sub SetCurrentPrimaryOCRSourceID(ByVal Value As String)
    SetWorkflowSetting "primary_ocr_source", Value
End Sub
Public Function GetCurrentPrimaryOCRSourceName() As String
    Dim providerID As String
    providerID = GetCurrentPrimaryOCRSourceID()
    If providerID = PID_OLLAMA Or providerID = PID_OPENROUTER Or providerID = PID_LMSTUDIO Then
        GetCurrentPrimaryOCRSourceName = "VLM (" & GetProviderName(providerID) & ")"
    ElseIf providerID = PID_OPENROUTER_OCR Then
        GetCurrentPrimaryOCRSourceName = "OpenRouter OCR (" & GetProviderName(providerID) & ")"
    ElseIf providerID = PID_DOCLING Or providerID = PID_DOCLING_CUSTOM Or providerID = PID_DOCLING_WEB Then
        GetCurrentPrimaryOCRSourceName = "Docling Server (" & GetProviderName(providerID) & ")"
    Else
        GetCurrentPrimaryOCRSourceName = "Unknown (" & providerID & ")"
    End If
End Function


' --- Extract Fields (DEPRECATED - Removed as extraction is now controlled by ribbon buttons) ---
' GetExtractFields and SetExtractFields functions removed

' --- Primary OCR Model ---
Public Function GetPrimaryOCRModel() As String
    GetPrimaryOCRModel = GetWorkflowSetting("ocr_model", DEFAULT_PRIMARY_OCR_MODEL)
End Function
Public Sub SetPrimaryOCRModel(ByVal Value As String)
    SetWorkflowSetting "ocr_model", Value
End Sub

' --- OCR Provider ID ---
Public Function GetOCRProviderID() As String
    GetOCRProviderID = GetWorkflowSetting("ocr_provider", DEFAULT_PRIMARY_OCR_SOURCE_ID)
End Function

Public Sub SetOCRProviderID(ByVal Value As String)
    SetWorkflowSetting "ocr_provider", Value
End Sub

' --- VLM Provider for Extraction (Legacy for Docling+VLM or specific scenarios) ---
Public Function GetVLMProviderIDForExtraction() As String
    GetVLMProviderIDForExtraction = GetWorkflowSetting("vlm_provider_id_for_extraction_legacy", DEFAULT_VLM_PROVIDER_ID_FOR_EXTRACTION_LEGACY)
End Function
Public Sub SetVLMProviderIDForExtraction(ByVal Value As String)
    SetWorkflowSetting "vlm_provider_id_for_extraction_legacy", Value
End Sub

' --- VLM Model for Extraction (Legacy for Docling+VLM or specific scenarios) ---
Public Function GetVLMModelForExtraction() As String
    GetVLMModelForExtraction = GetWorkflowSetting("vlm_model_for_extraction_legacy", DEFAULT_VLM_MODEL_FOR_EXTRACTION_LEGACY)
End Function
Public Sub SetVLMModelForExtraction(ByVal Value As String)
    SetWorkflowSetting "vlm_model_for_extraction_legacy", Value
End Sub

' --- Docling Transport ---
Public Function GetDoclingTransportID() As String
    GetDoclingTransportID = GetWorkflowSetting("docling_transport_id", DEFAULT_DOCLING_TRANSPORT_ID)
End Function
Public Sub SetDoclingTransportID(ByVal Value As String)
    SetWorkflowSetting "docling_transport_id", Value
End Sub

' --- Extraction Provider ID ---
Public Function GetExtractionProviderID() As String
    GetExtractionProviderID = GetWorkflowSetting("extraction.provider", DEFAULT_EXTRACTION_PROVIDER_ID)
End Function
Public Sub SetExtractionProviderID(ByVal Value As String)
    SetWorkflowSetting "extraction.provider", Value
End Sub

' --- Extraction Model ---
Public Function GetExtractionModel() As String
    GetExtractionModel = GetWorkflowSetting("extraction.model", DEFAULT_EXTRACTION_MODEL)
End Function
Public Sub SetExtractionModel(ByVal Value As String)
    SetWorkflowSetting "extraction.model", Value
End Sub

' --- Field Selection ---
Public Function GetFieldSelectionState(fieldName As String) As Boolean
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then GetFieldSelectionState = True: Exit Function ' Default to true if no config
    
    Dim workflowDict As Object
    Dim fieldSelectionDict As Object
    
    On Error Resume Next
    Set workflowDict = configData("ocr_settings")("current_workflow")
    If Err.Number <> 0 Or workflowDict Is Nothing Then
        GetFieldSelectionState = True ' Default
        Exit Function
    End If
    
    If workflowDict.Exists("extraction") And workflowDict("extraction").Exists("fields") Then
        Set fieldSelectionDict = workflowDict("extraction")("fields")
        If fieldSelectionDict.Exists(fieldName) Then
            GetFieldSelectionState = CBool(fieldSelectionDict(fieldName))
        Else
            GetFieldSelectionState = True ' Default for a new field not yet in config
            fieldSelectionDict(fieldName) = True ' Add new field with default true
        End If
    Else
        ' extraction.fields doesn't exist, create it with all true
        If Not workflowDict.Exists("extraction") Then
            Set workflowDict("extraction") = CreateObject("Scripting.Dictionary")
        End If
        Set fieldSelectionDict = CreateObject("Scripting.Dictionary")
        fieldSelectionDict("From") = True
        fieldSelectionDict("To") = True
        fieldSelectionDict("LetterRef") = True
        fieldSelectionDict("LetterDate") = True
        fieldSelectionDict("Subject") = True
        fieldSelectionDict("References") = True
        fieldSelectionDict("Body") = True
        fieldSelectionDict("Summary") = True
        fieldSelectionDict("Tags") = True
        Set workflowDict("extraction")("fields") = fieldSelectionDict
        GetFieldSelectionState = True ' Default for the requested field
    End If
    On Error GoTo 0
End Function

Public Sub SetFieldSelectionState(fieldName As String, Value As Boolean)
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then Exit Sub
    
    Dim workflowDict As Object
    Dim fieldSelectionDict As Object
    
    On Error Resume Next
    Set workflowDict = configData("ocr_settings")("current_workflow")
    If Err.Number <> 0 Or workflowDict Is Nothing Then Exit Sub
    
    If Not workflowDict.Exists("extraction") Then
        Set workflowDict("extraction") = CreateObject("Scripting.Dictionary")
    End If
    If Not workflowDict("extraction").Exists("fields") Then
        Set workflowDict("extraction")("fields") = CreateObject("Scripting.Dictionary")
    End If
    Set fieldSelectionDict = workflowDict("extraction")("fields")
    fieldSelectionDict(fieldName) = Value
    On Error GoTo 0
    OCRUtils.LogToFile "OCRConfig: Field selection '" & fieldName & "' set to '" & Value & "'"
End Sub

Public Function GetAllFieldSelectionStates() As Object ' Returns a Dictionary
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then
        ' Return a default dictionary if config is not loaded
        Dim defaultFields As Object
        Set defaultFields = CreateObject("Scripting.Dictionary")
        defaultFields("From") = True
        defaultFields("To") = True
        defaultFields("LetterRef") = True
        defaultFields("LetterDate") = True
        defaultFields("Subject") = True
        defaultFields("References") = True
        defaultFields("Body") = True
        defaultFields("Summary") = True
        defaultFields("Tags") = True
        Set GetAllFieldSelectionStates = defaultFields
        Exit Function
    End If

    Dim workflowDict As Object
    Dim fieldSelectionDict As Object
    Dim copiedFields As Object
    Dim Key As Variant
    
    On Error Resume Next
    Set workflowDict = configData("ocr_settings")("current_workflow")
    If Err.Number <> 0 Or workflowDict Is Nothing Or Not workflowDict.Exists("extraction") Or Not workflowDict("extraction").Exists("fields") Then
        ' If not found, create and return default
        Dim tempFields As Object
        Set tempFields = CreateObject("Scripting.Dictionary")
        tempFields("From") = True
        tempFields("To") = True
        tempFields("LetterRef") = True
        tempFields("LetterDate") = True
        tempFields("Subject") = True
        tempFields("References") = True
        tempFields("Body") = True
        tempFields("Summary") = True
        tempFields("Tags") = True
        If Not workflowDict Is Nothing Then
            If Not workflowDict.Exists("extraction") Then Set workflowDict("extraction") = CreateObject("Scripting.Dictionary")
            Set workflowDict("extraction")("fields") = tempFields
        End If
        Set GetAllFieldSelectionStates = tempFields
        Exit Function
    End If

    Set fieldSelectionDict = workflowDict("extraction")("fields")
    Set copiedFields = CreateObject("Scripting.Dictionary")
    For Each Key In fieldSelectionDict.keys
        copiedFields(Key) = fieldSelectionDict(Key)
    Next Key
    Set GetAllFieldSelectionStates = copiedFields
    On Error GoTo 0
End Function

' --- Helper for Workflow Settings ---
Public Function GetWorkflowSetting(KeyName As String, defaultValue As Variant) As Variant
    If configData Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: GetWorkflowSetting - configData is Nothing, attempting to load."
        LoadConfigurationFromJSON
        If configData Is Nothing Then ' Still nothing after load attempt
            OCRUtils.LogToFile "OCRConfig: GetWorkflowSetting - configData is STILL Nothing after load. Returning default for " & KeyName
            GetWorkflowSetting = defaultValue
            Exit Function
        End If
    End If

    Dim workflowDict As Object
    On Error Resume Next ' Check if ocr_settings and current_workflow exist
    Set workflowDict = configData("ocr_settings")("current_workflow")
    If Err.Number <> 0 Or workflowDict Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: GetWorkflowSetting - ocr_settings.current_workflow is not accessible. Error: " & Err.Description & ". Returning default for " & KeyName
        GetWorkflowSetting = defaultValue
        Err.Clear
        Exit Function
    End If
    On Error GoTo 0 ' Reset error handling

    ' Handle nested keys like "extraction.provider"
    If InStr(KeyName, ".") > 0 Then
        Dim keyParts() As String
        keyParts = Split(KeyName, ".")

        Dim currentDict As Object
        Set currentDict = workflowDict

        Dim i As Integer
        For i = 0 To UBound(keyParts) - 1
            On Error Resume Next
            If currentDict.Exists(keyParts(i)) Then
                Set currentDict = currentDict(keyParts(i))
                If Err.Number <> 0 Or currentDict Is Nothing Then
                    GetWorkflowSetting = defaultValue
                    SetWorkflowSetting KeyName, defaultValue ' Create the nested structure
                    Exit Function
                End If
            Else
                GetWorkflowSetting = defaultValue
                SetWorkflowSetting KeyName, defaultValue ' Create the nested structure
                Exit Function
            End If
            On Error GoTo 0
        Next i

        ' Get the final value
        Dim finalKey As String
        finalKey = keyParts(UBound(keyParts))
        On Error Resume Next
        If currentDict.Exists(finalKey) Then
            GetWorkflowSetting = currentDict(finalKey)
        Else
            GetWorkflowSetting = defaultValue
            SetWorkflowSetting KeyName, defaultValue ' Create the missing key
        End If
        On Error GoTo 0
    Else
        ' Handle simple keys (no nesting)
        On Error Resume Next
        If workflowDict.Exists(KeyName) Then
            GetWorkflowSetting = workflowDict(KeyName)
            If Err.Number <> 0 Then
                OCRUtils.LogToFile "OCRConfig: GetWorkflowSetting - Error accessing key '" & KeyName & "'. Error: " & Err.Description & ". Returning default."
                GetWorkflowSetting = defaultValue
                Err.Clear
            End If
        Else
            GetWorkflowSetting = defaultValue
            workflowDict(KeyName) = defaultValue ' Add if missing
            OCRUtils.LogToFile "OCRConfig: GetWorkflowSetting - Workflow key '" & KeyName & "' not found, using default and adding to config."
        End If
        On Error GoTo 0
    End If
End Function

Public Sub SetWorkflowSetting(KeyName As String, Value As Variant)
    If configData Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: SetWorkflowSetting - configData is Nothing, attempting to load."
        LoadConfigurationFromJSON
        If configData Is Nothing Then ' Still nothing after load attempt
            OCRUtils.LogToFile "OCRConfig: SetWorkflowSetting - configData is STILL Nothing. Cannot set " & KeyName
            Exit Sub
        End If
    End If

    Dim workflowDict As Object
    On Error Resume Next
    Set workflowDict = configData("ocr_settings")("current_workflow")
    If Err.Number <> 0 Or workflowDict Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: SetWorkflowSetting - ocr_settings.current_workflow is not accessible. Cannot set " & KeyName & ". Error: " & Err.Description
        Err.Clear
        Exit Sub
    End If
    On Error GoTo 0

    ' Handle nested keys like "extraction.provider"
    If InStr(KeyName, ".") > 0 Then
        Dim keyParts() As String
        keyParts = Split(KeyName, ".")

        Dim currentDict As Object
        Set currentDict = workflowDict

        Dim i As Integer
        For i = 0 To UBound(keyParts) - 1
            If Not currentDict.Exists(keyParts(i)) Then
                Set currentDict(keyParts(i)) = CreateObject("Scripting.Dictionary")
            End If
            Set currentDict = currentDict(keyParts(i))
        Next i

        ' Set the final value
        Dim finalKey As String
        finalKey = keyParts(UBound(keyParts))
        currentDict(finalKey) = Value
    Else
        ' Handle simple keys (no nesting)
        workflowDict(KeyName) = Value
    End If

    OCRUtils.LogToFile "OCRConfig: Workflow setting '" & KeyName & "' set to '" & Value & "'"
    ' Automatically save configuration after setting workflow values
    SaveConfigurationToJSON
End Sub


'===============================================================================
' PROVIDER MODULE MAPPING (Internal - not stored in config)
'===============================================================================
Private Function GetProviderModuleName(providerID As String) As String
    Select Case providerID
        Case PID_OLLAMA
            GetProviderModuleName = "Prov_Ollama"
        Case PID_OPENROUTER, PID_OPENROUTER_OCR
            GetProviderModuleName = "Prov_OpenRouter"
        Case PID_LMSTUDIO
            GetProviderModuleName = "Prov_LMStudio"
        Case PID_DOCLING_CUSTOM
            GetProviderModuleName = "Prov_DoclingOCR"
        Case PID_DOCLING_WEB
            GetProviderModuleName = "Prov_DoclingOCRWeb"
        Case Else
            GetProviderModuleName = ""
    End Select
End Function

'===============================================================================
' PROVIDER DETAILS GETTERS (from "providers" array in config)
'===============================================================================

Public Function GetProviderDetails(providerID As String) As Object ' Returns a Dictionary
    If configData Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: GetProviderDetails - configData is Nothing, attempting to load."
        LoadConfigurationFromJSON
        If configData Is Nothing Then
            OCRUtils.LogToFile "OCRConfig: GetProviderDetails - configData is STILL Nothing. Returning Nothing for " & providerID
            Set GetProviderDetails = Nothing
            Exit Function
        End If
    End If
    
    Dim providersConfig As Object
    On Error Resume Next
    Set providersConfig = configData("ocr_settings")("providers")
    If Err.Number <> 0 Or providersConfig Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: GetProviderDetails - ocr_settings.providers is not accessible. Error: " & Err.Description & ". Returning Nothing for " & providerID
        Set GetProviderDetails = Nothing
        Err.Clear
        Exit Function
    End If
    On Error GoTo 0

    ' The providers config in your JSON file is stored as a Dictionary
    If TypeName(providersConfig) = "Dictionary" Then
        If providersConfig.Exists(providerID) Then
            Set GetProviderDetails = providersConfig(providerID)
            OCRUtils.LogToFile "OCRConfig: GetProviderDetails - Found provider '" & providerID & "' in config."
            Exit Function
        End If
    ElseIf TypeName(providersConfig) = "Collection" Then
        ' Handle Collection format (if JSON array is used)
        Dim providerItem As Object
        For Each providerItem In providersConfig
            If TypeName(providerItem) = "Dictionary" Then
                If providerItem.Exists("id") And providerItem("id") = providerID Then
                    Set GetProviderDetails = providerItem
                    OCRUtils.LogToFile "OCRConfig: GetProviderDetails - Found provider '" & providerID & "' in collection."
                    Exit Function
                End If
            End If
        Next providerItem
    End If
    
    OCRUtils.LogToFile "OCRConfig: GetProviderDetails - Provider ID '" & providerID & "' not found in config."
    Set GetProviderDetails = Nothing
End Function

Public Function GetProviderAPIURL(providerID As String) As String
    Dim provider As Object
    Set provider = GetProviderDetails(providerID)
    If Not provider Is Nothing Then GetProviderAPIURL = provider("api_url") Else GetProviderAPIURL = ""
End Function

Public Function GetProviderAPIKey(providerID As String) As String
    Dim provider As Object
    Set provider = GetProviderDetails(providerID)
    If Not provider Is Nothing Then
        If provider.Exists("api_key") Then GetProviderAPIKey = provider("api_key") Else GetProviderAPIKey = ""
    Else
        GetProviderAPIKey = ""
    End If
End Function

Public Function GetProviderDefaultModel(providerID As String) As String
    ' Since we no longer use default_model, return the first available model or empty string
    Dim models As Variant
    models = GetProviderModels(providerID)
    If IsArray(models) And UBound(models) >= 0 Then
        ' Return first model only if it's not a placeholder message
        Dim firstModel As String
        firstModel = models(0)
        If InStr(firstModel, "- ") = 1 And InStr(firstModel, " -") > 0 Then
            ' This is a placeholder message like "- No models available -"
            GetProviderDefaultModel = ""
        Else
            GetProviderDefaultModel = firstModel
        End If
    Else
        GetProviderDefaultModel = ""
    End If
End Function

Public Function GetProviderModels(providerID As String) As Variant ' Returns VBA Array
    ' Check cache first
    Dim cacheResult As Variant
    cacheResult = GetCachedModelList(providerID)
    If cacheResult(1) Then ' Is cached
        GetProviderModels = cacheResult(0)
        Exit Function
    End If
    
    ' Not cached, get from config
    Dim provider As Object
    Set provider = GetProviderDetails(providerID)
    If Not provider Is Nothing And provider.Exists("models") Then
        ' JSON array becomes a Collection in VBA-JSON. Convert to VBA array for dropdowns.
        If TypeName(provider("models")) = "Collection" Then
            Dim coll As Collection, item As Variant, i As Long
            Set coll = provider("models")
            If coll.Count > 0 Then
                Dim arr() As String
                ReDim arr(0 To coll.Count - 1)
                For i = 0 To coll.Count - 1
                    arr(i) = CStr(coll(i + 1))
                Next i
                GetProviderModels = arr
            Else
                GetProviderModels = Array() ' Empty VBA array
            End If
        ElseIf IsArray(provider("models")) Then ' Already a VBA array (e.g. from default config)
             GetProviderModels = provider("models")
        Else
            GetProviderModels = Array()
        End If
    Else
        GetProviderModels = Array() ' Empty VBA array
    End If
End Function

Public Function GetProviderName(providerID As String) As String
    Dim provider As Object
    Set provider = GetProviderDetails(providerID)
    If Not provider Is Nothing Then GetProviderName = provider("name") Else GetProviderName = providerID
End Function

' Specific getters for Docling params (example)
Public Function GetDoclingParam(providerID As String, paramName As String, defaultValue As Variant) As Variant
    Dim provider As Object
    Set provider = GetProviderDetails(providerID)
    If Not provider Is Nothing And provider.Exists(paramName) Then
        GetDoclingParam = provider(paramName)
    Else
        GetDoclingParam = defaultValue
    End If
End Function

' Generic provider detail getter
Public Function GetProviderDetail(providerID As String, detailName As String, defaultValue As Variant) As Variant
    Dim provider As Object
    Set provider = GetProviderDetails(providerID)
    If Not provider Is Nothing And provider.Exists(detailName) Then
        GetProviderDetail = provider(detailName)
    Else
        GetProviderDetail = defaultValue
    End If
End Function

' Generic provider detail setter
Public Sub SetProviderDetail(providerID As String, detailName As String, Value As Variant)
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then Exit Sub
    
    On Error Resume Next
    Dim provider As Object
    Set provider = configData("ocr_settings")("providers")(providerID)
    If Not provider Is Nothing Then
        provider(detailName) = Value
        SaveConfigurationToJSON
    End If
    On Error GoTo 0
End Sub

' OpenRouter PDF Engine specific getter
Public Function GetOpenRouterPDFEngine() As String
    GetOpenRouterPDFEngine = GetProviderDetail(PID_OPENROUTER_OCR, "pdf_engine", "pdf-text")
End Function

' OpenRouter PDF Engine specific setter
Public Sub SetOpenRouterPDFEngine(engine As String)
    SetProviderDetail PID_OPENROUTER_OCR, "pdf_engine", engine
End Sub

' OpenRouter PDF Model specific getter
Public Function GetOpenRouterPDFModel() As String
    GetOpenRouterPDFModel = GetProviderDetail(PID_OPENROUTER_OCR, "pdf_model", "")
End Function

' OpenRouter PDF Model specific setter
Public Sub SetOpenRouterPDFModel(model As String)
    SetProviderDetail PID_OPENROUTER_OCR, "pdf_model", model
End Sub

' Set file-compatible models for OpenRouter OCR
Public Sub SetOpenRouterFileCompatibleModels(ByVal models As Variant)
    SetProviderDetail PID_OPENROUTER_OCR, "file_compatible_models", models
End Sub

' Add a model to file-compatible models list if not already present
Public Sub AddToOpenRouterFileCompatibleModels(ByVal model As String)
    If model = "" Or model = "No file-compatible models available" Then Exit Sub
    
    Dim currentModels As Variant
    currentModels = GetOpenRouterFileCompatibleModels()
    
    ' Check if model already exists
    If IsArray(currentModels) Then
        Dim existingModel As Variant
        For Each existingModel In currentModels
            If CStr(existingModel) = model Then Exit Sub ' Already exists
        Next existingModel
        
        ' Add to existing array
        Dim newArray() As String
        ReDim newArray(0 To UBound(currentModels) + 1)
        Dim i As Integer
        For i = 0 To UBound(currentModels)
            newArray(i) = CStr(currentModels(i))
        Next i
        newArray(UBound(newArray)) = model
        SetOpenRouterFileCompatibleModels newArray
    Else
        ' Create new array with just this model
        SetOpenRouterFileCompatibleModels Array(model)
    End If
End Sub

' Get file-compatible models for OpenRouter
Public Function GetOpenRouterFileCompatibleModels() As Variant
    Dim provider As Object
    Set provider = GetProviderDetails(PID_OPENROUTER_OCR)
    
    If Not provider Is Nothing Then
        If provider.Exists("file_compatible_models") Then
            Dim modelsObj As Object
            Set modelsObj = provider("file_compatible_models")
            
            If TypeName(modelsObj) = "Collection" Then
                ' Convert Collection to array
                Dim arr() As String
                Dim i As Long
                If modelsObj.Count > 0 Then
                    ReDim arr(0 To modelsObj.Count - 1)
                    For i = 0 To modelsObj.Count - 1
                        arr(i) = CStr(modelsObj(i + 1))
                    Next i
                    GetOpenRouterFileCompatibleModels = arr
                Else
                    GetOpenRouterFileCompatibleModels = Array()
                End If
            ElseIf IsArray(modelsObj) Then
                GetOpenRouterFileCompatibleModels = modelsObj
            Else
                GetOpenRouterFileCompatibleModels = Array()
            End If
        Else
            ' Fallback: return all models if file_compatible_models not defined
            GetOpenRouterFileCompatibleModels = GetProviderModels(PID_OPENROUTER_OCR)
        End If
    Else
        GetOpenRouterFileCompatibleModels = Array()
    End If
End Function


'===============================================================================
' GENERAL SETTINGS GETTERS
'===============================================================================
Public Function GetImageDPI() As Long
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then GetImageDPI = 150: Exit Function
    On Error Resume Next
    GetImageDPI = CLng(configData("ocr_settings")("general_settings")("image_dpi"))
    If Err.Number <> 0 Then GetImageDPI = 150
End Function

Public Function GetLogFilePath() As String
    If configData Is Nothing Then LoadConfigurationFromJSON
    If configData Is Nothing Then GetLogFilePath = "logs/VBAOCR.log": Exit Function
    On Error Resume Next
    GetLogFilePath = CStr(configData("ocr_settings")("general_settings")("log_file_path"))
    If Err.Number <> 0 Then GetLogFilePath = "logs/VBAOCR.log"
End Function

'===============================================================================
' LEGACY & QUICK SETUP (To be refactored or removed if fully config-driven)
'===============================================================================
' These constants are now effectively superseded by config.json but kept for reference
' Public Const OLLAMA_API_URL_CONST As String = "http://localhost:11434/api/generate"
' Public Const OLLAMA_MODEL_CONST As String = "qwen2.5vl:7b"
' Public Const OPENROUTER_API_URL_CONST As String = "https://openrouter.ai/api/v1/chat/completions"
' Public Const OPENROUTER_VISION_MODEL_CONST As String = "qwen/qwen2.5-vl-72b-instruct:free"
' Public Const OPENROUTER_TEXT_MODEL_CONST As String = "google/gemma-3-27b-it:free"
' Public Const OPENROUTER_API_KEY_CONST As String = "YOUR_OPENROUTER_API_KEY_HERE" ' Placeholder

' --- Legacy State Variables (can be removed if ribbon fully drives config) ---
' Private CURRENT_API_PROVIDER_ENUM As APIProviderType ' Replaced by string IDs
' Private OPENROUTER_MODE_ENUM As OpenRouterMode ' Replaced by provider IDs like OpenRouterVision/Text

' --- Legacy Provider Management (To be refactored) ---
Public Sub SetCurrentAPIProviderEnum(provider As APIProviderType)
    ' Updated to use new provider IDs
    Select Case provider
        Case OCRTypes.OllamaProvider
            SetCurrentPrimaryOCRSourceID PID_OLLAMA
            SetPrimaryOCRModel GetProviderDefaultModel(PID_OLLAMA) ' Set primary model
            ' SetVLMProviderIDForExtraction PID_OLLAMA ' This might be redundant if primary handles it
        Case OCRTypes.OpenRouterProvider
            ' Default to unified OpenRouter provider for vision-capable models
            SetCurrentPrimaryOCRSourceID PID_OPENROUTER
            SetPrimaryOCRModel GetProviderDefaultModel(PID_OPENROUTER) ' Set primary model
            ' SetVLMProviderIDForExtraction PID_OPENROUTER ' Redundant
        Case OCRTypes.DoclingProvider
            SetCurrentPrimaryOCRSourceID PID_DOCLING_CUSTOM
            SetDoclingTransportID PID_DOCLING_CUSTOM
        Case OCRTypes.DoclingWebProvider
            SetCurrentPrimaryOCRSourceID PID_DOCLING_WEB
            SetDoclingTransportID PID_DOCLING_WEB
    End Select
    OCRUtils.LogToFile "OCRConfig: SetCurrentAPIProviderEnum called with " & provider
End Sub

Public Function GetCurrentAPIProviderEnum() As APIProviderType
    ' Map current string ID back to Enum for legacy compatibility
    Dim primarySourceID As String
    primarySourceID = GetCurrentPrimaryOCRSourceID()
    
    If primarySourceID = PID_OLLAMA Then
        GetCurrentAPIProviderEnum = OCRTypes.OllamaProvider
    ElseIf primarySourceID = PID_OPENROUTER Or primarySourceID = PID_OPENROUTER_OCR Then
        GetCurrentAPIProviderEnum = OCRTypes.OpenRouterProvider
    ElseIf primarySourceID = PID_DOCLING_CUSTOM Then
        GetCurrentAPIProviderEnum = OCRTypes.DoclingProvider
    ElseIf primarySourceID = PID_DOCLING_WEB Then
        GetCurrentAPIProviderEnum = OCRTypes.DoclingWebProvider
    ElseIf primarySourceID = PID_LMSTUDIO Then
        GetCurrentAPIProviderEnum = OCRTypes.LMStudioProvider
    Else
        GetCurrentAPIProviderEnum = OCRTypes.OllamaProvider ' Default fallback
    End If
End Function

Public Sub SetOpenRouterModeEnum(mode As OpenRouterMode)
    ' Updated to use new provider IDs
    If mode = OCRTypes.VisionMode Then
        ' Use unified OpenRouter provider for vision-capable models
        SetCurrentPrimaryOCRSourceID PID_OPENROUTER
        SetVLMProviderIDForExtraction PID_OPENROUTER
    Else ' TextMode
        ' Use dedicated OCR provider for text-only processing
        SetCurrentPrimaryOCRSourceID PID_OPENROUTER_OCR
        SetVLMProviderIDForExtraction PID_OPENROUTER_OCR
    End If
    OCRUtils.LogToFile "OCRConfig: SetOpenRouterModeEnum called with " & mode
End Sub

Public Function GetOpenRouterModeEnum() As OpenRouterMode
    Dim primarySourceID As String
    Dim vlmProviderID As String
    primarySourceID = GetCurrentPrimaryOCRSourceID()
    vlmProviderID = GetVLMProviderIDForExtraction()
    
    If primarySourceID = PID_OPENROUTER Or vlmProviderID = PID_OPENROUTER Then
        GetOpenRouterModeEnum = OCRTypes.VisionMode
    ElseIf primarySourceID = PID_OPENROUTER_OCR Or vlmProviderID = PID_OPENROUTER_OCR Then
        GetOpenRouterModeEnum = OCRTypes.TextMode
    Else ' Default to VisionMode for unified provider
        GetOpenRouterModeEnum = OCRTypes.VisionMode
    End If
End Function

' Quick Setup Functions (Updated to use new config setters)
Public Sub ConfigureForOllama()
    SetCurrentPrimaryOCRSourceID PID_OLLAMA
    SetPrimaryOCRModel GetProviderDefaultModel(PID_OLLAMA)
    ' Extraction fields setting removed - controlled by ribbon buttons
    ' SetVLMProviderIDForExtraction PID_OLLAMA ' Not needed if primary handles extraction
    SaveConfigurationToJSON
End Sub

Public Sub ConfigureForOpenRouter()
    SetCurrentPrimaryOCRSourceID PID_OPENROUTER
    SetPrimaryOCRModel GetProviderDefaultModel(PID_OPENROUTER)
    ' Extraction fields setting removed - controlled by ribbon buttons
    ' SetVLMProviderIDForExtraction PID_OPENROUTER ' Not needed
    SaveConfigurationToJSON
End Sub

Public Sub ConfigureForOpenRouterOCR()
    SetCurrentPrimaryOCRSourceID PID_OPENROUTER_OCR
    SetPrimaryOCRModel GetProviderDefaultModel(PID_OPENROUTER_OCR)
    ' Extraction fields setting removed - controlled by ribbon buttons
    ' SetVLMProviderIDForExtraction PID_OPENROUTER_OCR ' Not needed
    SaveConfigurationToJSON
End Sub

Public Sub ConfigureForDocling() ' Docling Custom HTTP
    SetCurrentPrimaryOCRSourceID PID_DOCLING_CUSTOM
    SetDoclingTransportID PID_DOCLING_CUSTOM
    ' ExtractFields and VLM for extraction settings remain as per user's choice
    SaveConfigurationToJSON
End Sub

Public Sub ConfigureForDoclingWeb()
    SetCurrentPrimaryOCRSourceID PID_DOCLING_WEB
    SetDoclingTransportID PID_DOCLING_WEB
    SaveConfigurationToJSON
End Sub


'===============================================================================
' PROVIDER STATUS CACHING
'===============================================================================

' Initialize cache dictionaries if needed
Private Sub InitializeCacheIfNeeded()
    If m_ProviderStatusCache Is Nothing Then
        Set m_ProviderStatusCache = CreateObject("Scripting.Dictionary")
    End If
    If m_LastStatusCheck Is Nothing Then
        Set m_LastStatusCheck = CreateObject("Scripting.Dictionary")
    End If
    If m_ModelListCache Is Nothing Then
        Set m_ModelListCache = CreateObject("Scripting.Dictionary")
    End If
    If m_LastModelCheck Is Nothing Then
        Set m_LastModelCheck = CreateObject("Scripting.Dictionary")
    End If
End Sub

' Check if cached provider status is still valid
Private Function IsCacheValid(providerID As String, cacheType As String) As Boolean
    InitializeCacheIfNeeded
    
    Dim lastCheck As Object
    Select Case cacheType
        Case "status"
            Set lastCheck = m_LastStatusCheck
        Case "models"
            Set lastCheck = m_LastModelCheck
        Case Else
            IsCacheValid = False
            Exit Function
    End Select
    
    If Not lastCheck.Exists(providerID) Then
        IsCacheValid = False
        Exit Function
    End If
    
    Dim secondsElapsed As Long
    secondsElapsed = DateDiff("s", lastCheck(providerID), Now)
    IsCacheValid = (secondsElapsed < CACHE_LIFETIME_SECONDS)
End Function

' Get cached provider status if available and valid
Public Function GetCachedProviderStatus(providerID As String) As Variant
    InitializeCacheIfNeeded
    
    ' Return array: (0) = isAvailable, (1) = isCached
    Dim Result(0 To 1) As Variant
    Result(1) = False ' Assume not cached
    
    If IsCacheValid(providerID, "status") And m_ProviderStatusCache.Exists(providerID) Then
        Result(0) = m_ProviderStatusCache(providerID)
        Result(1) = True
        OCRUtils.LogToFile "OCRConfig: Using cached status for " & providerID & " = " & Result(0)
    Else
        ' No valid cache, caller must check provider
        Result(0) = False
        Result(1) = False
    End If
    
    GetCachedProviderStatus = Result
End Function

' Update provider status cache
Public Sub UpdateProviderStatusCache(providerID As String, isAvailable As Boolean)
    InitializeCacheIfNeeded
    
    m_ProviderStatusCache(providerID) = isAvailable
    m_LastStatusCheck(providerID) = Now
    OCRUtils.LogToFile "OCRConfig: Updated cache for " & providerID & " = " & isAvailable
End Sub

' Get cached model list if available and valid
Public Function GetCachedModelList(providerID As String) As Variant
    InitializeCacheIfNeeded
    
    ' Return array: (0) = model list array, (1) = isCached
    Dim Result(0 To 1) As Variant
    Result(1) = False ' Assume not cached
    
    If IsCacheValid(providerID, "models") And m_ModelListCache.Exists(providerID) Then
        Result(0) = m_ModelListCache(providerID)
        Result(1) = True
        OCRUtils.LogToFile "OCRConfig: Using cached model list for " & providerID
    Else
        ' No valid cache
        Result(0) = Array()
        Result(1) = False
    End If
    
    GetCachedModelList = Result
End Function

' Update model list cache
Public Sub UpdateModelListCache(providerID As String, models As Variant)
    InitializeCacheIfNeeded
    
    m_ModelListCache(providerID) = models
    m_LastModelCheck(providerID) = Now
    OCRUtils.LogToFile "OCRConfig: Updated model cache for " & providerID
End Sub

' Clear all caches (useful for refresh operations)
Public Sub ClearAllCaches()
    InitializeCacheIfNeeded
    
    m_ProviderStatusCache.RemoveAll
    m_LastStatusCheck.RemoveAll
    m_ModelListCache.RemoveAll
    m_LastModelCheck.RemoveAll
    OCRUtils.LogToFile "OCRConfig: Cleared all provider and model caches"
End Sub

' Clear cache for specific provider
Public Sub ClearProviderCache(providerID As String)
    InitializeCacheIfNeeded
    
    If m_ProviderStatusCache.Exists(providerID) Then m_ProviderStatusCache.Remove providerID
    If m_LastStatusCheck.Exists(providerID) Then m_LastStatusCheck.Remove providerID
    If m_ModelListCache.Exists(providerID) Then m_ModelListCache.Remove providerID
    If m_LastModelCheck.Exists(providerID) Then m_LastModelCheck.Remove providerID
    OCRUtils.LogToFile "OCRConfig: Cleared cache for provider " & providerID
End Sub

'===============================================================================
' GENERIC PROVIDER MODEL REFRESH
'===============================================================================
Public Sub RefreshProviderModels(providerID As String)
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "OCRConfig: RefreshProviderModels - STARTING for providerID: " & providerID

    ' Validate configuration state first
    If configData Is Nothing Then
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: configData is Nothing. Initializing."
        InitializeOCRConfig
        If configData Is Nothing Then
            OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: CRITICAL - configData still Nothing. Aborting update for " & providerID
            Exit Sub
        End If
    End If

    Dim providerDetails As Object
    Set providerDetails = GetProviderDetails(providerID)
    If providerDetails Is Nothing Then
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: No details found for providerID: " & providerID & ". Aborting."
        Exit Sub
    End If

    ' Get module name from internal mapping instead of config
    Dim providerModule As String
    providerModule = GetProviderModuleName(providerID)
    If providerModule = "" Then
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: No module mapping found for provider " & providerID & ". Aborting."
        Exit Sub
    End If
    
    Dim providerType As String
    On Error Resume Next
    providerType = providerDetails("type") ' Initial type from config
    Err.Clear
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Provider " & providerID & " has module: '" & providerModule & "', initial type: '" & providerType & "'"

    ' Dynamically call the provider's GetProviderType if available, otherwise use config
    Dim dynamicProviderType As String
    On Error Resume Next
    dynamicProviderType = Application.Run(providerModule & ".GetProviderType")
    If Err.Number = 0 And dynamicProviderType <> "" Then
        providerType = dynamicProviderType
        providerDetails("type") = providerType ' Update config with actual type
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Dynamically fetched type for " & providerID & ": " & providerType
    Else
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Could not fetch dynamic type for " & providerID & ". Error: " & Err.Description & ". Using configured type: " & providerType
        Err.Clear
        
        ' For known local providers, override the type
        If providerID = PID_OLLAMA Or providerID = PID_LMSTUDIO Then
            providerType = "local"
            OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Overriding type to 'local' for known provider: " & providerID
        End If
    End If
    On Error GoTo ErrorHandler

    ' Only refresh models for "local" or "cloud_api" types that support dynamic model fetching
    If LCase(providerType) <> "local" And LCase(providerType) <> "cloud_api" Then
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Provider " & providerID & " is of type '" & providerType & "'. Skipping dynamic model refresh."
        Exit Sub
    End If
    
    ' Check if provider is available before fetching models
    Dim isAvailable As Boolean
    On Error Resume Next
    isAvailable = Application.Run(providerModule & ".IsProviderAvailable")
    If Err.Number <> 0 Then
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Error calling IsProviderAvailable for " & providerModule & ". Assuming not available. Error: " & Err.Description
        isAvailable = False
        Err.Clear
    End If
    On Error GoTo ErrorHandler

    Dim previousModels As Variant
    Dim previousDefault As String
    Dim hasChanges As Boolean
    hasChanges = False
    
    previousModels = GetProviderModels(providerID) ' Uses the existing config
    previousDefault = GetProviderDefaultModel(providerID) ' Uses the existing config
    Dim availableModelNames As Collection
    Dim newModelsListForConfig As Collection
    Set newModelsListForConfig = New Collection
    Dim placeholderMessage As String
    
    If isAvailable Then
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Provider " & providerID & " is available. Fetching models..."
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Calling " & providerModule & ".GetAvailableModels"
        On Error Resume Next
        Set availableModelNames = Application.Run(providerModule & ".GetAvailableModels")
        If Err.Number <> 0 Then
            OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Error calling GetAvailableModels for " & providerModule & ". Error: " & Err.Description & " (Error Number: " & Err.Number & ")"
            Set availableModelNames = Nothing
            Err.Clear
        Else
            OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Successfully called GetAvailableModels for " & providerModule
        End If
        On Error GoTo ErrorHandler
        
        placeholderMessage = "- No models available -"
        If providerType = "local" Then placeholderMessage = "- No models running -"

        If Not availableModelNames Is Nothing And availableModelNames.Count > 0 Then
            Dim modelName As Variant
            For Each modelName In availableModelNames
                newModelsListForConfig.Add CStr(modelName)
            Next modelName
            OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Found " & availableModelNames.Count & " models for " & providerID
        Else
            newModelsListForConfig.Add placeholderMessage
            OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: No models found for " & providerID & ", using placeholder"
        End If
    Else
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Provider " & providerID & " is NOT available. Using placeholder."
        placeholderMessage = "- Provider not available -"
        newModelsListForConfig.Add placeholderMessage
    End If

    ' Update provider's entry in configData - handle OpenRouterOCR specially
    If providerID = PID_OPENROUTER_OCR Then
        ' For OpenRouterOCR, do NOT update file_compatible_models automatically
        ' The file_compatible_models should only be managed manually via config file
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Skipping automatic model update for " & providerID & " - file_compatible_models managed via config"
        ' Do NOT set the "models" field for OpenRouterOCR
        ' Do NOT modify file_compatible_models - preserve what's in config
    Else
        ' For other providers, update models as usual
        providerDetails("models") = CollectionToSimpleArray(newModelsListForConfig) ' Store as simple array for JSON
        
        If Not ArraysEqual(previousModels, CollectionToSimpleArray(newModelsListForConfig)) Then
            hasChanges = True
            OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Model list changed for " & providerID
        End If
    End If
    
    ' We don't update workflow models automatically - user must explicitly select them
    ' This ensures no automatic overwriting of user choices

    If hasChanges Then
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: Changes detected for " & providerID & ". Saving config."
        SaveConfigurationToJSON
    Else
        OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: No changes for " & providerID & "."
    End If
    
    ' Update model cache with fresh data
    UpdateModelListCache providerID, CollectionToSimpleArray(newModelsListForConfig)
    
    OCRUtils.LogToFile "OCRConfig.RefreshProviderModels: COMPLETED for " & providerID
    Exit Sub

ErrorHandler:
    OCRUtils.LogToFile "OCRConfig: ERROR in RefreshProviderModels for " & providerID & ": " & Err.Description
End Sub

' Helper to convert Collection to simple VBA Array for JSON serialization
Private Function CollectionToSimpleArray(coll As Collection) As Variant
    If coll Is Nothing Or coll.Count = 0 Then
        CollectionToSimpleArray = Array()
        Exit Function
    End If
    
    Dim arr() As String
    ReDim arr(0 To coll.Count - 1)
    Dim i As Long
    For i = 1 To coll.Count
        arr(i - 1) = CStr(coll(i))
    Next i
    CollectionToSimpleArray = arr
End Function

Private Function ArraysEqual(arr1 As Variant, arr2 As Variant) As Boolean
    On Error GoTo ErrorHandler
    ' Default to False
    ArraysEqual = False

    ' Check if both are arrays
    If Not IsArray(arr1) Or Not IsArray(arr2) Then
        ' If one is an array and the other isn't, they are not equal
        ' If neither is an array, but both are, for example, Empty, they could be considered equal
        ' For simplicity here, if they are not both arrays, consider them not equal for this context.
        If IsArray(arr1) <> IsArray(arr2) Then Exit Function
        ' If both are not arrays, compare directly (e.g. both Empty)
        If Not IsArray(arr1) And Not IsArray(arr2) Then
            If IsEmpty(arr1) And IsEmpty(arr2) Then ArraysEqual = True
            ' Add other non-array comparisons if needed, e.g. if arr1 = arr2 for simple types
            Exit Function
        End If
    End If

    ' Check if arrays have the same number of dimensions (simplified to 1D for this use case)
    ' VBA arrays can have different LBounds, so direct length comparison isn't enough.
    ' We need to compare UBound - LBound + 1 for each.

    Dim lbound1 As Long, ubound1 As Long
    Dim lbound2 As Long, ubound2 As Long
    Dim count1 As Long, count2 As Long

    On Error Resume Next ' Handle uninitialized arrays which cause error on LBound/UBound
    lbound1 = LBound(arr1)
    ubound1 = UBound(arr1)
    If Err.Number <> 0 Then ' arr1 is likely uninitialized or not a proper array
        count1 = -1 ' Mark as invalid/empty for comparison
        Err.Clear
    Else
        count1 = ubound1 - lbound1 + 1
    End If

    lbound2 = LBound(arr2)
    ubound2 = UBound(arr2)
    If Err.Number <> 0 Then ' arr2 is likely uninitialized
        count2 = -1 ' Mark as invalid/empty
        Err.Clear
    Else
        count2 = ubound2 - lbound2 + 1
    End If
    On Error GoTo ErrorHandler ' Restore default error handling

    ' If one array was invalid (e.g. Empty passed to IsArray then failed LBound)
    ' and the other was valid, they are not equal.
    ' If both were invalid in the same way (e.g. both Empty), count1 and count2 will be -1.
    If count1 = -1 And count2 = -1 Then ' Both effectively empty/uninitialized
        ArraysEqual = True
        Exit Function
    End If
    If count1 <> count2 Then Exit Function ' Different number of elements

    ' If count is 0 for both (e.g., Array() vs Array()), they are equal
    If count1 = 0 Then ' Implies count2 is also 0
        ArraysEqual = True
        Exit Function
    End If
    
    ' Compare elements
    Dim i As Long, j As Long
    For i = lbound1 To ubound1
        j = lbound2 + (i - lbound1) ' Corresponding index in arr2
        If arr1(i) <> arr2(j) Then Exit Function ' Elements differ
    Next i

    ' If we reached here, arrays are equal
    ArraysEqual = True
    Exit Function

ErrorHandler:
    OCRUtils.LogToFile "OCRConfig.ArraysEqual: Error " & Err.Number & " - " & Err.Description
    ArraysEqual = False ' Ensure it returns False on error
End Function

'===============================================================================
' LOG CONFIGURATION - COLUMN MAPPING AND HEADERS
'===============================================================================

' Get column mapping for a field (returns column letter like "A", "B", etc.)
Public Function GetColumnMapping(fieldName As String) As String
    ' Map field names to default columns for backward compatibility
    Dim defaultColumn As String
    Select Case fieldName
        Case "From": defaultColumn = "B"
        Case "To": defaultColumn = "C"
        Case "LetterRef": defaultColumn = "D"
        Case "LetterDate": defaultColumn = "E"
        Case "Subject": defaultColumn = "F"
        Case "References": defaultColumn = "G"
        Case "Body": defaultColumn = "H"
        Case "Summary": defaultColumn = "I"
        Case "Tags": defaultColumn = "J"
        ' New tracing fields
        Case "ResponseLetter": defaultColumn = "K"
        Case "ResponseDate": defaultColumn = "L"
        Case "ReferringLetter": defaultColumn = "M"
        Case "ReferringDate": defaultColumn = "N"
        Case Else: defaultColumn = "A"
    End Select
    
    GetColumnMapping = GetWorkflowSetting("logConfiguration.columnMapping." & fieldName, defaultColumn)
End Function

' Set column mapping for a field
Public Sub SetColumnMapping(fieldName As String, column As String)
    SetWorkflowSetting "logConfiguration.columnMapping." & fieldName, column
End Sub

' Get header name for a field
Public Function GetHeaderName(fieldName As String) As String
    ' Map field names to default header names
    Dim defaultHeader As String
    Select Case fieldName
        Case "From": defaultHeader = "From"
        Case "To": defaultHeader = "To"
        Case "LetterRef": defaultHeader = "Letter Reference"
        Case "LetterDate": defaultHeader = "Letter Date"
        Case "Subject": defaultHeader = "Subject"
        Case "References": defaultHeader = "References"
        Case "Body": defaultHeader = "Body"
        Case "Summary": defaultHeader = "Summary"
        Case "Tags": defaultHeader = "Topics / Tags"
        ' New tracing fields
        Case "ResponseLetter": defaultHeader = "Response Letter"
        Case "ResponseDate": defaultHeader = "Response Date"
        Case "ReferringLetter": defaultHeader = "Referring Letter"
        Case "ReferringDate": defaultHeader = "Referring Date"
        Case Else: defaultHeader = fieldName
    End Select
    
    GetHeaderName = GetWorkflowSetting("logConfiguration.headerNames." & fieldName, defaultHeader)
End Function

' Set header name for a field
Public Sub SetHeaderName(fieldName As String, headerName As String)
    SetWorkflowSetting "logConfiguration.headerNames." & fieldName, headerName
End Sub

' Get field enabled state for log configuration (separate from extraction fields)
Public Function GetLogFieldEnabled(fieldName As String) As Boolean
    ' Essential fields enabled by default, others disabled
    Dim defaultEnabled As Boolean
    Select Case fieldName
        Case "From", "To", "LetterRef", "LetterDate", "Subject", "References", "Body"
            defaultEnabled = True
        Case "Summary", "Tags", "ResponseLetter", "ResponseDate", "ReferringLetter", "ReferringDate"
            defaultEnabled = False
        Case Else
            defaultEnabled = True
    End Select
    
    GetLogFieldEnabled = GetWorkflowSetting("logConfiguration.fieldsEnabled." & fieldName, defaultEnabled)
End Function

' Set field enabled state for log configuration
Public Sub SetLogFieldEnabled(fieldName As String, enabled As Boolean)
    SetWorkflowSetting "logConfiguration.fieldsEnabled." & fieldName, enabled
End Sub

' Get all log field configurations as Dictionary
Public Function GetAllLogFieldConfigurations() As Object
    Dim allFields As Object
    Set allFields = CreateObject("Scripting.Dictionary")
    
    ' Essential fields
    Dim essentialFields As Variant
    essentialFields = Array("From", "To", "LetterRef", "LetterDate", "Subject", "References", "Body")
    
    ' Helper fields
    Dim helperFields As Variant
    helperFields = Array("Summary", "Tags")
    
    ' Tracing fields
    Dim tracingFields As Variant
    tracingFields = Array("ResponseLetter", "ResponseDate", "ReferringLetter", "ReferringDate")
    
    Dim fieldName As Variant
    Dim fieldConfig As Object
    
    ' Process all field types
    Dim allFieldsList As Variant
    allFieldsList = Array(essentialFields, helperFields, tracingFields)
    
    Dim fieldGroup As Variant
    For Each fieldGroup In allFieldsList
        For Each fieldName In fieldGroup
            Set fieldConfig = CreateObject("Scripting.Dictionary")
            fieldConfig("enabled") = GetLogFieldEnabled(CStr(fieldName))
            fieldConfig("column") = GetColumnMapping(CStr(fieldName))
            fieldConfig("headerName") = GetHeaderName(CStr(fieldName))
            fieldConfig("defaultName") = GetDefaultHeaderName(CStr(fieldName))
            Set allFields(CStr(fieldName)) = fieldConfig
        Next fieldName
    Next fieldGroup
    
    Set GetAllLogFieldConfigurations = allFields
End Function

' Helper function to get default header names
Private Function GetDefaultHeaderName(fieldName As String) As String
    Select Case fieldName
        Case "From": GetDefaultHeaderName = "From"
        Case "To": GetDefaultHeaderName = "To"
        Case "LetterRef": GetDefaultHeaderName = "Letter Reference"
        Case "LetterDate": GetDefaultHeaderName = "Letter Date"
        Case "Subject": GetDefaultHeaderName = "Subject"
        Case "References": GetDefaultHeaderName = "References"
        Case "Body": GetDefaultHeaderName = "Body"
        Case "Summary": GetDefaultHeaderName = "Summary"
        Case "Tags": GetDefaultHeaderName = "Topics / Tags"
        Case "ResponseLetter": GetDefaultHeaderName = "Response Letter"
        Case "ResponseDate": GetDefaultHeaderName = "Response Date"
        Case "ReferringLetter": GetDefaultHeaderName = "Referring Letter"
        Case "ReferringDate": GetDefaultHeaderName = "Referring Date"
        Case Else: GetDefaultHeaderName = fieldName
    End Select
End Function

' Apply log configuration to worksheet (sets up headers according to configuration)
Public Sub ApplyLogConfigurationToWorksheet()
    Dim ws As Worksheet
    Set ws = ActiveSheet
    
    If ws Is Nothing Then
        OCRUtils.LogToFile "OCRConfig: ApplyLogConfigurationToWorksheet - No active worksheet"
        Exit Sub
    End If
    
    OCRUtils.LogToFile "OCRConfig: Applying log configuration to worksheet"
    
    ' Clear existing headers (row 1)
    ws.Rows(1).ClearContents
    
    ' Always set PDF Path in column A
    ws.Cells(1, 1).Value = "PDF Path"
    
    ' Apply configured fields
    Dim allFields As Object
    Set allFields = GetAllLogFieldConfigurations()
    
    Dim fieldName As Variant
    Dim fieldConfig As Object
    Dim columnLetter As String
    Dim columnNumber As Integer
    
    For Each fieldName In allFields.Keys
        Set fieldConfig = allFields(fieldName)
        
        If fieldConfig("enabled") Then
            columnLetter = fieldConfig("column")
            columnNumber = ColumnLetterToNumber(columnLetter)
            
            If columnNumber > 0 Then
                ws.Cells(1, columnNumber).Value = fieldConfig("headerName")
                OCRUtils.LogToFile "OCRConfig: Set header '" & fieldConfig("headerName") & "' in column " & columnLetter & " for field " & fieldName
            End If
        End If
    Next fieldName
    
    ' Format headers
    With ws.Rows(1)
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .Borders.LineStyle = xlContinuous
    End With
    
    OCRUtils.LogToFile "OCRConfig: Log configuration applied to worksheet successfully"
End Sub

' Convert column letter (A, B, C, etc.) to column number (1, 2, 3, etc.)
Public Function ColumnLetterToNumber(columnLetter As String) As Integer
    Dim i As Integer
    Dim result As Integer
    result = 0
    
    For i = 1 To Len(columnLetter)
        result = result * 26 + (Asc(UCase(Mid(columnLetter, i, 1))) - Asc("A") + 1)
    Next i
    
    ColumnLetterToNumber = result
End Function

' Convert column number (1, 2, 3, etc.) to column letter (A, B, C, etc.)
Public Function ColumnNumberToLetter(columnNumber As Integer) As String
    Dim dividend As Integer
    Dim modulo As Integer
    Dim columnName As String
    
    dividend = columnNumber
    columnName = ""
    
    Do While dividend > 0
        modulo = (dividend - 1) Mod 26
        columnName = Chr(65 + modulo) & columnName
        dividend = Int((dividend - modulo) / 26)
    Loop
    
    ColumnNumberToLetter = columnName
End Function

' Validate column assignment (check for duplicates, etc.)
Public Function ValidateColumnAssignment(fieldName As String, columnLetter As String) As Boolean
    Dim allFields As Object
    Set allFields = GetAllLogFieldConfigurations()
    
    Dim otherFieldName As Variant
    Dim otherFieldConfig As Object
    
    ' Check if any other enabled field uses the same column
    For Each otherFieldName In allFields.Keys
        If CStr(otherFieldName) <> fieldName Then
            Set otherFieldConfig = allFields(otherFieldName)
            If otherFieldConfig("enabled") And UCase(otherFieldConfig("column")) = UCase(columnLetter) Then
                OCRUtils.LogToFile "OCRConfig: Column " & columnLetter & " already assigned to field " & otherFieldName
                ValidateColumnAssignment = False
                Exit Function
            End If
        End If
    Next otherFieldName
    
    ' Check if column A is being used (reserved for PDF Path)
    If UCase(columnLetter) = "A" Then
        OCRUtils.LogToFile "OCRConfig: Column A is reserved for PDF Path"
        ValidateColumnAssignment = False
        Exit Function
    End If
    
    ValidateColumnAssignment = True
End Function

'===============================================================================
' LOG CONFIGURATION TESTING AND VALIDATION
'===============================================================================

' Test function to validate log configuration functionality
Public Sub TestLogConfiguration()
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "OCRConfig: Starting log configuration test"
    
    ' Test 1: Set and get column mappings
    OCRUtils.LogToFile "OCRConfig: Test 1 - Column mapping functions"
    SetColumnMapping "From", "C"
    If GetColumnMapping("From") = "C" Then
        OCRUtils.LogToFile "OCRConfig: ✓ Column mapping test passed"
    Else
        OCRUtils.LogToFile "OCRConfig: ✗ Column mapping test failed"
    End If
    
    ' Test 2: Set and get header names
    OCRUtils.LogToFile "OCRConfig: Test 2 - Header name functions"
    SetHeaderName "From", "Sender Organization"
    If GetHeaderName("From") = "Sender Organization" Then
        OCRUtils.LogToFile "OCRConfig: ✓ Header name test passed"
    Else
        OCRUtils.LogToFile "OCRConfig: ✗ Header name test failed"
    End If
    
    ' Test 3: Set and get field enabled states
    OCRUtils.LogToFile "OCRConfig: Test 3 - Field enabled state functions"
    SetLogFieldEnabled "Summary", True
    If GetLogFieldEnabled("Summary") = True Then
        OCRUtils.LogToFile "OCRConfig: ✓ Field enabled state test passed"
    Else
        OCRUtils.LogToFile "OCRConfig: ✗ Field enabled state test failed"
    End If
    
    ' Test 4: Column validation
    OCRUtils.LogToFile "OCRConfig: Test 4 - Column validation"
    If ValidateColumnAssignment("From", "D") = True Then
        OCRUtils.LogToFile "OCRConfig: ✓ Column validation test passed"
    Else
        OCRUtils.LogToFile "OCRConfig: ✗ Column validation test failed"
    End If
    
    ' Test 5: Get all configurations
    OCRUtils.LogToFile "OCRConfig: Test 5 - Get all configurations"
    Dim allConfigs As Object
    Set allConfigs = GetAllLogFieldConfigurations()
    If allConfigs.Count > 0 Then
        OCRUtils.LogToFile "OCRConfig: ✓ Get all configurations test passed (" & allConfigs.Count & " fields found)"
    Else
        OCRUtils.LogToFile "OCRConfig: ✗ Get all configurations test failed"
    End If
    
    ' Test 6: Column letter conversion
    OCRUtils.LogToFile "OCRConfig: Test 6 - Column letter conversion"
    If ColumnLetterToNumber("C") = 3 And ColumnNumberToLetter(3) = "C" Then
        OCRUtils.LogToFile "OCRConfig: ✓ Column conversion test passed"
    Else
        OCRUtils.LogToFile "OCRConfig: ✗ Column conversion test failed"
    End If
    
    ' Save configuration to persist test changes
    SaveConfigurationToJSON
    
    OCRUtils.LogToFile "OCRConfig: Log configuration test completed successfully"
    MsgBox "Log configuration test completed successfully! Check the log file for detailed results.", vbInformation
    
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRConfig: Error in TestLogConfiguration: " & Err.Description
    MsgBox "Log configuration test failed: " & Err.Description, vbCritical
End Sub

' Apply test configuration for demonstration
Public Sub ApplyTestLogConfiguration()
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "OCRConfig: Applying test log configuration"
    
    ' Set up a sample configuration
    SetLogFieldEnabled "From", True
    SetColumnMapping "From", "B"
    SetHeaderName "From", "Sender"
    
    SetLogFieldEnabled "To", True
    SetColumnMapping "To", "C"
    SetHeaderName "To", "Recipient"
    
    SetLogFieldEnabled "Subject", True
    SetColumnMapping "Subject", "D"
    SetHeaderName "Subject", "Letter Subject"
    
    SetLogFieldEnabled "Body", True
    SetColumnMapping "Body", "E"
    SetHeaderName "Body", "Content"
    
    ' Disable optional fields for clean test
    SetLogFieldEnabled "Summary", False
    SetLogFieldEnabled "Tags", False
    SetLogFieldEnabled "ResponseLetter", False
    SetLogFieldEnabled "ResponseDate", False
    SetLogFieldEnabled "ReferringLetter", False
    SetLogFieldEnabled "ReferringDate", False
    
    SaveConfigurationToJSON
    
    OCRUtils.LogToFile "OCRConfig: Test log configuration applied successfully"
    MsgBox "Test log configuration applied! You can now test the settings form page 2 or apply headers to worksheet.", vbInformation
    
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRConfig: Error in ApplyTestLogConfiguration: " & Err.Description
    MsgBox "Error applying test configuration: " & Err.Description, vbCritical
End Sub
