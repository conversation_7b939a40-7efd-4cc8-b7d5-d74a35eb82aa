Attribute VB_Name = "ResponseParser"
'
' OCR Response Parser Module
' Handles parsing and field extraction from different provider responses
' Author: AI Assistant
'

Option Explicit

' ================================
' MAIN RESPONSE PARSING FUNCTIONS
' ================================

' Extract data from OpenRouter response (both vision and text modes)
Public Function ExtractDataFromOpenRouterResponse(ByVal responseText As String, ByVal isVisionMode As Boolean) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim apiResponse As Object
    Dim messageContent As String
    Dim ocrData As Object
    Dim currentModeName As String
    
    Set apiResponse = JsonConverter.ParseJson(responseText)
    
    ' Determine current mode for logging
    If isVisionMode Then
        currentModeName = "OpenRouter Vision"
    Else
        currentModeName = "OpenRouter Text"
    End If
    
    If Not apiResponse Is Nothing Then
        If apiResponse.Exists("choices") Then
            If apiResponse("choices").Count > 0 Then
                messageContent = apiResponse("choices")(1)("message")("content")
                
                ' Log the raw content before parsing
                OCRUtils.LogToFile "=== RAW MODEL RESPONSE START (" & currentModeName & ") ==="
                OCRUtils.LogToFile "Content Length: " & Len(messageContent) & " characters"
                OCRUtils.LogToFile "Raw Content: " & messageContent
                OCRUtils.LogToFile "=== RAW MODEL RESPONSE END (" & currentModeName & ") ==="
                
                ' Clean the content for JSON parsing (remove markdown code blocks)
                Dim cleanedContent As String
                cleanedContent = OCRUtils.CleanJsonFromMarkdown(messageContent)
                OCRUtils.LogToFile "Cleaned Content: " & cleanedContent
                
                Set ocrData = JsonConverter.ParseJson(cleanedContent)
                Dim fieldSelection As Object
                Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections
                
                If Not ocrData Is Nothing Then
                    ExtractDataFromOpenRouterResponse = ParseOCRFields(ocrData, fieldSelection)
                    ExtractDataFromOpenRouterResponse.Success = True
                    OCRUtils.LogToFile "JSON parsing successful for " & currentModeName
                Else
                    ExtractDataFromOpenRouterResponse.ErrorMessage = "Failed to parse JSON from response"
                    OCRUtils.LogToFile "JSON parsing failed for " & currentModeName & " - ocrData is Nothing"
                End If
            Else
                ExtractDataFromOpenRouterResponse.ErrorMessage = "No choices found in API response"
                OCRUtils.LogToFile "No choices found in API response for " & currentModeName
            End If
        Else
            ExtractDataFromOpenRouterResponse.ErrorMessage = "No 'choices' key found in API response"
            OCRUtils.LogToFile "No 'choices' key found in API response for " & currentModeName
        End If
    Else
        ExtractDataFromOpenRouterResponse.ErrorMessage = "Failed to parse API response"
        OCRUtils.LogToFile "API response parsing failed for " & currentModeName & " - apiResponse is Nothing"
    End If
    
    Exit Function
    
ErrorHandler:
    ExtractDataFromOpenRouterResponse.Success = False
    ExtractDataFromOpenRouterResponse.ErrorMessage = "Error extracting data: " & Err.Description
    OCRUtils.LogToFile "ERROR in ExtractDataFromOpenRouterResponse (" & currentModeName & "): " & Err.Description
End Function

' Extract data from Ollama response (structured JSON)
Public Function ExtractDataFromOllamaResponse(ByVal responseData As Object) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim ocrData As Object
    Dim responseText As String
    
    If responseData.Exists("response") Then
        responseText = responseData("response")
        Set ocrData = JsonConverter.ParseJson(responseText)
        Dim fieldSelection As Object
        Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections
        
        If Not ocrData Is Nothing Then
            ExtractDataFromOllamaResponse = ParseOCRFields(ocrData, fieldSelection)
            ExtractDataFromOllamaResponse.Success = True
        Else
            ExtractDataFromOllamaResponse.ErrorMessage = "Failed to parse Ollama response JSON"
        End If
    Else
        ExtractDataFromOllamaResponse.ErrorMessage = "No 'response' key found in Ollama response"
    End If
    
    Exit Function
    
ErrorHandler:
    ExtractDataFromOllamaResponse.Success = False
    ExtractDataFromOllamaResponse.ErrorMessage = "Error extracting Ollama data: " & Err.Description
End Function

' Extract data from Ollama plain text response (OCR-only mode)
Public Function ExtractDataFromOllamaPlainTextResponse(ByVal responseJson As String) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim responseData As Object
    Dim plainText As String
    Dim Result As ocrResult
    
    OCRUtils.LogToFile "ResponseParser: Processing Ollama plain text response (Extract Fields = FALSE)"
    
    ' Parse the JSON wrapper to get the plain text response
    Set responseData = JsonConverter.ParseJson(responseJson)
    
    If Not responseData Is Nothing And responseData.Exists("response") Then
        plainText = CStr(responseData("response"))
        
        ' For OCR-only mode, put all text in Body and leave other fields empty (don't overwrite existing data)
        Result.Body = plainText
        Result.FromText = ""
        Result.ToText = ""
        Result.LetterReference = ""
        Result.LetterDate = ""
        Result.Subject = ""
        Result.References = ""
        Result.Summary = ""
        Result.Tags = ""
        Result.Success = True
        
        OCRUtils.LogToFile "ResponseParser: Successfully extracted " & Len(plainText) & " characters of plain text from Ollama"
        ExtractDataFromOllamaPlainTextResponse = Result
    Else
        Result.ErrorMessage = "Failed to parse Ollama plain text response"
        Result.Success = False
        OCRUtils.LogToFile "ResponseParser: ERROR - Failed to parse Ollama plain text response"
        ExtractDataFromOllamaPlainTextResponse = Result
    End If
    
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error extracting Ollama plain text: " & Err.Description
    OCRUtils.LogToFile "ResponseParser: ERROR in ExtractDataFromOllamaPlainTextResponse: " & Err.Description
    ExtractDataFromOllamaPlainTextResponse = Result
End Function

' Extract data from OpenRouter plain text response (OCR-only mode)
Public Function ExtractDataFromOpenRouterPlainTextResponse(ByVal responseJson As String, ByVal isVisionMode As Boolean) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim apiResponse As Object
    Dim messageContent As String
    Dim Result As ocrResult
    Dim currentModeName As String
    
    ' Determine current mode for logging
    If isVisionMode Then
        currentModeName = "OpenRouter Vision"
    Else
        currentModeName = "OpenRouter Text"
    End If
    
    OCRUtils.LogToFile "ResponseParser: Processing " & currentModeName & " plain text response (Extract Fields = FALSE)"
    
    ' Parse the JSON wrapper to get the plain text response
    Set apiResponse = JsonConverter.ParseJson(responseJson)
    
    If Not apiResponse Is Nothing Then
        If apiResponse.Exists("choices") Then
            If apiResponse("choices").Count > 0 Then
                messageContent = CStr(apiResponse("choices")(1)("message")("content"))
                
                ' For OCR-only mode, put all text in Body and leave other fields empty (don't overwrite existing data)
                Result.Body = messageContent
                Result.FromText = ""
                Result.ToText = ""
                Result.LetterReference = ""
                Result.LetterDate = ""
                Result.Subject = ""
                Result.References = ""
                Result.Summary = ""
                Result.Tags = ""
                Result.Success = True
                
                OCRUtils.LogToFile "ResponseParser: Successfully extracted " & Len(messageContent) & " characters of plain text from " & currentModeName
                ExtractDataFromOpenRouterPlainTextResponse = Result
            Else
                Result.ErrorMessage = "No choices found in API response"
                Result.Success = False
                OCRUtils.LogToFile "ResponseParser: ERROR - No choices found in " & currentModeName & " response"
                ExtractDataFromOpenRouterPlainTextResponse = Result
            End If
        Else
            Result.ErrorMessage = "No 'choices' key found in API response"
            Result.Success = False
            OCRUtils.LogToFile "ResponseParser: ERROR - No 'choices' key found in " & currentModeName & " response"
            ExtractDataFromOpenRouterPlainTextResponse = Result
        End If
    Else
        Result.ErrorMessage = "Failed to parse API response"
        Result.Success = False
        OCRUtils.LogToFile "ResponseParser: ERROR - Failed to parse " & currentModeName & " plain text response"
        ExtractDataFromOpenRouterPlainTextResponse = Result
    End If
    
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error extracting " & currentModeName & " plain text: " & Err.Description
    OCRUtils.LogToFile "ResponseParser: ERROR in ExtractDataFromOpenRouterPlainTextResponse (" & currentModeName & "): " & Err.Description
    ExtractDataFromOpenRouterPlainTextResponse = Result
End Function

' Extract data from LM Studio response
Public Function ExtractDataFromLMStudioResponse(ByVal responseJson As String) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim apiResponse As Object
    Dim Result As ocrResult
    Dim cleanedJson As String
    
    ' Initialize result
    Result.Success = False
    
    OCRUtils.LogToFile "ResponseParser: Processing LM Studio extraction response"
    
    ' Parse the JSON response - ensure responseJson is a string
    If TypeName(responseJson) <> "String" Then
        Result.ErrorMessage = "Expected string input but got " & TypeName(responseJson)
        OCRUtils.LogToFile "LMStudio: ERROR - Expected string input but got " & TypeName(responseJson)
        GoTo ExitFunction
    End If
    
    ' Apply universal JSON cleaning before parsing
    cleanedJson = OCRUtils.CleanJsonFromMarkdown(responseJson)
    OCRUtils.LogToFile "ResponseParser: Applied JSON cleaning, cleaned length: " & Len(cleanedJson)
    OCRUtils.LogToFile "ResponseParser: First 200 chars of cleaned JSON: " & Left(cleanedJson, 200)
    
    Set apiResponse = JsonConverter.ParseJson(cleanedJson)
    
    If apiResponse Is Nothing Then
        ' Try parsing original JSON as fallback
        OCRUtils.LogToFile "LMStudio: Cleaned JSON failed, trying original JSON as fallback"
        Set apiResponse = JsonConverter.ParseJson(responseJson)
        
        If apiResponse Is Nothing Then
            Result.ErrorMessage = "Failed to parse JSON response (both cleaned and original attempts failed)"
            OCRUtils.LogToFile "LMStudio: ERROR - Failed to parse JSON response after cleaning and fallback"
            GoTo ExitFunction
        Else
            OCRUtils.LogToFile "LMStudio: Original JSON parsing succeeded after cleaned version failed"
        End If
    Else
        OCRUtils.LogToFile "LMStudio: Successfully parsed cleaned JSON"
    End If
    
    ' Check if the response is already in our expected format (direct JSON object)
    If apiResponse.Exists("from") Or apiResponse.Exists("to") Or apiResponse.Exists("subject") Or apiResponse.Exists("letter_reference") Then
        ' The response is already in our expected format
        ' Use ParseOCRFields for consistency with other providers
        Dim fieldSelection As Object
        Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections
        
        Result = ParseOCRFields(apiResponse, fieldSelection)
        Result.Success = True
        
        OCRUtils.LogToFile "LMStudio: Successfully parsed direct JSON response using ParseOCRFields"
    ' Check for OpenAI-compatible format with 'choices'
    ElseIf apiResponse.Exists("choices") Then
        ' Original parsing logic for 'choices' format
        If apiResponse("choices").Count > 0 Then
            Dim messageContent As String
            messageContent = apiResponse("choices")(1)("message")("content")
            
            ' Clean the JSON from the message content if needed
            cleanedJson = OCRUtils.CleanJsonFromMarkdown(messageContent)
            
            ' Parse the cleaned JSON
            Dim contentObj As Object
            Set contentObj = JsonConverter.ParseJson(cleanedJson)
            
            If Not contentObj Is Nothing Then
                ' Extract fields from the content object with proper type handling
                If contentObj.Exists("from") Then Result.FromText = CStr(contentObj("from"))
                If contentObj.Exists("to") Then Result.ToText = CStr(contentObj("to"))
                If contentObj.Exists("letter_reference") Then Result.LetterReference = CStr(contentObj("letter_reference"))
                If contentObj.Exists("date") Then Result.LetterDate = CStr(contentObj("date"))
                If contentObj.Exists("letter_date") Then Result.LetterDate = CStr(contentObj("letter_date"))
                If contentObj.Exists("subject") Then Result.Subject = CStr(contentObj("subject"))
                If contentObj.Exists("references") Then
                    ' Handle references as either string or array
                    If TypeName(contentObj("references")) = "Collection" Then
                        Result.References = JsonToString(contentObj("references"))
                    Else
                        Result.References = CStr(contentObj("references"))
                    End If
                End If
                If contentObj.Exists("body") Then
                    ' Handle body as either string or complex object/array
                    If TypeName(contentObj("body")) = "Collection" Then
                        Result.Body = JsonToString(contentObj("body"))
                    Else
                        Result.Body = CStr(contentObj("body"))
                    End If
                End If
                If contentObj.Exists("summary") Then Result.Summary = CStr(contentObj("summary"))
                If contentObj.Exists("tags") Then Result.Tags = CStr(contentObj("tags"))
                
                Result.Success = True
                OCRUtils.LogToFile "LMStudio: Successfully parsed JSON from choices content"
            Else
                Result.ErrorMessage = "Failed to parse JSON content from choices"
                OCRUtils.LogToFile "Failed to parse JSON content from choices in LM Studio response"
            End If
        Else
            Result.ErrorMessage = "No choices found in API response"
            OCRUtils.LogToFile "No choices found in API response for LM Studio"
        End If
    Else
        Result.ErrorMessage = "Unrecognized JSON format in LM Studio response"
        OCRUtils.LogToFile "Unrecognized JSON format in LM Studio response - neither direct fields nor 'choices' found"
    End If
    
ExitFunction:
    ExtractDataFromLMStudioResponse = Result
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error extracting LM Studio data: " & Err.Description
    OCRUtils.LogToFile "LMStudio: ERROR in ExtractDataFromLMStudioResponse: " & Err.Description
    ExtractDataFromLMStudioResponse = Result
End Function

' Extract data from LM Studio plain text response (OCR-only mode)
Public Function ExtractDataFromLMStudioPlainTextResponse(ByVal responseJson As String) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim responseData As Object
    Dim messageContent As String
    Dim Result As ocrResult
    Dim plainText As String
    
    OCRUtils.LogToFile "ResponseParser: Processing LM Studio plain text response (Extract Fields = FALSE)"
    
    ' Parse the JSON wrapper to get the plain text response
    Set responseData = JsonConverter.ParseJson(responseJson)
    
    If Not responseData Is Nothing Then
        If responseData.Exists("choices") Then
            If responseData("choices").Count > 0 Then
                messageContent = CStr(responseData("choices")(1)("message")("content"))
                plainText = messageContent ' Default to full content
                
                ' Try to parse messageContent as JSON to find a nested text field
                Dim innerJsonObj As Object
                On Error Resume Next ' Ignore error if messageContent is not JSON
                Set innerJsonObj = JsonConverter.ParseJson(messageContent)
                On Error GoTo ErrorHandler ' Restore error handling

                If Not innerJsonObj Is Nothing Then
                    ' Successfully parsed as JSON, look for common text keys
                    If innerJsonObj.Exists("natural_text") Then
                        plainText = CStr(innerJsonObj("natural_text"))
                        OCRUtils.LogToFile "ResponseParser: Extracted plain text from nested 'natural_text' key in LM Studio OCR response"
                    ElseIf innerJsonObj.Exists("text") Then
                        plainText = CStr(innerJsonObj("text"))
                        OCRUtils.LogToFile "ResponseParser: Extracted plain text from nested 'text' key in LM Studio OCR response"
                    ElseIf innerJsonObj.Exists("full_text") Then
                        plainText = CStr(innerJsonObj("full_text"))
                        OCRUtils.LogToFile "ResponseParser: Extracted plain text from nested 'full_text' key in LM Studio OCR response"
                    Else
                        OCRUtils.LogToFile "ResponseParser: LM Studio OCR response was JSON, but no known text key found. Using full JSON content as plain text."
                        ' plainText remains messageContent (the full JSON string)
                    End If
                Else
                    OCRUtils.LogToFile "ResponseParser: LM Studio OCR response content was not valid JSON. Using as plain text."
                End If
                
                ' For OCR-only mode, put all text in Body and leave other fields empty
                Result.Body = plainText
                Result.FromText = ""
                Result.ToText = ""
                Result.LetterReference = ""
                Result.LetterDate = ""
                Result.Subject = ""
                Result.References = ""
                Result.Summary = ""
                Result.Tags = ""
                Result.Success = True
                
                OCRUtils.LogToFile "ResponseParser: Successfully extracted " & Len(messageContent) & " characters of plain text from LM Studio"
                ExtractDataFromLMStudioPlainTextResponse = Result
            Else
                Result.ErrorMessage = "No choices found in LM Studio response"
                Result.Success = False
                OCRUtils.LogToFile "ResponseParser: ERROR - No choices found in LM Studio response"
                ExtractDataFromLMStudioPlainTextResponse = Result
            End If
        Else
            Result.ErrorMessage = "No 'choices' key found in LM Studio response"
            Result.Success = False
            OCRUtils.LogToFile "ResponseParser: ERROR - No 'choices' key found in LM Studio response"
            ExtractDataFromLMStudioPlainTextResponse = Result
        End If
    Else
        Result.ErrorMessage = "Failed to parse LM Studio response"
        Result.Success = False
        OCRUtils.LogToFile "ResponseParser: ERROR - Failed to parse LM Studio response"
        ExtractDataFromLMStudioPlainTextResponse = Result
    End If
    
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error extracting LM Studio plain text: " & Err.Description
    OCRUtils.LogToFile "ResponseParser: ERROR in ExtractDataFromLMStudioPlainTextResponse: " & Err.Description
    ExtractDataFromLMStudioPlainTextResponse = Result
End Function

' Parse Docling API response
Public Function ParseDoclingResponse(ByVal jsonString As String, ByVal outputFormat As String) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim Result As ocrResult
    Dim apiResponse As Object
    Dim document As Object
    Dim extractedText As String
    
    ' Initialize result
    Result.Success = False
    Result.Body = ""
    
    ' Log response for debugging
    OCRUtils.LogToFile "=== DOCLING API RESPONSE START ==="
    OCRUtils.LogToFile "Response length: " & Len(jsonString) & " characters"
    OCRUtils.LogToFile "First 500 chars: " & Left(jsonString, 500)
    OCRUtils.LogToFile "=== DOCLING API RESPONSE END ==="
    
    ' Parse JSON response
    Set apiResponse = JsonConverter.ParseJson(jsonString)
    
    If apiResponse Is Nothing Then
        Result.ErrorMessage = "Failed to parse JSON response"
        OCRUtils.LogToFile "Docling: ERROR - Failed to parse JSON response"
        GoTo ExitFunction
    End If
    
    ' Check status
    If apiResponse.Exists("status") Then
        If apiResponse("status") <> "success" Then
            Result.ErrorMessage = "Docling API returned error status: " & apiResponse("status")
            If apiResponse.Exists("errors") Then
                Result.ErrorMessage = Result.ErrorMessage & " - " & JsonToString(apiResponse("errors"))
            End If
            OCRUtils.LogToFile "Docling: ERROR - " & Result.ErrorMessage
            GoTo ExitFunction
        End If
    End If
    
    ' Extract document content
    If apiResponse.Exists("document") Then
        Set document = apiResponse("document")
        
        ' Extract text based on output format
        Select Case LCase(outputFormat)
            Case "text"
                If document.Exists("text_content") And Not IsNull(document("text_content")) Then
                    extractedText = document("text_content")
                End If
            Case "md", "markdown"
                If document.Exists("md_content") And Not IsNull(document("md_content")) Then
                    extractedText = document("md_content")
                End If
            Case "json"
                If document.Exists("json_content") And Not IsNull(document("json_content")) Then
                    extractedText = JsonToString(document("json_content"))
                End If
            Case Else
                Result.ErrorMessage = "Unsupported output format: " & outputFormat
                GoTo ExitFunction
        End Select
        
        If extractedText <> "" Then
            ' For Docling, we get raw OCR text without structured extraction
            ' Set only the Body content - structured fields remain empty
            Result.Body = CleanDoclingText(extractedText)
            
            ' Only set placeholder values if Extract Fields = TRUE (structured mode)
            ' When Extract Fields = FALSE, leave other fields empty (don't overwrite existing data)
            ' Always use structured mode (extraction controlled by workflow)
        If True Then
                Result.FromText = "N/A via Docling"
                Result.ToText = "N/A via Docling"
                Result.LetterReference = "N/A via Docling"
                Result.LetterDate = "N/A via Docling"
                Result.Subject = "N/A via Docling"
                Result.References = "N/A via Docling"
                Result.Summary = "N/A via Docling"
                Result.Tags = "N/A via Docling"
            Else
                Result.FromText = ""
                Result.ToText = ""
                Result.LetterReference = ""
                Result.LetterDate = ""
                Result.Subject = ""
                Result.References = ""
                Result.Summary = ""
                Result.Tags = ""
            End If
            Result.Success = True
            
            OCRUtils.LogToFile "Docling: Successfully extracted " & Len(Result.Body) & " characters of " & outputFormat & " content"
        Else
            Result.ErrorMessage = "No content found in " & outputFormat & " format"
            OCRUtils.LogToFile "Docling: ERROR - No content found in " & outputFormat & " format"
        End If
    Else
        Result.ErrorMessage = "No document object in response"
        OCRUtils.LogToFile "Docling: ERROR - No document object in response"
    End If
    
ExitFunction:
    ParseDoclingResponse = Result
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error parsing Docling response: " & Err.Description
    OCRUtils.LogToFile "Docling: ERROR - " & Result.ErrorMessage
    ParseDoclingResponse = Result
End Function

' ================================
' DOCLING POST-PROCESSING
' ================================

' Process Docling OCR text through VLM for field extraction
Public Function ProcessDoclingWithVLM(ByVal ocrText As String, ByVal provider As APIProviderType, Optional ByVal extractionModel As String = "") As ocrResult
    On Error GoTo ErrorHandler
    
    Dim prompt As String
    Dim Response As String
    Dim Result As ocrResult
    Dim currentExtractionModel As String
    
    ' Determine the model to use for extraction
    If extractionModel <> "" Then
        currentExtractionModel = extractionModel
    Else
        ' Fallback to the provider's default model if no specific extractionModel is passed
        ' This requires GetProviderDefaultModel to work with APIProviderType or map it.
        ' For simplicity, we'll assume the provider's SendTextTo... function handles default if model is empty.
        currentExtractionModel = "" ' Let provider module handle default if empty
        OCRUtils.LogToFile "ResponseParser.ProcessDoclingWithVLM: No specific extraction model provided, will use provider's default."
    End If
    
    OCRUtils.LogToFile "ResponseParser.ProcessDoclingWithVLM: Using provider " & CStr(provider) & " with model: '" & currentExtractionModel & "'"

    ' Build post-processing prompt
    prompt = PromptTemplates.BuildDoclingPostProcessPrompt(ocrText)
    
    ' Send to appropriate VLM provider for field extraction
    Select Case provider
        Case OllamaProvider
            Response = Prov_Ollama.SendTextToOllama(prompt, currentExtractionModel) ' Pass model
        Case OpenRouterProvider
            Response = Prov_OpenRouter.SendTextToOpenRouter(prompt, currentExtractionModel) ' Pass model
        Case LMStudioProvider
            Response = Prov_LMStudio.SendTextToLMStudio(prompt, currentExtractionModel) ' Pass model
        Case Else
            Result.ErrorMessage = "Unsupported VLM provider for Docling post-processing"
            ProcessDoclingWithVLM = Result
            Exit Function
    End Select
    
    If Response <> "" Then
        ' Parse the VLM response for structured fields
        Result = ParseVLMPostProcessResponse(Response, provider)
    Else
        Result.ErrorMessage = "Failed to get response from VLM provider"
    End If
    
    ProcessDoclingWithVLM = Result
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error in Docling VLM post-processing: " & Err.Description
    ProcessDoclingWithVLM = Result
End Function

' ================================
' FIELD EXTRACTION HELPERS
' ================================

' Parse OCR fields from JSON data (common for all providers)
Public Function ParseOCRFields(ByVal ocrData As Object, fieldSelection As Object) As ocrResult
    Dim Result As ocrResult
    
    ' Handle both flat strings and nested objects
    If fieldSelection("From") Then Result.FromText = GetSmartJsonValue(ocrData, "from")
    If fieldSelection("To") Then Result.ToText = GetSmartJsonValue(ocrData, "to")
    If fieldSelection("LetterRef") Then Result.LetterReference = GetSmartJsonValue(ocrData, "letter_reference")
    If fieldSelection("LetterDate") Then Result.LetterDate = GetSmartJsonValue(ocrData, "letter_date", "date")
    If fieldSelection("Subject") Then Result.Subject = GetSmartJsonValue(ocrData, "subject")
    If fieldSelection("References") Then Result.References = GetSmartJsonValue(ocrData, "references")
    If fieldSelection("Body") Then Result.Body = GetSmartJsonValue(ocrData, "body")
    If fieldSelection("Summary") Then Result.Summary = GetSmartJsonValue(ocrData, "summary")
    If fieldSelection("Tags") Then Result.Tags = GetSmartJsonValue(ocrData, "tags")
    
    Result.Success = True
    
    ParseOCRFields = Result
End Function

' Smart JSON value extraction that handles both flat strings and nested objects
Private Function GetSmartJsonValue(ByVal jsonObj As Object, ParamArray keys() As Variant) As String
    On Error Resume Next
    
    Dim Key As Variant
    Dim Value As Variant
    Dim Result As String
    Dim currentKey As String
    
    ' Try each key in order
    For Each Key In keys
        currentKey = CStr(Key)
        If jsonObj.Exists(currentKey) Then
            Set Value = Nothing
            Value = jsonObj(currentKey)
            
            ' Debug logging for body and summary fields
            If currentKey = "body" Or currentKey = "summary" Then
                OCRUtils.LogToFile "GetSmartJsonValue: Processing field '" & currentKey & "'"
                OCRUtils.LogToFile "GetSmartJsonValue: Value type: " & TypeName(Value)
                If IsObject(Value) Then
                    OCRUtils.LogToFile "GetSmartJsonValue: Object detected, keys count: " & Value.Count
                    If TypeName(Value) = "Dictionary" Then
                        Dim debugKey As Variant
                        For Each debugKey In Value.keys
                            OCRUtils.LogToFile "GetSmartJsonValue: Object key: " & debugKey
                        Next
                    End If
                Else
                    OCRUtils.LogToFile "GetSmartJsonValue: Simple value, first 200 chars: " & Left(CStr(Value), 200)
                End If
            End If
            
            ' Check if it's an object (nested structure)
            If IsObject(Value) Then
                ' Handle nested object based on the field type
                If TypeName(Value) = "Dictionary" Then
                    ' Special handling for body field with {"text": [...]} structure
                    If currentKey = "body" Then
                        ' Check if it's the common {"text": [...]} structure
                        If Value.Exists("text") Then
                            Dim textValue As Variant
                            Set textValue = Nothing
                            textValue = Value("text")
                            
                            If IsObject(textValue) And TypeName(textValue) = "Collection" Then
                                ' It's {"text": ["para1", "para2", ...]}
                                Result = ExtractTextFromArray(textValue)
                                OCRUtils.LogToFile "GetSmartJsonValue: Extracted body from {'text': [...]} structure"
                            ElseIf Not IsObject(textValue) Then
                                ' It's {"text": "simple string"}
                                Result = CStr(textValue)
                                OCRUtils.LogToFile "GetSmartJsonValue: Extracted body from {'text': 'string'} structure"
                            Else
                                ' It's {"text": {another nested object}}
                                Result = ExtractTextFromComplexStructure(textValue)
                                OCRUtils.LogToFile "GetSmartJsonValue: Extracted body from {'text': {...}} structure"
                            End If
                        Else
                            ' Body is a complex object but not {"text": ...}, use general extraction
                            Result = ExtractTextFromComplexStructure(Value)
                            OCRUtils.LogToFile "GetSmartJsonValue: Extracted body using general complex structure extraction"
                        End If
                    ElseIf currentKey = "summary" Then
                        ' Summary might also have complex structure
                        Result = ExtractTextFromComplexStructure(Value)
                    Else
                        ' Standard nested object handling for other fields
                        If Value.Exists("name") Then
                            Result = CStr(Value("name"))
                        ElseIf Value.Exists("value") Then
                            Result = CStr(Value("value"))
                        ElseIf Value.Exists("text") Then
                            Result = CStr(Value("text"))
                        Else
                            ' For non-body/summary fields, try to extract meaningful text
                            Result = ExtractFirstStringValue(Value)
                        End If
                    End If
                ElseIf TypeName(Value) = "Collection" Then
                    ' Handle arrays - especially for body content
                    If currentKey = "body" Or currentKey = "references" Then
                        Result = ExtractTextFromArray(Value)
                    Else
                        ' For other fields, join array elements
                        Result = JoinArrayElements(Value)
                    End If
                Else
                    Result = CStr(Value)
                End If
            Else
                ' It's a simple value
                Result = CStr(Value)
            End If
            
            ' If we found a non-empty value, return it
            If Result <> "" And Result <> "null" Then
                GetSmartJsonValue = Result
                Exit Function
            End If
        End If
    Next
    
    ' Default if nothing found
    GetSmartJsonValue = "Not Found"
    On Error GoTo 0
End Function

' Extract text from complex nested structures (for body/summary fields)
Private Function ExtractTextFromComplexStructure(ByVal Obj As Object) As String
    On Error Resume Next
    
    Dim Result As String
    Dim Key As Variant
    Dim Value As Variant
    
    ' First, check for common text-containing keys
    Dim textKeys As Variant
    textKeys = Array("natural_text", "text", "content", "paragraph", "value", "body_text", "summary_text", "extracted_text")
    
    ' Try each text key
    Dim textKey As Variant
    For Each textKey In textKeys
        If Obj.Exists(CStr(textKey)) Then
            Set Value = Nothing
            Value = Obj(CStr(textKey))
            
            If IsObject(Value) Then
                ' Recursive call if it's another nested object
                Result = ExtractTextFromComplexStructure(Value)
            Else
                Result = CStr(Value)
            End If
            
            If Result <> "" And Result <> "null" Then
                ExtractTextFromComplexStructure = Result
                Exit Function
            End If
        End If
    Next
    
    ' If no specific text key found, try to extract all string values
    Result = ""
    For Each Key In Obj.keys
        Set Value = Nothing
        Value = Obj(Key)
        
        If Not IsObject(Value) Then
            ' It's a simple value, check if it's meaningful text
            Dim strValue As String
            strValue = CStr(Value)
            If Len(strValue) > 10 And InStr(strValue, " ") > 0 Then
                ' Looks like actual text content
                If Result <> "" Then Result = Result & vbCrLf
                Result = Result & strValue
            End If
        ElseIf TypeName(Value) = "Collection" Then
            ' Handle nested arrays
            Dim arrText As String
            arrText = ExtractTextFromArray(Value)
            If arrText <> "" Then
                If Result <> "" Then Result = Result & vbCrLf
                Result = Result & arrText
            End If
        End If
    Next
    
    ExtractTextFromComplexStructure = Result
    On Error GoTo 0
End Function

' Extract text from arrays (for body content with multiple paragraphs)
Private Function ExtractTextFromArray(ByVal arr As Object) As String
    On Error Resume Next
    
    Dim Result As String
    Dim item As Variant
    Dim itemText As String
    
    Result = ""
    
    For Each item In arr
        If IsObject(item) Then
            ' If array contains objects, extract text from each
            If TypeName(item) = "Dictionary" Then
                itemText = ExtractTextFromComplexStructure(item)
            Else
                itemText = CStr(item)
            End If
        Else
            ' Simple value in array
            itemText = CStr(item)
        End If
        
        ' Add to result if it's meaningful text
        If itemText <> "" And itemText <> "null" Then
            If Result <> "" Then Result = Result & vbCrLf
            Result = Result & itemText
        End If
    Next
    
    ExtractTextFromArray = Result
    On Error GoTo 0
End Function

' Join array elements for simple fields
Private Function JoinArrayElements(ByVal arr As Object) As String
    On Error Resume Next
    
    Dim Result As String
    Dim item As Variant
    Dim firstItem As Boolean
    
    Result = ""
    firstItem = True
    
    For Each item In arr
        If Not IsObject(item) Then
            If firstItem Then
                firstItem = False
            Else
                Result = Result & ", "
            End If
            Result = Result & CStr(item)
        End If
    Next
    
    JoinArrayElements = Result
    On Error GoTo 0
End Function

' Extract the first meaningful string value from an object
Private Function ExtractFirstStringValue(ByVal Obj As Object) As String
    On Error Resume Next
    
    Dim Key As Variant
    Dim Value As Variant
    
    For Each Key In Obj.keys
        Set Value = Nothing
        Value = Obj(Key)
        
        If Not IsObject(Value) Then
            Dim strValue As String
            strValue = CStr(Value)
            ' Return first non-empty, meaningful string
            If strValue <> "" And strValue <> "null" And Len(strValue) > 2 Then
                ExtractFirstStringValue = strValue
                Exit Function
            End If
        End If
    Next
    
    ExtractFirstStringValue = ""
    On Error GoTo 0
End Function

' Clean Docling text content (remove image placeholders)
Private Function CleanDoclingText(ByVal rawText As String) As String
    Dim cleanedText As String
    cleanedText = rawText
    
    ' Remove image placeholders
    cleanedText = Replace(cleanedText, "<!-- image -->", "")
    
    ' Clean up excessive line breaks (including those left by removed images)
    Do While InStr(cleanedText, vbCrLf & vbCrLf & vbCrLf) > 0
        cleanedText = Replace(cleanedText, vbCrLf & vbCrLf & vbCrLf, vbCrLf & vbCrLf)
    Loop
    
    ' Clean up double line breaks that might be left after image removal
    Do While InStr(cleanedText, vbCrLf & vbCrLf & vbCrLf) > 0
        cleanedText = Replace(cleanedText, vbCrLf & vbCrLf & vbCrLf, vbCrLf & vbCrLf)
    Loop
    
    ' Trim
    CleanDoclingText = Trim(cleanedText)
End Function

' ================================
' VLM COMMUNICATION HELPERS
' ================================

' Stubs for SendTextToOllama and SendTextToOpenRouter removed,
' as ProcessDoclingWithVLM now calls the actual implementations
' in Prov_Ollama.bas and Prov_OpenRouter.bas respectively.

' Parse VLM post-processing response
Private Function ParseVLMPostProcessResponse(ByVal Response As String, ByVal provider As APIProviderType) As ocrResult
    On Error GoTo ErrorHandler
    
    Select Case provider
        Case OllamaProvider
            ParseVLMPostProcessResponse = ExtractDataFromOllamaResponse(JsonConverter.ParseJson(Response))
        Case OpenRouterProvider
            ParseVLMPostProcessResponse = ExtractDataFromOpenRouterResponse(Response, False)
        Case LMStudioProvider
            ParseVLMPostProcessResponse = ExtractDataFromLMStudioResponse(JsonConverter.ParseJson(Response))
        Case Else
            ParseVLMPostProcessResponse.ErrorMessage = "Unsupported provider for post-processing"
    End Select
    
    Exit Function
    
ErrorHandler:
    ParseVLMPostProcessResponse.Success = False
    ParseVLMPostProcessResponse.ErrorMessage = "Error parsing VLM post-process response: " & Err.Description
End Function

' ================================
' UTILITY FUNCTIONS
' ================================

' Convert JSON object to string
Private Function JsonToString(ByVal jsonObj As Object) As String
    On Error Resume Next
    JsonToString = JsonConverter.ConvertToJson(jsonObj)
    If Err.Number <> 0 Then
        JsonToString = "[Complex JSON Object]"
    End If
    On Error GoTo 0
End Function

' Validate OCR result completeness
Public Function ValidateOCRResult(ByRef Result As ocrResult) As Boolean
    ValidateOCRResult = Result.Success And _
                       Result.LetterReference <> "Not Found" And _
                       Result.LetterDate <> "Not Found" And _
                       Result.Subject <> "Not Found" And _
                       Result.Body <> ""
End Function

' Get result quality score (0-100)
Public Function GetResultQualityScore(ByRef Result As ocrResult) As Integer
    Dim score As Integer
    score = 0
    
    If Result.Success Then score = score + 20
    If Result.LetterReference <> "Not Found" And Result.LetterReference <> "" Then score = score + 20
    If Result.LetterDate <> "Not Found" And Result.LetterDate <> "" Then score = score + 15
    If Result.Subject <> "Not Found" And Result.Subject <> "" Then score = score + 15
    If Result.References <> "Not Found" And Result.References <> "" Then score = score + 10
    If Len(Result.Body) > 100 Then score = score + 20
    
    GetResultQualityScore = score
End Function

' Helper function to populate result from direct JSON
Private Function PopulateResultFromDirectJson(ByVal jsonObj As Object) As ocrResult
    Dim Result As ocrResult
    
    ' Initialize with default values
    Result.Success = True
    Result.LetterReference = "Not Found"
    Result.LetterDate = "Not Found"
    Result.Subject = "Not Found"
    Result.References = "Not Found"
    Result.Body = ""
    Result.FromText = "Not Found"
    Result.ToText = "Not Found"
    Result.Summary = ""
    Result.Tags = ""
    
    ' Populate fields from JSON
    If jsonObj.Exists("from") Then Result.FromText = jsonObj("from")
    If jsonObj.Exists("to") Then Result.ToText = jsonObj("to")
    If jsonObj.Exists("reference") Then Result.LetterReference = jsonObj("reference")
    If jsonObj.Exists("date") Then Result.LetterDate = jsonObj("date")
    If jsonObj.Exists("subject") Then Result.Subject = jsonObj("subject")
    If jsonObj.Exists("references") Then Result.References = jsonObj("references")
    If jsonObj.Exists("body") Then Result.Body = jsonObj("body")
    If jsonObj.Exists("summary") Then Result.Summary = jsonObj("summary")
    If jsonObj.Exists("tags") Then Result.Tags = jsonObj("tags")
    
    PopulateResultFromDirectJson = Result
End Function
