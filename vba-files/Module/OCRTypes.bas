Attribute VB_Name = "OCRTypes"
' OCR Types and Enums Module
' Shared type definitions for the OCR system
' Author: AI Assistant
'

Option Explicit

' ================================
' TYPE DEFINITIONS
' ================================

' OCR Result Structure
Public Type ocrResult
    FromText As String ' New field
    ToText As String   ' New field
    LetterReference As String
    LetterDate As String
    Subject As String
    References As String ' Moved for grouping
    Body As String
    Summary As String  ' New field
    Tags As String     ' New field
    Success As Boolean
    ErrorMessage As String
End Type

' ================================
' ENUMERATIONS
' ================================

' API Provider Selection
Public Enum APIProviderType
    OllamaProvider = 1      ' Local Ollama (image-based)
    OpenRouterProvider = 2  ' OpenRouter (vision or text)
    DoclingProvider = 3     ' Local Docling (OCR server - custom multipart)
    DoclingWebProvider = 4  ' Local Docling (OCR server - VBA-Web)
    LMStudioProvider = 5    ' Local LM Studio (vision models)
End Enum

' OpenRouter processing modes
Public Enum OpenRouterMode
    VisionMode = 1  ' Convert PDF to images, send to vision model
    TextMode = 2    ' Use PDF text extraction + text model
End Enum

' OCR Method Types (for unified processing)
Public Enum OCRMethodType
    VLMDirect = 1       ' Direct VLM processing (Ollama, OpenRouter)
    DoclingPlusVLM = 2  ' Docling OCR + VLM post-processing
End Enum

' ================================
' WINDOWS API DECLARATIONS
' ================================

#If VBA7 Then
    Public Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
    Public Declare PtrSafe Function GetTickCount Lib "kernel32" () As Long
#Else
    Public Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
    Public Declare Function GetTickCount Lib "kernel32" () As Long
#End If
