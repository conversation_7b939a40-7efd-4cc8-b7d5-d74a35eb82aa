Attribute VB_Name = "MultipartFormData"
'
' Robust Multipart Form Data Module for VBA
' Designed to match Python requests library behavior exactly
' Author: AI Assistant
'

Option Explicit

' ================================
' PUBLIC TYPES
' ================================

Public Type MultipartFormField
    Name As String
    Value As String
    FileName As String
    ContentType As String
    IsFile As Boolean
    fileBytes() As Byte
End Type

Public Type MultipartRequest
    Url As String
    fields() As MultipartFormField
    fieldCount As Long
    Boundary As String
    requestBody() As Byte
    ContentLength As Long
    Headers As Collection
End Type

' ================================
' PUBLIC INTERFACE
' ================================

' Create a new multipart request
Public Function CreateMultipartRequest(ByVal Url As String) As MultipartRequest
    Dim Request As MultipartRequest
    
    Request.Url = Url
    Request.Boundary = GenerateBoundary()
    Request.fieldCount = 0
    ' Initialize with one element but track with FieldCount=0 (alternative approach that works!)
    ReDim Request.fields(0 To 0)
    Set Request.Headers = New Collection
    
    ' Headers collection ready for use (no default headers needed)
    
    CreateMultipartRequest = Request
End Function

' Add a text field to the multipart request
Public Sub AddTextField(ByRef Request As MultipartRequest, ByVal fieldName As String, ByVal fieldValue As String)
    Dim newField As MultipartFormField
    
    ' Initialize field
    newField.Name = fieldName
    newField.Value = fieldValue
    newField.FileName = ""
    newField.ContentType = ""
    newField.IsFile = False
    ReDim newField.fileBytes(0 To 0) ' Empty array (using working approach)
    
    ' Resize fields array and add new field
    If Request.fieldCount = 0 Then
        ReDim Request.fields(0 To 0)
    Else
        ReDim Preserve Request.fields(0 To Request.fieldCount)
    End If
    
    Request.fields(Request.fieldCount) = newField
    Request.fieldCount = Request.fieldCount + 1
End Sub

' Add a file field to the multipart request
Public Sub AddFileField(ByRef Request As MultipartRequest, ByVal fieldName As String, ByVal FileName As String, ByVal ContentType As String, ByRef fileBytes() As Byte)
    Dim newField As MultipartFormField
    Dim i As Long
    
    ' Initialize field
    newField.Name = fieldName
    newField.Value = ""
    newField.FileName = FileName
    newField.ContentType = ContentType
    newField.IsFile = True
    
    ' Copy file bytes safely
    On Error GoTo SafeCopyBytes
    If UBound(fileBytes) >= LBound(fileBytes) Then
        ReDim newField.fileBytes(LBound(fileBytes) To UBound(fileBytes))
        For i = LBound(fileBytes) To UBound(fileBytes)
            newField.fileBytes(i) = fileBytes(i)
        Next i
    Else
        ReDim newField.fileBytes(0 To 0) ' Empty array (using working approach)
    End If
    GoTo ContinueFileField
    
SafeCopyBytes:
    ReDim newField.fileBytes(0 To 0) ' Empty array on error (using working approach)
    
ContinueFileField:
    ' Resize fields array and add new field
    If Request.fieldCount = 0 Then
        ReDim Request.fields(0 To 0)
    Else
        ReDim Preserve Request.fields(0 To Request.fieldCount)
    End If
    
    Request.fields(Request.fieldCount) = newField
    Request.fieldCount = Request.fieldCount + 1
End Sub

' Build the complete multipart request body
Public Function BuildRequestBody(ByRef Request As MultipartRequest) As Boolean
    On Error GoTo ErrorHandler
    
    Dim bodyParts As Collection
    Dim totalSize As Long
    Dim currentPos As Long
    Dim i As Long, j As Long
    Dim Part As Variant
    
    Set bodyParts = New Collection
    
    ' Build each field part using FieldCount
    If Request.fieldCount > 0 Then
        For i = 0 To Request.fieldCount - 1
            If Request.fields(i).IsFile Then
                bodyParts.Add BuildFilePart(Request.Boundary, Request.fields(i))
            Else
                bodyParts.Add BuildTextPart(Request.Boundary, Request.fields(i))
            End If
        Next i
    End If
    
    ' Add closing boundary
    bodyParts.Add StringToAsciiBytes("--" & Request.Boundary & "--" & vbCrLf)
    
    ' Calculate total size
    totalSize = 0
    For Each Part In bodyParts
        totalSize = totalSize + UBound(Part) + 1
    Next Part
    
    ' Combine all parts
    ReDim Request.requestBody(totalSize - 1)
    currentPos = 0
    
    For Each Part In bodyParts
        For j = 0 To UBound(Part)
            Request.requestBody(currentPos) = Part(j)
            currentPos = currentPos + 1
        Next j
    Next Part
    
    Request.ContentLength = totalSize
    BuildRequestBody = True
    Exit Function
    
ErrorHandler:
    BuildRequestBody = False
End Function

' Send the multipart request and return response
Public Function SendMultipartRequest(ByRef Request As MultipartRequest) As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim adoStream As Object
    Dim contentTypeHeader As String
    Dim headerKey As Variant
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    Set adoStream = CreateObject("ADODB.Stream")
    
    ' Build request body if not already built
    If Request.ContentLength = 0 Then
        If Not BuildRequestBody(Request) Then
            SendMultipartRequest = ""
            Exit Function
        End If
    End If
    
    ' Prepare stream with request body
    adoStream.Type = 1 ' Binary
    adoStream.Open
    adoStream.Write Request.requestBody
    adoStream.Position = 0
    
    ' Open HTTP connection
    Http.Open "POST", Request.Url, False
    
    ' Set Content-Type header with boundary (match Python format exactly)
    contentTypeHeader = "multipart/form-data; boundary=" & Request.Boundary
    Http.SetRequestHeader "Content-Type", contentTypeHeader
    
    ' Set additional headers
    For Each headerKey In Request.Headers
        Http.SetRequestHeader CStr(headerKey), Request.Headers(headerKey)
    Next headerKey
    
    ' Send request
    Http.Send adoStream
    
    ' Return response
    If Http.status = 200 Then
        SendMultipartRequest = Http.responseText
    Else
        SendMultipartRequest = ""
        ' Log error for debugging
        OCRUtils.LogToFile "MultipartFormData: HTTP Error " & Http.status & ": " & Http.responseText
    End If
    
    ' Cleanup
    adoStream.Close
    Set adoStream = Nothing
    Set Http = Nothing
    Exit Function
    
ErrorHandler:
    SendMultipartRequest = ""
    OCRUtils.LogToFile "MultipartFormData: Error in SendMultipartRequest: " & Err.Description
End Function

' ================================
' PRIVATE HELPER FUNCTIONS
' ================================

' Generate a unique boundary string (match Python format)
Private Function GenerateBoundary() As String
    ' Python uses a format like: ----formdata-requests-1a2b3c4d5e6f7890
    Dim timestamp As String
    Dim randomPart As String
    
    timestamp = Format(Now, "yyyymmddhhnnss")
    randomPart = GenerateRandomHex(16)
    
    GenerateBoundary = "----formdata-vba-" & timestamp & randomPart
End Function

' Generate random hex string
Private Function GenerateRandomHex(ByVal Length As Long) As String
    Dim Result As String
    Dim i As Long
    Dim hexChars As String
    
    hexChars = "0123456789abcdef"
    
    For i = 1 To Length
        Result = Result & Mid(hexChars, Int(Rnd() * 16) + 1, 1)
    Next i
    
    GenerateRandomHex = Result
End Function

' Build a text field part
Private Function BuildTextPart(ByVal Boundary As String, ByRef field As MultipartFormField) As Byte()
    Dim content As String
    
    content = "--" & Boundary & vbCrLf & _
              "Content-Disposition: form-data; name=""" & field.Name & """" & vbCrLf & vbCrLf & _
              field.Value & vbCrLf
              
    BuildTextPart = StringToAsciiBytes(content)
End Function

' Build a file field part
Private Function BuildFilePart(ByVal Boundary As String, ByRef field As MultipartFormField) As Byte()
    Dim header As String
    Dim headerBytes() As Byte
    Dim Result() As Byte
    Dim totalSize As Long
    Dim i As Long
    
    ' Build header
    header = "--" & Boundary & vbCrLf & _
             "Content-Disposition: form-data; name=""" & field.Name & """; filename=""" & field.FileName & """" & vbCrLf & _
             "Content-Type: " & field.ContentType & vbCrLf & vbCrLf
             
    headerBytes = StringToAsciiBytes(header)
    
    ' Calculate total size
    totalSize = UBound(headerBytes) + 1 + UBound(field.fileBytes) + 1 + 2 ' +2 for CRLF
    
    ' Combine header, file bytes, and CRLF
    ReDim Result(totalSize - 1)
    
    ' Copy header
    For i = 0 To UBound(headerBytes)
        Result(i) = headerBytes(i)
    Next i
    
    ' Copy file bytes
    For i = 0 To UBound(field.fileBytes)
        Result(UBound(headerBytes) + 1 + i) = field.fileBytes(i)
    Next i
    
    ' Add CRLF
    Result(totalSize - 2) = 13 ' CR
    Result(totalSize - 1) = 10 ' LF
    
    BuildFilePart = Result
End Function

' Convert string to ASCII bytes (no BOM)
Private Function StringToAsciiBytes(ByVal inputString As String) As Byte()
    On Error GoTo ErrorHandler
    StringToAsciiBytes = StrConv(inputString, vbFromUnicode)
    Exit Function
ErrorHandler:
    ReDim StringToAsciiBytes(0 To 0) ' Use working approach
End Function

' Read file to byte array
Public Function ReadFileToBytes(ByVal FilePath As String) As Byte()
    On Error GoTo ErrorHandler
    
    Dim FileNum As Integer
    Dim fileBytes() As Byte
    
    FileNum = FreeFile
    Open FilePath For Binary Access Read As FileNum
    
    If LOF(FileNum) > 0 Then
        ReDim fileBytes(LOF(FileNum) - 1)
        Get FileNum, , fileBytes
    Else
        ReDim fileBytes(0 To 0) ' Empty array (using working approach)
    End If
    
    Close FileNum
    ReadFileToBytes = fileBytes
    Exit Function
    
ErrorHandler:
    If FileNum > 0 Then Close FileNum
    ReDim ReadFileToBytes(0 To 0) ' Use working approach
End Function

' Extract filename from path
Public Function ExtractFileName(ByVal fullPath As String) As String
    Dim pos As Integer
    pos = InStrRev(fullPath, "\")
    If pos > 0 Then
        ExtractFileName = Mid(fullPath, pos + 1)
    Else
        ExtractFileName = fullPath
    End If
End Function
