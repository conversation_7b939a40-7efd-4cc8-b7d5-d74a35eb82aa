Attribute VB_Name = "FormLoadingHelpers"
Option Explicit

' Module-level variable to track form reference
Private m_SettingsForm As Object

' Public entry point for lazy loading provider data
Public Sub LazyLoadProviderData()
    On Error GoTo ErrorHandler
    
    ' Check if settings form is still loaded
    If m_SettingsForm Is Nothing Then
        OCRUtils.LogToFile "FormLoadingHelpers: Settings form not available for update"
        Exit Sub
    End If
    
    OCRUtils.LogToFile "FormLoadingHelpers: Starting lazy load of provider data"
    
    ' Disable logging for performance
    OCRUtils.SetLogging False
    
    ' Update progress
    ProgressHelpers.ShowProgress "Checking provider availability..."
    
    ' Refresh local models (this checks availability)
    UserFromSettings.RefreshLocalModels m_SettingsForm
    
    ' Update progress
    ProgressHelpers.ShowProgress "Updating provider lists..."
    
    ' Get configured providers
    Dim configuredOCRProvider As String
    Dim configuredExtractionProvider As String
    configuredOCRProvider = OCRConfig.GetWorkflowSetting("ocr_provider", "")
    configuredExtractionProvider = OCRConfig.GetWorkflowSetting("extraction.provider", "")
    
    ' Re-populate OCR provider list and select the configured one
    UserFromSettings.PopulateProviderList m_SettingsForm.lstOCRProvider, False
    UserFromSettings.SetListboxByValue m_SettingsForm.lstOCRProvider, configuredOCRProvider
    
    ' Re-populate Extraction provider list and select the configured one
    UserFromSettings.PopulateProviderList m_SettingsForm.lstExtractionProvider, True
    UserFromSettings.SetListboxByValue m_SettingsForm.lstExtractionProvider, configuredExtractionProvider
    
    ProgressHelpers.ShowProgress "Loading provider models..."
    
    ' Update model lists based on refreshed data
    UserFromSettings.UpdateModelListForOCR m_SettingsForm
    UserFromSettings.UpdateModelListForExtraction m_SettingsForm
    
    ' Hide progress
    ProgressHelpers.HideProgress
    
    ' Re-enable logging
    OCRUtils.SetLogging True
    
    OCRUtils.LogToFile "FormLoadingHelpers: Lazy load completed"
    
    ' Clear form reference
    Set m_SettingsForm = Nothing
    
    Exit Sub
    
ErrorHandler:
    ProgressHelpers.HideProgress
    OCRUtils.SetLogging True
    OCRUtils.LogToFile "FormLoadingHelpers: Error in LazyLoadProviderData: " & Err.Description
    Set m_SettingsForm = Nothing
End Sub

' Set the form reference for lazy loading
Public Sub SetFormForLazyLoad(frm As Object)
    Set m_SettingsForm = frm
End Sub

' Quick initialization for settings form (uses cached data)
Public Sub QuickInitializeSettingsForm(frm As frmSettings)
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "FormLoadingHelpers: Starting quick initialization"
    
    ' Disable logging for speed
    OCRUtils.SetLogging False
    
    ' Show loading message
    ProgressHelpers.ShowProgress "Loading settings from cache..."
    
    ' Use quick checks (500ms timeout) for initial load
    Dim ollamaAvailable As Boolean
    Dim lmStudioAvailable As Boolean
    
    ' Check cached status first
    Dim cacheResult As Variant
    
    ' Check Ollama cache
    cacheResult = OCRConfig.GetCachedProviderStatus(OCRConfig.PID_OLLAMA)
    If cacheResult(1) Then
        ollamaAvailable = cacheResult(0)
    Else
        ' Do quick check if not cached
        ollamaAvailable = Prov_Ollama.isOllamaRunning(True)
    End If
    
    ' Check LM Studio cache
    cacheResult = OCRConfig.GetCachedProviderStatus(OCRConfig.PID_LMSTUDIO)
    If cacheResult(1) Then
        lmStudioAvailable = cacheResult(0)
    Else
        ' Do quick check if not cached
        lmStudioAvailable = Prov_LMStudio.isLMStudioRunning(True)
    End If
    
    ' Update UserFromSettings cached status
    UserFromSettings.SetProviderAvailability ollamaAvailable, lmStudioAvailable
    
    ' Populate provider lists based on cached/quick check
    ProgressHelpers.ShowProgress "Loading providers..."
    PopulateProviderList frm.lstOCRProvider, False
    PopulateProviderList frm.lstExtractionProvider, True
    
    ' Load saved settings (this will also populate and select models)
    ProgressHelpers.ShowProgress "Applying saved settings..."
    UserFromSettings.LoadAndApplySettings frm
    
    ' Try to use cached model lists if available to speed up loading
    ' This will override the models loaded by LoadAndApplySettings if cache is available
    ProgressHelpers.ShowProgress "Loading models from cache..."
    LoadCachedModelsToForm frm
    
    ' Hide progress - form is now usable
    ProgressHelpers.HideProgress
    
    ' Re-enable logging
    OCRUtils.SetLogging True
    
    ' Store form reference for lazy loading
    SetFormForLazyLoad frm
    
    ' Schedule background update of provider data
    ' Application.OnTime Now, "LazyLoadProviderData" ' Moved to UserForm_Activate
    
    OCRUtils.LogToFile "FormLoadingHelpers: Quick initialization completed, background load scheduled"
    
    Exit Sub
    
ErrorHandler:
    ProgressHelpers.HideProgress
    OCRUtils.SetLogging True
    OCRUtils.LogToFile "FormLoadingHelpers: Error in QuickInitializeSettingsForm: " & Err.Description
    ' Fall back to regular initialization
    UserFromSettings.InitializeSettingsForm frm
End Sub

' Load cached models to form controls
Private Sub LoadCachedModelsToForm(frm As frmSettings)
    On Error Resume Next ' Don't fail if cache is empty
    
    Dim cacheResult As Variant
    Dim models As Variant
    Dim i As Long
    
    ' Load OCR models from cache
    If frm.lstOCRProvider.ListIndex <> -1 Then
        cacheResult = OCRConfig.GetCachedModelList(frm.lstOCRProvider.Value)
        If cacheResult(1) And IsArray(cacheResult(0)) Then
            ' Use cached models
            frm.lstOCRModel.Clear
            models = cacheResult(0)
            For i = LBound(models) To UBound(models)
                If models(i) <> "" Then
                    frm.lstOCRModel.AddItem models(i)
                End If
            Next i
            If frm.lstOCRModel.ListCount > 0 Then frm.lstOCRModel.ListIndex = 0
        End If
    End If
    
    ' Load extraction models from cache
    If frm.lstExtractionProvider.ListIndex <> -1 Then
        cacheResult = OCRConfig.GetCachedModelList(frm.lstExtractionProvider.Value)
        If cacheResult(1) And IsArray(cacheResult(0)) Then
            ' Use cached models
            frm.lstExtractionModel.Clear
            models = cacheResult(0)
            For i = LBound(models) To UBound(models)
                If models(i) <> "" Then
                    frm.lstExtractionModel.AddItem models(i)
                End If
            Next i
            If frm.lstExtractionModel.ListCount > 0 Then frm.lstExtractionModel.ListIndex = 0
        End If
    End If
    
    On Error GoTo 0
End Sub
