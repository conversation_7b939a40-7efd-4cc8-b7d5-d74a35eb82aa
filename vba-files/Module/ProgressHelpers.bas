Attribute VB_Name = "ProgressHelpers"
Option Explicit

Private m_frmProgressInstance As frmProgress

' Display or update a modeless progress dialog with the given message
Public Sub ShowProgress(statusText As String)
    On Error GoTo ErrorHandler

    If m_frmProgressInstance Is Nothing Then
        Set m_frmProgressInstance = New frmProgress
    End If
    
    m_frmProgressInstance.lblProgress.Caption = statusText
    
    If Not m_frmProgressInstance.Visible Then
        m_frmProgressInstance.Show vbModeless
    End If
    
    DoEvents ' Keep UI responsive
    
    Exit Sub
ErrorHandler:
    ' Handle error, perhaps log it
    OCRUtils.LogToFile "ProgressHelpers: Error in ShowProgress: " & Err.Description
End Sub

' Hide the progress dialog and unload to clear resources
Public Sub HideProgress()
    On Error GoTo ErrorHandler
    
    If Not m_frmProgressInstance Is Nothing Then
        Unload m_frmProgressInstance
        Set m_frmProgressInstance = Nothing
    End If
    
    Exit Sub
ErrorHandler:
    ' Handle error, perhaps log it
    OCRUtils.LogToFile "ProgressHelpers: Error in HideProgress: " & Err.Description
End Sub
