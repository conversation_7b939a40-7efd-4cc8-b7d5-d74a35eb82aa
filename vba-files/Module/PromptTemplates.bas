Attribute VB_Name = "PromptTemplates"
'
' OCR Prompt Templates Module - SIMPLIFIED
' Universal prompts for all VLM providers and Docling post-processing
' Author: AI Assistant
'

Option Explicit

' ================================
' CORE UNIVERSAL PROMPTS (3 TEMPLATES ONLY)
' ================================

' Template 1: Universal OCR-Only Prompt (Extract Fields = FALSE)
Public Function GetUniversalOCRPrompt(ByVal pageCount As Integer) As String
    GetUniversalOCRPrompt = "You are an expert OCR system specialized in construction project letters. " & _
        "Read and transcribe ALL text from this " & pageCount & "-page document exactly as it appears. " & _
        "Preserve the original formatting, line breaks, paragraphs, and structure. " & _
        "Include ALL content from every page without summarizing, editing, or omitting any text. " & _
        "Return only the complete transcribed text content."
End Function

' Template 2: Universal OCR + Field Extraction Prompt (Extract Fields = TRUE)
Public Function GetUniversalOCRExtractionPrompt(ByVal pageCount As Integer, fieldSelection As Object) As String
    Dim prompt As String
    
    ' Main instruction
    prompt = "You are an expert OCR system specialized in construction project letters. " & _
             "Read and extract structured information from this " & pageCount & "-page document." & vbLf & vbLf
    
    ' Add the structured extraction requirements
    prompt = prompt & BuildJSONStructureString(fieldSelection) & vbLf
    
    ' Critical formatting rules
    prompt = prompt & "## Critical JSON Formatting Rules" & vbLf & _
                      "- Use standard JSON syntax only" & vbLf & _
                      "- All string values MUST be enclosed in double quotes ("""")" & vbLf & _
                      "- Do NOT use backticks (`) or single quotes (') for JSON strings" & vbLf & _
                      "- Ensure proper escaping of quotes within string values" & vbLf & _
                      "- Return ONLY the JSON object, no additional text" & vbLf
    
    GetUniversalOCRExtractionPrompt = prompt
End Function

' Template 3: Post-Processing Extraction Prompt (Docling OCR → VLM)
Public Function GetPostProcessExtractionPrompt(fieldSelection As Object) As String
    Dim prompt As String
    
    ' Main instruction
    prompt = "You are an expert document analyzer specialized in construction project letters. " & _
             "Analyze the following OCR text and extract structured information." & vbLf & vbLf
    
    ' Add the structured extraction requirements
    prompt = prompt & BuildJSONStructureString(fieldSelection) & vbLf
    
    ' Critical formatting rules
    prompt = prompt & "## Critical JSON Formatting Rules" & vbLf & _
                      "- Use standard JSON syntax only" & vbLf & _
                      "- All string values MUST be enclosed in double quotes ("""")" & vbLf & _
                      "- Do NOT use backticks (`) or single quotes (') for JSON strings" & vbLf & _
                      "- Ensure proper escaping of quotes within string values" & vbLf & _
                      "- Return ONLY the JSON object, no additional text" & vbLf & vbLf
    
    prompt = prompt & "## OCR TEXT TO ANALYZE:" & vbLf & _
                      "=================================" & vbLf
    
    GetPostProcessExtractionPrompt = prompt
End Function

' Helper function to build the JSON structure string based on selected fields
Private Function BuildJSONStructureString(fieldSelection As Object) As String
    Dim prompt As String
    
    ' Document Type Information
    prompt = "## Document Type Information" & vbLf & _
             "Document type: Construction Project Letter" & vbLf & _
             "Expected structure: Formal business letter with letterhead, reference numbers, date, recipient details, subject, body, and signature" & vbLf & _
             "Key elements to identify: Sender organization, recipient organization, reference numbers, formal subject line, referenced documents" & vbLf & vbLf
    
    ' Data Extraction Requirements
    prompt = prompt & "## Data Extraction Requirements" & vbLf & _
                      "Extract the following information that appears directly in the letter:" & vbLf
    
    If fieldSelection("From") Then
        prompt = prompt & "- Sender organization: The company/organization sending the letter (check letterhead and signature block)" & vbLf
    End If
    If fieldSelection("To") Then
        prompt = prompt & "- Recipient organization: The company/organization receiving the letter (check 'To:' line or address block)" & vbLf
    End If
    If fieldSelection("LetterRef") Then
        prompt = prompt & "- Letter reference: The alphanumeric reference code (look for 'Our Reference:', 'Our Ref:', 'Ref:', or 'Ref. No.:' - NOT the subject line)" & vbLf
    End If
    If fieldSelection("LetterDate") Then
        prompt = prompt & "- Letter date: The date the letter was written (usually near the top, before 'Dear Sir/Madam')" & vbLf
    End If
    If fieldSelection("Subject") Then
        prompt = prompt & "- Subject line: The formal subject of the letter (text after 'Subject:', 'Re:', or 'RE:')" & vbLf
    End If
    If fieldSelection("References") Then
        prompt = prompt & "- Referenced documents: Any documents mentioned in a 'References:' section (usually numbered 1., 2., etc.)" & vbLf
    End If
    If fieldSelection("Body") Then
        prompt = prompt & "- Letter body: Complete text from greeting to closing, all pages included" & vbLf
    End If
    
    ' Generated Analysis Requirements
    If fieldSelection("Summary") Or fieldSelection("Tags") Then
        prompt = prompt & vbLf & "## Generated Analysis Requirements" & vbLf & _
                          "Generate the following based on your understanding of the letter content:" & vbLf
        
        If fieldSelection("Summary") Then
            prompt = prompt & "- Summary: CREATE a 2-3 sentence summary describing the letter's main purpose and key action items (DO NOT look for existing summary text)" & vbLf
        End If
        If fieldSelection("Tags") Then
            prompt = prompt & "- Keywords: GENERATE 3-5 specific, relevant keywords/topics based on the letter content (DO NOT extract existing keywords)" & vbLf
        End If
    End If
    
    prompt = prompt & vbLf
    
    ' Special Processing Instructions
    prompt = prompt & "## Special Processing Instructions" & vbLf & _
                      "- Letter reference is typically formatted like 'BNDP3-ZZ-CSCEC-MMD-LT-01435' with hyphens" & vbLf & _
                      "- Do NOT confuse the letter reference with the subject line" & vbLf & _
                      "- References section may contain multiple numbered items - extract all of them" & vbLf & _
                      "- For EXTRACTED fields only: If information is not found in the document, use ""Not Found"" as the value" & vbLf & _
                      "- For GENERATED fields (summary/tags): Always provide meaningful content based on the letter" & vbLf & _
                      "- Preserve technical details, specifications, and contract clauses in the body" & vbLf & _
                      "- Extract information exactly as written without paraphrasing" & vbLf & vbLf
    
    ' Output Format with Example
    prompt = prompt & "## Output Format" & vbLf & _
                      "Return results in the following JSON structure:" & vbLf & vbLf
    
    ' Build example JSON with actual field selections
    prompt = prompt & BuildExampleJSON(fieldSelection) & vbLf
    
    BuildJSONStructureString = prompt
End Function

' Helper function to build example JSON based on field selection
Private Function BuildExampleJSON(fieldSelection As Object) As String
    Dim json As String
    Dim firstField As Boolean
    firstField = True
    
    json = "{" & vbLf
    
    If fieldSelection("From") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""from"": ""Mott MacDonald Limited"""
    End If
    
    If fieldSelection("To") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""to"": ""China State Construction Engineering Corporation"""
    End If
    
    If fieldSelection("LetterRef") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""letter_reference"": ""BNDP3-ZZ-MMD-CSCEC-LT-02064"""
    End If
    
    If fieldSelection("LetterDate") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""letter_date"": ""2025-05-05"""
    End If
    
    If fieldSelection("Subject") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""subject"": ""Phase-3 Cost Impact due to Additional Blockwork Enclosure"""
    End If
    
    If fieldSelection("References") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""references"": ""BNDP3-ZZ-CSCEC-MMD-LT-01435 dated 4th March 2025, ADDC inspection dated 9th August 2024"""
    End If
    
    If fieldSelection("Body") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""body"": ""Dear Sir,\n\nWe refer to your letter referenced Asite-1 regarding the cost and time impact..."""
    End If
    
    If fieldSelection("Summary") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""summary"": ""Engineer clarifies that blockwork requirements originate from ADDC inspection, not the Engineer. Requests contractor to provide cost and time impact justification for the additional work."""
    End If
    
    If fieldSelection("Tags") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""tags"": ""blockwork, MDBS enclosure, ADDC inspection, villa construction, Phase 3, cost impact, contractor claim"""
    End If
    
    json = json & vbLf & "}"
    
    BuildExampleJSON = json
End Function

' Alternative minimal version for very small models
Private Function BuildMinimalJSONStructureString(fieldSelection As Object) As String
    Dim prompt As String
    
    prompt = "Extract and analyze this construction letter into JSON format." & vbLf & vbLf
    
    prompt = prompt & "EXTRACT FROM LETTER:" & vbLf
    
    If fieldSelection("From") Then
        prompt = prompt & "- from: Company sending the letter (from letterhead)" & vbLf
    End If
    
    If fieldSelection("To") Then
        prompt = prompt & "- to: Company receiving the letter (from 'To:' line)" & vbLf
    End If
    
    If fieldSelection("LetterRef") Then
        prompt = prompt & "- letter_reference: Find text after 'Our Reference:' or 'Our Ref:' (example: BNDP3-ZZ-MMD-CSCEC-LT-02064)" & vbLf
    End If
    
    If fieldSelection("LetterDate") Then
        prompt = prompt & "- letter_date: Date near top of letter (format: YYYY-MM-DD)" & vbLf
    End If
    
    If fieldSelection("Subject") Then
        prompt = prompt & "- subject: Text after 'Subject:' or 'Re:'" & vbLf
    End If
    
    If fieldSelection("References") Then
        prompt = prompt & "- references: Find section labeled 'References:' and list all numbered items (1., 2., etc.)" & vbLf
    End If
    
    If fieldSelection("Body") Then
        prompt = prompt & "- body: Full letter text from greeting to signature" & vbLf
    End If
    
    If fieldSelection("Summary") Or fieldSelection("Tags") Then
        prompt = prompt & vbLf & "GENERATE BASED ON UNDERSTANDING:" & vbLf
        
        If fieldSelection("Summary") Then
            prompt = prompt & "- summary: CREATE 2-3 sentence summary of letter's purpose (always generate this)" & vbLf
        End If
        
        If fieldSelection("Tags") Then
            prompt = prompt & "- tags: CREATE 3-5 relevant keywords based on content (always generate this)" & vbLf
        End If
    End If
    
    prompt = prompt & vbLf & "Use ""Not Found"" ONLY for extracted fields that are missing." & vbLf & vbLf
    
    prompt = prompt & "EXAMPLE OUTPUT:" & vbLf
    prompt = prompt & BuildMinimalExampleJSON(fieldSelection)
    
    BuildMinimalJSONStructureString = prompt
End Function

' Build minimal example JSON for small models
Private Function BuildMinimalExampleJSON(fieldSelection As Object) As String
    Dim json As String
    Dim firstField As Boolean
    firstField = True
    
    json = "{" & vbLf
    
    If fieldSelection("LetterRef") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""letter_reference"": ""BNDP3-ZZ-MMD-CSCEC-LT-02064"""
    End If
    
    If fieldSelection("References") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""references"": ""Contract ABC-123 dated 1 Jan 2024, Meeting minutes MM-456"""
    End If
    
    If fieldSelection("Summary") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""summary"": ""Contractor requests approval for design changes and additional time extension. Engineer to review technical justification and cost breakdown."""
    End If
    
    If fieldSelection("Tags") Then
        If Not firstField Then json = json & "," & vbLf Else firstField = False
        json = json & "  ""tags"": ""design change, time extension, approval request, cost variation, technical review"""
    End If
    
    json = json & vbLf & "}"
    
    BuildMinimalExampleJSON = json
End Function


' ================================
' UNIVERSAL PROMPT BUILDERS (ALL PROVIDERS)
' ================================

' Universal builder for ALL VLM providers (Ollama, OpenRouter Vision, etc.)
Public Function BuildUniversalVLMPrompt(ByVal pageCount As Integer) As String
    Dim fieldSelection As Object
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections
    
    ' Always build extraction prompts (controlled by workflow mode)
    If True Then
        BuildUniversalVLMPrompt = GetUniversalOCRExtractionPrompt(pageCount, fieldSelection)
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR+EXTRACTION prompt (Extract Fields = TRUE)"
    Else
        BuildUniversalVLMPrompt = GetUniversalOCRPrompt(pageCount)
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR-ONLY prompt (Extract Fields = FALSE)"
    End If
End Function

' Universal builder for ALL text-based providers (OpenRouter PDF, etc.)
Public Function BuildUniversalTextPrompt() As String
    Dim fieldSelection As Object
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections

    ' Always build extraction prompts (controlled by workflow mode)
    If True Then
        BuildUniversalTextPrompt = GetUniversalOCRExtractionPrompt(1, fieldSelection) ' Page count irrelevant for PDF input
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR+EXTRACTION prompt for text mode (Extract Fields = TRUE)"
    Else
        BuildUniversalTextPrompt = GetUniversalOCRPrompt(1) ' Page count irrelevant for PDF input
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR-ONLY prompt for text mode (Extract Fields = FALSE)"
    End If
End Function

' Builder for Docling post-processing (always extraction)
Public Function BuildDoclingPostProcessPrompt(ByVal ocrText As String) As String
    Dim fieldSelection As Object
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections
    
    BuildDoclingPostProcessPrompt = GetPostProcessExtractionPrompt(fieldSelection) & ocrText
    OCRUtils.LogToFile "PromptTemplates: Using POST-PROCESS EXTRACTION prompt for Docling"
End Function

' Template 4: Field Extraction from Raw OCR Text (Two-Step Workflow)
Public Function BuildFieldExtractionPrompt(ByVal ocrText As String) As String
    Dim fieldSelection As Object
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections
    
    Dim prompt As String
    
    ' Main instruction
    prompt = "You are an expert document analyzer specialized in construction project letters. " & _
             "Analyze the following OCR text to extract information AND generate analytical insights." & vbLf & vbLf
    
    ' Add the structured extraction requirements
    prompt = prompt & BuildJSONStructureString(fieldSelection) & vbLf
    
    ' Critical formatting rules
    prompt = prompt & "## Critical JSON Formatting Rules" & vbLf & _
                      "- Use standard JSON syntax only" & vbLf & _
                      "- All string values MUST be enclosed in double quotes ("""")" & vbLf & _
                      "- Do NOT use backticks (`) or single quotes (') for JSON strings" & vbLf & _
                      "- Ensure proper escaping of quotes within string values" & vbLf & _
                      "- Return ONLY the JSON object, no additional text" & vbLf & vbLf
    
    prompt = prompt & "## OCR TEXT TO ANALYZE:" & vbLf & _
                      "=================================" & vbLf & _
                      ocrText & vbLf & _
                      "================================="
    
    BuildFieldExtractionPrompt = prompt
End Function

' ================================
' LEGACY COMPATIBILITY WRAPPERS
' ================================

' These functions maintain backward compatibility while using the new universal templates

Public Function BuildOllamaPrompt(ByVal pageCount As Integer) As String
    BuildOllamaPrompt = BuildUniversalVLMPrompt(pageCount)
End Function

Public Function BuildLMStudioPrompt(ByVal pageCount As Integer) As String
    BuildLMStudioPrompt = BuildUniversalVLMPrompt(pageCount)
End Function

Public Function BuildOpenRouterVisionPrompt(ByVal pageCount As Integer) As String
    BuildOpenRouterVisionPrompt = BuildUniversalVLMPrompt(pageCount)
End Function

Public Function BuildOpenRouterTextPrompt() As String
    BuildOpenRouterTextPrompt = BuildUniversalTextPrompt()
End Function

' ================================
' UTILITY FUNCTIONS
' ================================

' Validate that a prompt contains required elements for extraction
Public Function ValidateExtractionPrompt(ByVal prompt As String) As Boolean
    Dim hasJSON As Boolean
    Dim hasFields As Boolean
    
    hasJSON = (InStr(prompt, "JSON") > 0 Or InStr(prompt, "json") > 0)
    hasFields = (InStr(prompt, "letter_reference") > 0 And InStr(prompt, "letter_date") > 0 And InStr(prompt, "subject") > 0)
    
    ValidateExtractionPrompt = hasJSON And hasFields
End Function

' Get the expected JSON structure for validation (now dynamic)
Public Function GetExpectedJSONStructure() As String
    Dim fieldSelection As Object
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections
    
    Dim sb As Object ' Using a Scripting.Dictionary as a string builder
    Set sb = CreateObject("Scripting.Dictionary")
    Dim firstField As Boolean
    firstField = True
    
    sb.Add "json_start", "{" & vbLf

    If fieldSelection("From") Then
        If Not firstField Then sb.Add "comma_from", "," & vbLf Else firstField = False
        sb.Add "field_from", "  ""from"": ""string"""
    End If
    If fieldSelection("To") Then
        If Not firstField Then sb.Add "comma_to", "," & vbLf Else firstField = False
        sb.Add "field_to", "  ""to"": ""string"""
    End If
    If fieldSelection("LetterRef") Then
        If Not firstField Then sb.Add "comma_letter_ref", "," & vbLf Else firstField = False
        sb.Add "field_letter_ref", "  ""letter_reference"": ""string"""
    End If
    If fieldSelection("LetterDate") Then
        If Not firstField Then sb.Add "comma_letter_date", "," & vbLf Else firstField = False
        sb.Add "field_letter_date", "  ""letter_date"": ""YYYY-MM-DD"""
    End If
    If fieldSelection("Subject") Then
        If Not firstField Then sb.Add "comma_subject", "," & vbLf Else firstField = False
        sb.Add "field_subject", "  ""subject"": ""string"""
    End If
    If fieldSelection("References") Then
        If Not firstField Then sb.Add "comma_references", "," & vbLf Else firstField = False
        sb.Add "field_references", "  ""references"": ""string"""
    End If
    If fieldSelection("Body") Then
        If Not firstField Then sb.Add "comma_body", "," & vbLf Else firstField = False
        sb.Add "field_body", "  ""body"": ""string"""
    End If
    If fieldSelection("Summary") Then
        If Not firstField Then sb.Add "comma_summary", "," & vbLf Else firstField = False
        sb.Add "field_summary", "  ""summary"": ""string"""
    End If
    If fieldSelection("Tags") Then
        If Not firstField Then sb.Add "comma_tags", "," & vbLf Else firstField = False
        sb.Add "field_tags", "  ""tags"": ""string"""
    End If
    
    sb.Add "json_end", vbLf & "}"
    
    Dim Key As Variant, tempStr As String
    For Each Key In sb.keys
        tempStr = tempStr & sb(Key)
    Next Key
    GetExpectedJSONStructure = tempStr
End Function

' Get prompt statistics for debugging
Public Function GetPromptStats() As String
    Dim fieldSelection As Object
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates() ' Get current field selections

    GetPromptStats = "Simplified Prompt Templates:" & vbCrLf & _
                    "- Universal OCR: " & Len(GetUniversalOCRPrompt(1)) & " chars" & vbCrLf & _
                    "- Universal OCR+Extraction: " & Len(GetUniversalOCRExtractionPrompt(1, fieldSelection)) & " chars" & vbCrLf & _
                    "- Post-Process Extraction: " & Len(GetPostProcessExtractionPrompt(fieldSelection)) & " chars" & vbCrLf & _
                    "- Field Extraction (Two-Step): " & Len(BuildFieldExtractionPrompt("[sample text]")) & " chars"
End Function

' Check if model is considered "small" based on name patterns
Private Function IsSmallModel(modelName As String) As Boolean
    Dim smallPatterns As Variant
    smallPatterns = Array("1b", "3b", "7b", "8b", "mini", "small", "tiny")
    
    Dim pattern As Variant
    For Each pattern In smallPatterns
        If InStr(1, LCase(modelName), CStr(pattern)) > 0 Then
            IsSmallModel = True
            Exit Function
        End If
    Next pattern
    
    IsSmallModel = False
End Function

' Get optimized JSON structure based on model size
Public Function GetOptimizedJSONStructure(fieldSelection As Object, Optional modelName As String = "") As String
    If IsSmallModel(modelName) Then
        GetOptimizedJSONStructure = BuildMinimalJSONStructureString(fieldSelection)
        OCRUtils.LogToFile "PromptTemplates: Using MINIMAL prompt structure for small model: " & modelName
    Else
        GetOptimizedJSONStructure = BuildJSONStructureString(fieldSelection)
        OCRUtils.LogToFile "PromptTemplates: Using STANDARD prompt structure for model: " & modelName
    End If
End Function

' For debugging: Create a test prompt to see the full structure
Public Function GenerateTestPrompt() As String
    ' Create a test field selection with all fields enabled
    Dim testFields As Object
    Set testFields = CreateObject("Scripting.Dictionary")
    testFields.Add "From", True
    testFields.Add "To", True
    testFields.Add "LetterRef", True
    testFields.Add "LetterDate", True
    testFields.Add "Subject", True
    testFields.Add "References", True
    testFields.Add "Body", False  ' Exclude body for brevity in test
    testFields.Add "Summary", True
    testFields.Add "Tags", True
    
    Dim fullPrompt As String
    fullPrompt = "==== STANDARD PROMPT (FOR LARGER MODELS) ====" & vbLf & vbLf
    fullPrompt = fullPrompt & BuildJSONStructureString(testFields) & vbLf & vbLf
    fullPrompt = fullPrompt & "==== MINIMAL PROMPT (FOR SMALLER MODELS) ====" & vbLf & vbLf
    fullPrompt = fullPrompt & BuildMinimalJSONStructureString(testFields)
    
    GenerateTestPrompt = fullPrompt
End Function

' Specialized prompt for extracting just the problematic fields
Public Function BuildTargetedExtractionPrompt() As String
    Dim prompt As String
    
    prompt = "## Targeted Field Extraction" & vbLf & vbLf
    
    prompt = prompt & "Extract ONLY these two fields from the construction letter:" & vbLf & vbLf
    
    prompt = prompt & "1. **Letter Reference Number**" & vbLf & _
                      "   - Location: Header section of the letter" & vbLf & _
                      "   - Look for: 'Our Reference:', 'Our Ref:', 'Ref:', or 'Ref. No.:'" & vbLf & _
                      "   - Format: Usually alphanumeric with hyphens (e.g., BNDP3-ZZ-MMD-CSCEC-LT-02064)" & vbLf & _
                      "   - WARNING: This is NOT the subject line!" & vbLf & vbLf
    
    prompt = prompt & "2. **Document References**" & vbLf & _
                      "   - Location: In the letter body, often after greeting" & vbLf & _
                      "   - Look for: A section starting with 'References:'" & vbLf & _
                      "   - Format: Numbered list (1., 2., etc.) of other documents" & vbLf & _
                      "   - Extract: All referenced documents as comma-separated text" & vbLf & vbLf
    
    prompt = prompt & "## Examples from Real Letters:" & vbLf & vbLf
    
    prompt = prompt & "Example 1:" & vbLf & _
                      "Text: 'Our Reference: BNDP3-ZZ-MMD-CSCEC-LT-02064'" & vbLf & _
                      "Extract: ""letter_reference"": ""BNDP3-ZZ-MMD-CSCEC-LT-02064""" & vbLf & vbLf
    
    prompt = prompt & "Example 2:" & vbLf & _
                      "Text: 'References:" & vbLf & _
                      "1. BNDP3-ZZ-CSCEC-MMD-LT-01435 dated 4th March 2025" & vbLf & _
                      "2. Meeting minutes MM-456'" & vbLf & _
                      "Extract: ""references"": ""BNDP3-ZZ-CSCEC-MMD-LT-01435 dated 4th March 2025, Meeting minutes MM-456""" & vbLf & vbLf
    
    prompt = prompt & "## Output Format:" & vbLf & _
                      "{" & vbLf & _
                      "  ""letter_reference"": ""[extracted reference or 'Not Found']""," & vbLf & _
                      "  ""references"": ""[extracted references or 'Not Found']""" & vbLf & _
                      "}"
    
    BuildTargetedExtractionPrompt = prompt
End Function
