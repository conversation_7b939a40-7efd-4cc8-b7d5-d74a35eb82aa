Attribute VB_Name = "Prov_Ollama"
'
' Ollama Provider Module
' Handles Ollama-specific OCR processing using local vision models
' Author: AI Assistant
'

Option Explicit

' ================================
' PUBLIC INTERFACE
' ================================

' Process PDF with Ollama using images
Public Function ProcessPDFWithOllama(ByVal pdfPath As String, Optional ByVal modelName As String = "", Optional ByVal extractionMode As Boolean = False) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim tempImagePaths() As String
    Dim base64Images() As String
    Dim pageCount As Integer
    Dim i As Integer
    Dim jsonResponse As String
    Dim parsedData As Object
    
    ProcessPDFWithOllama.Success = False
    
    ' Get page count
    pageCount = OCRUtils.GetPDFPageCount(pdfPath)
    If pageCount = 0 Then
        ProcessPDFWithOllama.ErrorMessage = "Failed to get PDF page count"
        Exit Function
    End If
    
    ' Convert all pages to images
    ReDim tempImagePaths(1 To pageCount)
    ReDim base64Images(1 To pageCount)
    
    For i = 1 To pageCount
        tempImagePaths(i) = OCRUtils.ConvertPDFPageToImage(pdfPath, i)
        If tempImagePaths(i) = "" Then
            ProcessPDFWithOllama.ErrorMessage = "Failed to convert page " & i
            GoTo Cleanup
        End If
        
        base64Images(i) = OCRUtils.EncodeImageToBase64(tempImagePaths(i))
        If base64Images(i) = "" Then
            ProcessPDFWithOllama.ErrorMessage = "Failed to encode page " & i
            GoTo Cleanup
        End If
    Next i
    
    ' Send to Ollama
    jsonResponse = SendMultiPageToOllamaAPI(base64Images, pageCount, extractionMode)
    If jsonResponse = "" Then
        ProcessPDFWithOllama.ErrorMessage = "Failed to get response from Ollama"
        GoTo Cleanup
    End If
    
    ' Parse response based on extraction mode parameter
    If extractionMode Then
        ' Structured extraction mode - expect JSON response
        Set parsedData = JsonConverter.ParseJson(jsonResponse)
        If Not parsedData Is Nothing Then
            ProcessPDFWithOllama = ResponseParser.ExtractDataFromOllamaResponse(parsedData)
            ProcessPDFWithOllama.Success = True
            OCRUtils.LogToFile "Ollama: Successfully parsed structured JSON response (Extraction Mode = TRUE)"
        Else
            ProcessPDFWithOllama.ErrorMessage = "Failed to parse JSON response from Ollama"
            OCRUtils.LogToFile "Ollama: Failed to parse JSON response (Extraction Mode = TRUE)"
        End If
    Else
        ' OCR-only mode - expect plain text response
        ProcessPDFWithOllama = ResponseParser.ExtractDataFromOllamaPlainTextResponse(jsonResponse)
        ProcessPDFWithOllama.Success = True
        OCRUtils.LogToFile "Ollama: Using plain text OCR response (Extraction Mode = FALSE)"
    End If
    
Cleanup:
    ' Clean up temporary files
    For i = 1 To pageCount
        If i <= UBound(tempImagePaths) Then
            If OCRUtils.FileExists(tempImagePaths(i)) Then Kill tempImagePaths(i)
        End If
    Next i
    
    Exit Function
    
ErrorHandler:
    ProcessPDFWithOllama.ErrorMessage = "Error: " & Err.Description
    GoTo Cleanup
End Function

' Test Ollama connection
Public Sub TestOllamaConnection()
    Dim Http As Object
    Dim Response As String
    Dim ollamaApiUrl As String
    Dim ollamaModel As String
    
    On Error GoTo ErrorHandler
    
    ollamaApiUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_OLLAMA)
    ollamaModel = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OLLAMA) ' Or a specific model if chosen
    
    If ollamaApiUrl = "" Then
        MsgBox "Ollama API URL not configured in config.json.", vbCritical
        Exit Sub
    End If
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    ' Assuming the /tags endpoint is relative to the base API URL (e.g., http://localhost:11434/api/tags)
    Dim tagsUrl As String
    tagsUrl = Replace(ollamaApiUrl, "/generate", "/tags") ' Adjust if base URL is different
    
    Http.Open "GET", tagsUrl, False
    Http.Send
    
    If Http.status = 200 Then
        Response = Http.responseText
        MsgBox "Ollama connection successful!" & vbCrLf & _
               "Using Model: " & ollamaModel & vbCrLf & _
               "API URL: " & ollamaApiUrl, vbInformation
        Debug.Print "Available Ollama models: " & Response
    Else
        MsgBox "Ollama connection failed. Make sure Ollama is running.", vbCritical
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error: " & Err.Description & vbCrLf & _
           "Make sure Ollama is running on localhost:11434", vbCritical
End Sub

' ================================
' PRIVATE IMPLEMENTATION
' ================================

' Send multiple images to Ollama
Private Function SendMultiPageToOllamaAPI(ByRef base64Images() As String, ByVal pageCount As Integer, Optional ByVal extractionMode As Boolean = False) As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim requestBody As String
    Dim prompt As String
    Dim imagesJson As String
    Dim i As Integer
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    ' Build prompt based on extraction mode
    If extractionMode Then
        prompt = PromptTemplates.GetUniversalOCRExtractionPrompt(pageCount, OCRConfig.GetAllFieldSelectionStates())
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR+EXTRACTION prompt (Extract Fields = TRUE)"
    Else
        prompt = PromptTemplates.GetUniversalOCRPrompt(pageCount)
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR-ONLY prompt (Extract Fields = FALSE)"
    End If
    
    ' Build images array
    imagesJson = "["
    For i = 1 To pageCount
        If i > 1 Then imagesJson = imagesJson & ","
        imagesJson = imagesJson & """" & base64Images(i) & """"
    Next i
    imagesJson = imagesJson & "]"
    
    Dim currentOllamaModel As String
    currentOllamaModel = GetActiveOllamaModelName() ' Dynamically get an available local model

    If currentOllamaModel = "" Then
        OCRUtils.LogToFile "Ollama: No valid local model determined for SendMultiPageToOllamaAPI. Aborting."
        SendMultiPageToOllamaAPI = "" ' Indicate failure
        Exit Function
    End If

    ' Build request body based on mode
    If extractionMode Then
        ' Structured extraction mode - include JSON format schema
        requestBody = "{" & _
                     """model"": """ & currentOllamaModel & """," & _
                     """prompt"": """ & OCRUtils.EscapeJsonString(prompt) & """," & _
                     """images"": " & imagesJson & "," & _
                     """stream"": false," & _
                     """format"": {" & _
                     """type"": ""object""," & _
                     """properties"": {" & _
                     """letter_reference"": {""type"": ""string""}," & _
                     """letter_date"": {""type"": ""string""}," & _
                     """subject"": {""type"": ""string""}," & _
                     """body"": {""type"": ""string""}" & _
                     "}," & _
                     """required"": [""body""]" & _
                     "}" & _
                     "}"
        OCRUtils.LogToFile "Ollama: Using structured JSON format for extraction (Extract Fields = TRUE)"
    Else
        ' OCR-only mode - no format constraint, get plain text
        requestBody = "{" & _
                     """model"": """ & currentOllamaModel & """," & _
                     """prompt"": """ & OCRUtils.EscapeJsonString(prompt) & """," & _
                     """images"": " & imagesJson & "," & _
                     """stream"": false" & _
                     "}"
        OCRUtils.LogToFile "Ollama: Using plain text format for OCR-only (Extract Fields = FALSE)"
    End If
    
    OCRUtils.LogToFile "Ollama: Sending " & pageCount & " images to model: " & currentOllamaModel
    
    ' Get base URL from config
    Dim baseUrl As String
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_OLLAMA)
    If baseUrl = "" Then
        SendMultiPageToOllamaAPI = ""
        OCRUtils.LogToFile "Ollama: API URL not configured."
        Exit Function
    End If
    
    ' Build full URL by appending endpoint to base URL
    Dim fullUrl As String
    fullUrl = baseUrl & "/api/generate"

    Http.Open "POST", fullUrl, False
    Http.SetRequestHeader "Content-Type", "application/json"
    Http.SetRequestHeader "Accept", "application/json"
    Http.Send requestBody
    
    ' Wait for response
    Dim timeout As Long
    timeout = Timer + (60 * pageCount)
    
    Do While Http.readyState <> 4 And Timer < timeout
        DoEvents
        OCRUtils.WaitSeconds 0.1
    Loop
    
    If Http.readyState = 4 And Http.status = 200 Then
        SendMultiPageToOllamaAPI = Http.responseText
        OCRUtils.LogToFile "Ollama: Successfully received response"
    Else
        SendMultiPageToOllamaAPI = ""
        OCRUtils.LogToFile "Ollama: Request failed - Status: " & Http.status
    End If
    
    Exit Function
    
ErrorHandler:
    SendMultiPageToOllamaAPI = ""
    OCRUtils.LogToFile "Ollama: Error in SendMultiPageToOllamaAPI: " & Err.Description
End Function

' Send text-only request to Ollama (for Docling post-processing)
Public Function SendTextToOllama(ByVal prompt As String, Optional ByVal modelName As String = "") As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim requestBody As String
    Dim targetModel As String
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    ' Determine the model to use
    If modelName <> "" Then
        targetModel = modelName
        OCRUtils.LogToFile "Prov_Ollama.SendTextToOllama: Using specified model for extraction: '" & targetModel & "'"
    Else
        ' Use the active running model as fallback
        targetModel = GetActiveOllamaModelName()
        If targetModel = "" Then
            OCRUtils.LogToFile "Ollama: No valid local model determined for SendTextToOllama. Aborting."
            SendTextToOllama = "" ' Indicate failure
            Exit Function
        End If
        OCRUtils.LogToFile "Prov_Ollama.SendTextToOllama: Using active running model: '" & targetModel & "'"
    End If

    requestBody = "{" & _
                 """model"": """ & targetModel & """," & _
                 """prompt"": """ & OCRUtils.EscapeJsonString(prompt) & """," & _
                 """stream"": false," & _
                 """format"": {" & _
                 """type"": ""object""," & _
                 """properties"": {" & _
                 """letter_reference"": {""type"": ""string""}," & _
                 """letter_date"": {""type"": ""string""}," & _
                 """subject"": {""type"": ""string""}," & _
                 """body"": {""type"": ""string""}" & _
                 "}," & _
                 """required"": [""body""]" & _
                 "}" & _
                 "}"
    
    OCRUtils.LogToFile "Ollama: Sending text-only request for post-processing to model: " & targetModel
    
    ' Get base URL from config
    Dim baseUrl As String
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_OLLAMA)
    If baseUrl = "" Then
        SendTextToOllama = ""
        OCRUtils.LogToFile "Ollama: API URL not configured for text processing."
        Exit Function
    End If
    
    ' Build full URL by appending endpoint to base URL
    Dim fullUrl As String
    fullUrl = baseUrl & "/api/generate"
    
    Http.Open "POST", fullUrl, False
    Http.SetRequestHeader "Content-Type", "application/json"
    Http.SetRequestHeader "Accept", "application/json"
    Http.Send requestBody
    
    ' Wait for response
    Dim timeout As Long
    timeout = Timer + 120 ' 2 minute timeout for text processing
    
    Do While Http.readyState <> 4 And Timer < timeout
        DoEvents
        OCRUtils.WaitSeconds 0.1
    Loop
    
    If Http.readyState = 4 And Http.status = 200 Then
        SendTextToOllama = Http.responseText
        OCRUtils.LogToFile "Ollama: Successfully received text processing response"
    Else
        SendTextToOllama = ""
        OCRUtils.LogToFile "Ollama: Text request failed - Status: " & Http.status
    End If
    
    Exit Function
    
ErrorHandler:
    SendTextToOllama = ""
    OCRUtils.LogToFile "Ollama: Error in SendTextToOllama: " & Err.Description
End Function

' Get JSON string of RUNNING Ollama models from /api/ps
Public Function GetRunningOllamaModelsJson() As String
    On Error GoTo ErrorHandler
    
    Dim Client As New WebClient
    Dim Request As New WebRequest
    Dim Response As WebResponse
    Dim baseUrl As String
    Dim startTime As Double
    
    ' Initialize return value
    GetRunningOllamaModelsJson = ""
    startTime = Timer
    
    OCRUtils.LogToFile "Ollama: GetRunningOllamaModelsJson - Starting model detection with VBA-Web"
    
    ' Validate configuration first
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_OLLAMA)
    If baseUrl = "" Then
        OCRUtils.LogToFile "Ollama: ERROR - API URL not configured. Cannot fetch running models."
        Exit Function
    End If
    
    ' Configure VBA-Web client
    Client.TimeoutMS = 5000 ' 5 second timeout
    Client.Insecure = True ' Allow self-signed certificates for local development
    
    ' Determine base URL and resource path
    Dim clientBaseUrl As String
    Dim resourcePath As String
    
    Dim apiPathPos As Long
    apiPathPos = InStr(baseUrl, "/api/")
    If apiPathPos > 0 Then
        clientBaseUrl = Left(baseUrl, apiPathPos - 1) ' e.g., "http://localhost:11434"
        resourcePath = "api/ps"
    Else
        If Right(baseUrl, 1) = "/" Then
            clientBaseUrl = Left(baseUrl, Len(baseUrl) - 1) ' Remove trailing slash
        Else
            clientBaseUrl = baseUrl
        End If
        resourcePath = "api/ps"
        OCRUtils.LogToFile "Ollama: Warning - Configured API URL '" & baseUrl & "' does not appear to be the full /api/generate endpoint. Using as base URL."
    End If
    
    Client.baseUrl = clientBaseUrl
    
    ' Configure request
    Request.Resource = resourcePath
    Request.Method = VbWebMethod.vbWebHttpGet
    Request.Format = VbWebFormat.vbWebFormatJson
    
    OCRUtils.LogToFile "Ollama: Attempting to fetch running models from: " & Client.baseUrl & "/" & resourcePath
    
    ' Execute request
    Set Response = Client.Execute(Request)
    
    ' Process response
    If Response.StatusCode = 200 Then
        If Len(Response.content) > 0 Then
            GetRunningOllamaModelsJson = Response.content
            OCRUtils.LogToFile "Ollama: SUCCESS - Fetched running models via VBA-Web. Response length: " & Len(Response.content)
            ' Log first 200 chars for debugging
            OCRUtils.LogToFile "Ollama: Response preview: " & Left(Response.content, 200) & IIf(Len(Response.content) > 200, "...", "")
        Else
            OCRUtils.LogToFile "Ollama: ERROR - Empty response from Ollama API"
        End If
    ElseIf Response.StatusCode = 404 Then
        OCRUtils.LogToFile "Ollama: ERROR - Endpoint not found (404). Check if Ollama is running and API version is correct."
    ElseIf Response.StatusCode = VbWebStatusCode.vbWebStatusRequestTimeout Then
        OCRUtils.LogToFile "Ollama: ERROR - Request timed out. Ollama may not be running or is unresponsive."
    ElseIf Response.StatusCode = 0 Then
        OCRUtils.LogToFile "Ollama: ERROR - Connection failed. Ollama may not be running at: " & Client.baseUrl
    Else
        OCRUtils.LogToFile "Ollama: ERROR - HTTP " & Response.StatusCode & " - " & Response.StatusDescription
    End If
    
    Dim totalTime As Double
    totalTime = Timer - startTime
    OCRUtils.LogToFile "Ollama: Model detection completed in " & Format(totalTime, "0.00") & " seconds"
    
    Exit Function
    
ErrorHandler:
    Dim errorMsg As String
    errorMsg = "Ollama: ERROR in GetRunningOllamaModelsJson (VBA-Web): " & Err.Description & " (Number: " & Err.Number & ")"
    OCRUtils.LogToFile errorMsg
    GetRunningOllamaModelsJson = ""
End Function

' Get a collection of names of currently RUNNING Ollama models
Public Function GetRunningOllamaModelNames() As Collection
    On Error GoTo ErrorHandler
    Dim jsonString As String
    Dim parsedResponse As Object
    Dim modelsArray As Object
    Dim modelEntry As Object
    Dim i As Long
    Dim modelCount As Long
    
    ' Initialize with empty collection
    Set GetRunningOllamaModelNames = New Collection
    
    OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Starting model name extraction"
    
    ' Get JSON from API
    jsonString = GetRunningOllamaModelsJson() ' Fetches from /api/ps
    If jsonString = "" Then
        OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: No JSON response from API - likely Ollama not running or API error"
        ' Add placeholder for no models
        GetRunningOllamaModelNames.Add "- No models running -"
        Exit Function ' Return collection with placeholder
    End If

    ' Validate JSON length
    If Len(jsonString) < 10 Then ' Minimum viable JSON would be longer than this
        OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: JSON response too short, likely invalid: " & jsonString
        Exit Function
    End If

    ' Parse JSON with enhanced error handling
    On Error GoTo JsonParseError
    Set parsedResponse = JsonConverter.ParseJson(jsonString)
    On Error GoTo ErrorHandler
    
    If parsedResponse Is Nothing Then
        OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: JSON parser returned Nothing for input: " & Left(jsonString, 200)
        Exit Function ' Return empty collection
    End If

    ' Validate JSON structure
    If Not parsedResponse.Exists("models") Then
        OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Missing 'models' key in JSON response. Available keys: " & GetDictionaryKeys(parsedResponse)
        Exit Function ' Return empty collection
    End If
    
    ' Get models array with validation
    Set modelsArray = parsedResponse("models")
    If modelsArray Is Nothing Then
        OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: 'models' key exists but value is null"
        Exit Function ' Return empty collection
    End If
    
    ' Check if it's a collection/array
    If TypeName(modelsArray) <> "Collection" Then
        OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: 'models' is not a Collection, type is: " & TypeName(modelsArray)
        Exit Function
    End If
    
    modelCount = modelsArray.Count
    If modelCount = 0 Then
        OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: 'models' array is empty - no models currently running"
        ' Add placeholder for no models
        GetRunningOllamaModelNames.Add "- No models running -"
        Exit Function ' Return collection with placeholder
    End If
    
    OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Processing " & modelCount & " model entries"
    
    ' Extract model names with validation
    For i = 1 To modelCount
        On Error GoTo ModelEntryError
        Set modelEntry = modelsArray(i)
        
        If modelEntry Is Nothing Then
            OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Model entry " & i & " is null, skipping"
            GoTo NextModel
        End If
        
        If TypeName(modelEntry) <> "Dictionary" Then
            OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Model entry " & i & " is not a Dictionary, type: " & TypeName(modelEntry)
            GoTo NextModel
        End If
        
        If Not modelEntry.Exists("name") Then
            OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Model entry " & i & " missing 'name' field. Available keys: " & GetDictionaryKeys(modelEntry)
            GoTo NextModel
        End If
        
        Dim modelName As String
        modelName = CStr(modelEntry("name"))
        If Len(modelName) > 0 Then
            GetRunningOllamaModelNames.Add modelName
            OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Added model: " & modelName
        Else
            OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Model entry " & i & " has empty name field"
        End If
        
NextModel:
        On Error GoTo ErrorHandler
    Next i
    
    OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: SUCCESS - Extracted " & GetRunningOllamaModelNames.Count & " valid model names from " & modelCount & " entries"
    Exit Function
    
JsonParseError:
    OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: JSON Parse Error: " & Err.Description & " for JSON: " & Left(jsonString, 500)
    Set GetRunningOllamaModelNames = New Collection ' Return empty collection on JSON error
    Exit Function
    
ModelEntryError:
    OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: Error processing model entry " & i & ": " & Err.Description
    Resume NextModel ' Skip this entry and continue
    
ErrorHandler:
    OCRUtils.LogToFile "Ollama.GetRunningOllamaModelNames: General Error: " & Err.Description & " (Number: " & Err.Number & ")"
    Set GetRunningOllamaModelNames = New Collection ' Return empty collection on error
End Function

' Helper function to get dictionary keys for debugging
Private Function GetDictionaryKeys(dict As Object) As String
    On Error Resume Next
    Dim Key As Variant, keys As String
    For Each Key In dict.keys
        If keys <> "" Then keys = keys & ", "
        keys = keys & CStr(Key)
    Next Key
    GetDictionaryKeys = keys
    If GetDictionaryKeys = "" Then GetDictionaryKeys = "(no keys or not a dictionary)"
End Function

' Helper function to get a valid, CURRENTLY RUNNING local Ollama model name
Private Function GetActiveOllamaModelName() As String
    On Error GoTo ErrorHandler
    Dim runningModelsJsonString As String
    Dim parsedResponse As Object
    Dim defaultOllamaModelName As String
    Dim selectedModelName As String
    Dim i As Long
    Dim modelEntry As Object
    Dim runningModelsList As Object

    selectedModelName = "" ' Initialize

    runningModelsJsonString = GetRunningOllamaModelsJson() ' Fetches from /api/ps

    If runningModelsJsonString = "" Then
        OCRUtils.LogToFile "Ollama: Could not retrieve RUNNING model list from Ollama server (GetRunningOllamaModelsJson returned empty)."
        GetActiveOllamaModelName = ""
        Exit Function
    End If

    Set parsedResponse = JsonConverter.ParseJson(runningModelsJsonString)

    If parsedResponse Is Nothing Then
        OCRUtils.LogToFile "Ollama: Failed to parse JSON for running models list from string: " & Left(runningModelsJsonString, 200) & "..."
        GetActiveOllamaModelName = ""
        Exit Function
    End If

    If Not parsedResponse.Exists("models") Then
        OCRUtils.LogToFile "Ollama: Parsed JSON for running models does not contain 'models' array. Full JSON: " & Left(runningModelsJsonString, 500)
        GetActiveOllamaModelName = ""
        Exit Function
    End If
    
    Set runningModelsList = parsedResponse("models")

    If runningModelsList Is Nothing Or runningModelsList.Count = 0 Then
        OCRUtils.LogToFile "Ollama: No models currently running on the Ollama server (models array is null or empty)."
        GetActiveOllamaModelName = ""
        Exit Function
    End If

    ' Try to find the default configured Ollama model among the RUNNING models
    defaultOllamaModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OLLAMA)
    OCRUtils.LogToFile "Ollama: Configured default model for Ollama provider: '" & defaultOllamaModelName & "'"
    OCRUtils.LogToFile "Ollama: Checking against " & runningModelsList.Count & " running model(s)."

    For i = 1 To runningModelsList.Count
        Set modelEntry = runningModelsList(i)
        If modelEntry.Exists("name") Then
            OCRUtils.LogToFile "Ollama: Checking running model: '" & modelEntry("name") & "'"
            If LCase(modelEntry("name")) = LCase(defaultOllamaModelName) Then
                selectedModelName = modelEntry("name")
                OCRUtils.LogToFile "Ollama: Found configured default model '" & selectedModelName & "' currently running."
                Exit For
            End If
        Else
            OCRUtils.LogToFile "Ollama: Running model entry at index " & i & " has no 'name' property."
        End If
    Next i

    ' If default model not found running, use the first available RUNNING model from the list
    If selectedModelName = "" Then
        OCRUtils.LogToFile "Ollama: Configured default model '" & defaultOllamaModelName & "' is not currently running."
        Set modelEntry = runningModelsList(1) ' Get the first running model
        If modelEntry.Exists("name") Then
            selectedModelName = modelEntry("name")
            OCRUtils.LogToFile "Ollama: Using first available RUNNING model: '" & selectedModelName & "'"
        Else
            OCRUtils.LogToFile "Ollama: First running model in the list has no 'name' property. Cannot select a model."
            selectedModelName = "" ' Should not happen if count > 0 and entries are valid
        End If
    End If
    
    If selectedModelName = "" Then
         OCRUtils.LogToFile "Ollama: CRITICAL - Could not determine a valid RUNNING local model name to use."
    Else
        OCRUtils.LogToFile "Ollama: Selected active RUNNING model: '" & selectedModelName & "'"
    End If

    GetActiveOllamaModelName = selectedModelName
    Exit Function

ErrorHandler:
    OCRUtils.LogToFile "Ollama: Error in GetActiveOllamaModelName: " & Err.Description
    GetActiveOllamaModelName = ""
End Function


' Check if Ollama service is running (quick health check)
Public Function isOllamaRunning(Optional quickCheck As Boolean = False) As Boolean
    On Error GoTo ErrorHandler
    
    ' Check cache first
    Dim cacheResult As Variant
    cacheResult = OCRConfig.GetCachedProviderStatus(OCRConfig.PID_OLLAMA)
    If cacheResult(1) Then ' Is cached
        isOllamaRunning = cacheResult(0)
        Exit Function
    End If
    
    Dim Client As New WebClient
    Dim Request As New WebRequest
    Dim Response As WebResponse
    Dim baseUrl As String
    
    ' Initialize
    isOllamaRunning = False
    
    OCRUtils.LogToFile "Ollama.IsOllamaRunning: Starting service health check with VBA-Web"
    
    ' Get base URL
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_OLLAMA)
    If baseUrl = "" Then
        OCRUtils.LogToFile "Ollama.IsOllamaRunning: No API URL configured"
        Exit Function
    End If
    
    ' Configure VBA-Web client for quick health check
    Client.TimeoutMS = IIf(quickCheck, 500, 2000) ' 500ms for quick check, 2s for normal
    Client.Insecure = True ' Allow self-signed certificates for local development
    
    ' Determine base URL and resource path
    Dim clientBaseUrl As String
    Dim resourcePath As String
    
    Dim apiPathPos As Long
    apiPathPos = InStr(baseUrl, "/api/")
    If apiPathPos > 0 Then
        clientBaseUrl = Left(baseUrl, apiPathPos - 1) ' e.g., "http://localhost:11434"
        resourcePath = "api/version" ' /api/version is typically faster than /api/ps
    Else
        If Right(baseUrl, 1) = "/" Then
            clientBaseUrl = Left(baseUrl, Len(baseUrl) - 1) ' Remove trailing slash
        Else
            clientBaseUrl = baseUrl
        End If
        resourcePath = "api/version"
    End If
    
    Client.baseUrl = clientBaseUrl
    
    ' Configure request
    Request.Resource = resourcePath
    Request.Method = VbWebMethod.vbWebHttpGet
    Request.Format = VbWebFormat.vbWebFormatJson
    
    OCRUtils.LogToFile "Ollama.IsOllamaRunning: Checking service at: " & Client.baseUrl & "/" & resourcePath
    
    ' Execute request
    Set Response = Client.Execute(Request)
    
    ' Process response - any successful response indicates Ollama is running
    If Response.StatusCode = 200 Or Response.StatusCode = 404 Then
        ' 200 = version endpoint exists and responded
        ' 404 = service is running but endpoint may not exist (still counts as "running")
        isOllamaRunning = True
        OCRUtils.LogToFile "Ollama.IsOllamaRunning: Service is RUNNING (HTTP " & Response.StatusCode & ")"
    ElseIf Response.StatusCode = VbWebStatusCode.vbWebStatusRequestTimeout Then
        isOllamaRunning = False
        OCRUtils.LogToFile "Ollama.IsOllamaRunning: Service health check TIMED OUT - service likely not running"
    ElseIf Response.StatusCode = 0 Then
        isOllamaRunning = False
        OCRUtils.LogToFile "Ollama.IsOllamaRunning: Connection FAILED - service not running at: " & Client.baseUrl
    Else
        isOllamaRunning = False
        OCRUtils.LogToFile "Ollama.IsOllamaRunning: Service not responding properly (HTTP " & Response.StatusCode & " - " & Response.StatusDescription & ")"
    End If
    
    ' Update cache with result
    OCRConfig.UpdateProviderStatusCache OCRConfig.PID_OLLAMA, isOllamaRunning
    
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "Ollama.IsOllamaRunning: ERROR in health check (VBA-Web): " & Err.Description & " (Number: " & Err.Number & ")"
    isOllamaRunning = False
    ' Update cache even on error
    OCRConfig.UpdateProviderStatusCache OCRConfig.PID_OLLAMA, isOllamaRunning
End Function

' Check if Ollama model supports vision
Private Function IsVisionModelAvailable() As Boolean
    Dim modelInfo As String
    modelInfo = GetRunningOllamaModelsJson() ' Check against running models
    
    ' Simple check for vision-capable models against the default or configured model
    Dim activeModelName As String
    activeModelName = GetActiveOllamaModelName() ' Get the currently selected RUNNING model

    If activeModelName = "" Then
        IsVisionModelAvailable = False
        Exit Function
    End If
    
    ' Check if the name of the active running model suggests vision capabilities
    ' This is a heuristic. A more robust way would be to check model details if /api/ps provided them.
    IsVisionModelAvailable = (InStr(LCase(activeModelName), "vision") > 0 Or InStr(LCase(activeModelName), "vl") > 0)
    
    If IsVisionModelAvailable Then
        OCRUtils.LogToFile "Ollama: Active running model '" & activeModelName & "' appears to be vision-capable."
    Else
        OCRUtils.LogToFile "Ollama: Active running model '" & activeModelName & "' does NOT appear to be vision-capable based on name."
    End If
End Function

' ================================
' STANDARDIZED PROVIDER INTERFACE
' ================================

' Check if the Ollama provider is available (i.e., service is running)
Private Function IsProviderAvailable() As Boolean
    IsProviderAvailable = isOllamaRunning()
End Function

' Get a collection of available model names for this provider
Private Function GetAvailableModels() As Collection
    OCRUtils.LogToFile "Ollama.GetAvailableModels: Calling GetRunningOllamaModelNames"
    Set GetAvailableModels = GetRunningOllamaModelNames()
    OCRUtils.LogToFile "Ollama.GetAvailableModels: Returned " & GetAvailableModels.Count & " models"
End Function

' Get the type of this provider
Private Function GetProviderType() As String
    GetProviderType = "local" ' Ollama is a local provider
End Function




