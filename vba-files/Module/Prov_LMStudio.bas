Attribute VB_Name = "Prov_LMStudio"
'
' LM Studio Provider Module
' Handles LM Studio-specific OCR processing using local vision models
' Author: AI Assistant
'

Option Explicit

' ================================
' PUBLIC INTERFACE
' ================================

' Process PDF with LM Studio using images
Public Function ProcessPDFWithLMStudio(ByVal pdfPath As String, Optional ByVal model As String = "", Optional ByVal extractionMode As Boolean = False) As ocrResult
    On Error GoTo ErrorHandler
    ' Ensure logging is enabled for OCR processing
    OCRUtils.SetLogging True
    OCRUtils.LogToFile "LMStudio: ProcessPDFWithLMStudio started for '" & pdfPath & "', model: " & model & ", extractionMode: " & extractionMode

    Dim tempImagePaths() As String
    Dim base64Images() As String
    Dim pageCount As Integer
    Dim i As Integer
    Dim jsonResponse As String
    Dim parsedData As Object
    
    ProcessPDFWithLMStudio.Success = False
    
    ' Get page count
    pageCount = OCRUtils.GetPDFPageCount(pdfPath)
    If pageCount = 0 Then
        ProcessPDFWithLMStudio.ErrorMessage = "Failed to get PDF page count"
        OCRUtils.LogToFile "LMStudio: Page count returned 0, aborting."
        Exit Function
    End If
    
    ' Convert all pages to images
    ReDim tempImagePaths(1 To pageCount)
    ReDim base64Images(1 To pageCount)
    
    For i = 1 To pageCount
        tempImagePaths(i) = OCRUtils.ConvertPDFPageToImage(pdfPath, i)
        If tempImagePaths(i) = "" Then
            ProcessPDFWithLMStudio.ErrorMessage = "Failed to convert page " & i
            OCRUtils.LogToFile "LMStudio: Failed to convert page " & i
            GoTo Cleanup
        End If
        
        base64Images(i) = OCRUtils.EncodeImageToBase64(tempImagePaths(i))
        If base64Images(i) = "" Then
            ProcessPDFWithLMStudio.ErrorMessage = "Failed to encode page " & i
            OCRUtils.LogToFile "LMStudio: Failed to encode page " & i
            GoTo Cleanup
        End If
    Next i
      ' Send to LM Studio
    jsonResponse = SendMultiPageToLMStudioAPI(base64Images, pageCount, model, extractionMode)
    If jsonResponse = "" Then
        ProcessPDFWithLMStudio.ErrorMessage = "Failed to get response from LM Studio"
        OCRUtils.LogToFile "LMStudio: No JSON response returned from SendMultiPageToLMStudioAPI."
        GoTo Cleanup
    End If
    
    ' Parse response based on extraction mode parameter
    If extractionMode Then
        ' Structured extraction mode
        Set parsedData = JsonConverter.ParseJson(jsonResponse) ' Assign to intermediate variable
        If Not parsedData Is Nothing Then
            ProcessPDFWithLMStudio = ResponseParser.ExtractDataFromLMStudioResponse(parsedData)
            If ProcessPDFWithLMStudio.Success Then
                OCRUtils.LogToFile "LMStudio: Successfully parsed structured JSON response (Extraction Mode = TRUE)"
            Else
                ' Error message should be set by ExtractDataFromLMStudioResponse
                OCRUtils.LogToFile "LMStudio: Failed to parse structured JSON response (Extraction Mode = TRUE). Error: " & ProcessPDFWithLMStudio.ErrorMessage
            End If
        Else
            ProcessPDFWithLMStudio.Success = False
            ProcessPDFWithLMStudio.ErrorMessage = "Failed to parse overall JSON response from LM Studio API"
            OCRUtils.LogToFile "LMStudio: Failed to parse overall JSON response (Extraction Mode = TRUE) - parsedData is Nothing"
        End If
    Else
        ' OCR-only mode - expect plain text response
        ProcessPDFWithLMStudio = ResponseParser.ExtractDataFromLMStudioPlainTextResponse(jsonResponse)
        If ProcessPDFWithLMStudio.Success Then
            OCRUtils.LogToFile "LMStudio: Using plain text OCR response (Extraction Mode = FALSE)"
        Else
            ' Error message should be set by ExtractDataFromLMStudioPlainTextResponse
            OCRUtils.LogToFile "LMStudio: Failed to process plain text OCR response (Extraction Mode = FALSE). Error: " & ProcessPDFWithLMStudio.ErrorMessage
        End If
    End If
    
Cleanup:
    ' Clean up temporary files
    For i = 1 To pageCount
        If i <= UBound(tempImagePaths) Then
            If OCRUtils.FileExists(tempImagePaths(i)) Then Kill tempImagePaths(i)
        End If
    Next i
    
    Exit Function
    
ErrorHandler:
    ProcessPDFWithLMStudio.ErrorMessage = "Error: " & Err.Description
    GoTo Cleanup
End Function

' Test LM Studio connection
Public Sub TestLMStudioConnection()
    Dim Client As New WebClient
    Dim Request As New WebRequest
    Dim Response As WebResponse
    Dim lmStudioApiUrl As String
    Dim lmStudioModel As String
    
    On Error GoTo ErrorHandler
    
    lmStudioApiUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_LMSTUDIO)
    lmStudioModel = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_LMSTUDIO)
    
    If lmStudioApiUrl = "" Then
        MsgBox "LM Studio API URL not configured in config.json.", vbCritical
        Exit Sub
    End If
    
    ' Configure VBA-Web client
    Client.TimeoutMS = 5000 ' 5 second timeout
    Client.Insecure = True ' Allow self-signed certificates for local development
    
    ' Extract base URL from full endpoint
    Dim baseUrl As String
    Dim v1Pos As Long
    v1Pos = InStr(lmStudioApiUrl, "/v1/")
    If v1Pos > 0 Then
        baseUrl = Left(lmStudioApiUrl, v1Pos - 1)
    Else
        baseUrl = lmStudioApiUrl
    End If
    Client.baseUrl = baseUrl
    
    ' Configure request for models endpoint
    Request.Resource = "v1/models"
    Request.Method = VbWebMethod.vbWebHttpGet
    Request.Format = VbWebFormat.vbWebFormatJson
    
    ' Execute request
    Set Response = Client.Execute(Request)
    
    If Response.StatusCode = 200 Then
        MsgBox "LM Studio connection successful!" & vbCrLf & _
               "Using Model: " & lmStudioModel & vbCrLf & _
               "API URL: " & lmStudioApiUrl, vbInformation
        Debug.Print "Available LM Studio models: " & Response.content
    Else
        MsgBox "LM Studio connection failed. Make sure LM Studio server is running.", vbCritical
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error: " & Err.Description & vbCrLf & _
           "Make sure LM Studio is running with server mode enabled", vbCritical
End Sub

' ================================
' PRIVATE IMPLEMENTATION
' ================================

' Send multiple images to LM Studio
Private Function SendMultiPageToLMStudioAPI(ByRef base64Images() As String, ByVal pageCount As Integer, Optional ByVal model As String = "", Optional ByVal extractionMode As Boolean = False) As String
    On Error GoTo ErrorHandler

    OCRUtils.LogToFile "LMStudio: SendMultiPageToLMStudioAPI - Starting with " & pageCount & " images, model: " & model & ", extractionMode: " & extractionMode

    Dim Client As New WebClient
    Dim Request As New WebRequest
    Dim Response As WebResponse
    Dim requestBody As Object
    Dim messages As Object
    Dim message As Object
    Dim content As Collection
    Dim textPart As Object
    Dim imagePart As Object
    Dim prompt As String
    Dim i As Integer

    ' Build prompt based on extraction mode
    If extractionMode Then
        prompt = PromptTemplates.GetUniversalOCRExtractionPrompt(pageCount, OCRConfig.GetAllFieldSelectionStates())
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR+EXTRACTION prompt (Extract Fields = TRUE)"
    Else
        prompt = PromptTemplates.GetUniversalOCRPrompt(pageCount)
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR-ONLY prompt (Extract Fields = FALSE)"
    End If    ' Get current model
    Dim currentLMStudioModel As String
    If model <> "" Then
        ' Use the provided model
        currentLMStudioModel = model
        OCRUtils.LogToFile "LMStudio: Using provided model: " & currentLMStudioModel
    Else
        ' Fall back to getting the active model
        currentLMStudioModel = GetActiveLMStudioModelName()
        OCRUtils.LogToFile "LMStudio: No model specified, using active model: " & currentLMStudioModel
    End If

    If currentLMStudioModel = "" Then
        OCRUtils.LogToFile "LMStudio: No valid model determined. Aborting."
        SendMultiPageToLMStudioAPI = ""
        Exit Function
    End If

    ' Build request body following OpenAI chat completions format
    Set requestBody = CreateObject("Scripting.Dictionary")
    requestBody("model") = currentLMStudioModel
    requestBody("temperature") = 0.1
    requestBody("max_tokens") = 4096

    ' Build messages array with vision content
    Set messages = New Collection
    Set message = CreateObject("Scripting.Dictionary")
    message("role") = "user"

    ' Build content array with text and images
    Set content = New Collection
    ' Text prompt
    Set textPart = CreateObject("Scripting.Dictionary")
    textPart("type") = "text"
    textPart("text") = prompt
    content.Add textPart

    ' Add images
    For i = 1 To pageCount
        Set imagePart = CreateObject("Scripting.Dictionary")
        imagePart("type") = "image_url"
        Dim imageUrlDict As Object
        Set imageUrlDict = CreateObject("Scripting.Dictionary")
        imageUrlDict("url") = "data:image/jpeg;base64," & base64Images(i)
        Set imagePart("image_url") = imageUrlDict
        content.Add imagePart
    Next i

    ' Attach content array to message
    Set message("content") = content
    messages.Add message
    Set requestBody("messages") = messages    ' Add JSON format if needed based on extraction mode parameter - using string concatenation like Ollama
    If extractionMode Then
        Dim responseFormatJson As String
        responseFormatJson = "{" & _
                           """type"": ""json_schema""," & _
                           """json_schema"": {" & _
                           """name"": ""ocr_extraction""," & _
                           """strict"": true," & _
                           """schema"": {" & _
                           """type"": ""object""," & _
                           """properties"": {" & _
                           """letter_reference"": {""type"": ""string""}," & _
                           """letter_date"": {""type"": ""string""}," & _
                           """subject"": {""type"": ""string""}," & _
                           """references"": {""type"": ""string""}," & _
                           """body"": {""type"": ""string""}" & _
                           "}," & _
                           """required"": [""letter_reference"", ""letter_date"", ""subject"", ""body""]" & _
                           "}" & _
                           "}" & _
                           "}"

        ' Parse the JSON string into a Dictionary object for the request body
        Dim respFmt As Object
        Set respFmt = JsonConverter.ParseJson(responseFormatJson)
        Set requestBody("response_format") = respFmt
    End If

    ' Configure HTTP client
    Dim lmStudioUrl As String
    lmStudioUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_LMSTUDIO)
    If lmStudioUrl = "" Then
        SendMultiPageToLMStudioAPI = ""
        OCRUtils.LogToFile "LMStudio: API URL not configured."
        Exit Function
    End If
    
    ' Extract base URL from full endpoint
    Dim baseUrl As String
    Dim v1Pos As Long
    v1Pos = InStr(lmStudioUrl, "/v1/")
    If v1Pos > 0 Then
        baseUrl = Left(lmStudioUrl, v1Pos - 1)
    Else
        baseUrl = lmStudioUrl
    End If
    
    OCRUtils.LogToFile "LMStudio: Configuring HTTP client with base URL: " & baseUrl
    Client.baseUrl = baseUrl
    Client.TimeoutMS = 120000 ' 2 minute timeout
    Client.Insecure = True
    
    ' Configure request
    Request.Resource = "v1/chat/completions"
    Request.Method = VbWebMethod.vbWebHttpPost
    Request.Format = VbWebFormat.vbWebFormatJson
    Set Request.Body = requestBody

    ' Execute request
    Set Response = Client.Execute(Request)

    ' Handle response
    If Response.StatusCode = 200 Then
        OCRUtils.LogToFile "LMStudio: Received HTTP 200. Response length: " & Len(Response.content)
        SendMultiPageToLMStudioAPI = Response.content
    Else
        OCRUtils.LogToFile "LMStudio: HTTP " & Response.StatusCode & " - " & Response.StatusDescription
        OCRUtils.LogToFile "LMStudio: Error response: " & Response.content
        SendMultiPageToLMStudioAPI = ""
    End If
    Exit Function

ErrorHandler:
    OCRUtils.LogToFile "LMStudio: Error in SendMultiPageToLMStudioAPI: " & Err.Description & " (" & Err.Number & ")"
    SendMultiPageToLMStudioAPI = ""
End Function

' Send text-only request to LM Studio (for Docling post-processing)
Public Function SendTextToLMStudio(ByVal prompt As String, Optional ByVal modelName As String = "") As String
    On Error GoTo ErrorHandler
    
    Dim currentModel As String
    If modelName <> "" Then
        currentModel = modelName
        OCRUtils.LogToFile "LMStudio: Using provided model for text processing: " & modelName
    Else
        currentModel = GetActiveLMStudioModelName() ' Get from config
        OCRUtils.LogToFile "LMStudio: Using configured model for text processing: " & currentModel
    End If
    
    ' Build the request
    Dim requestBody As String
    requestBody = "{""model"": """ & currentModel & """, ""messages"": [{""role"": ""user"", ""content"": """ & OCRUtils.EscapeJsonString(prompt) & """}], ""stream"": false}"
    
    ' Get provider details
    Dim providerDetails As Object
    Set providerDetails = OCRConfig.GetProviderDetails(OCRConfig.PID_LMSTUDIO)
    
    ' Configure HTTP client
    Dim httpClient As Object
    Set httpClient = CreateObject("MSXML2.XMLHTTP")
    
    Dim baseUrl As String
    baseUrl = providerDetails("api_url")
    OCRUtils.LogToFile "LMStudio: Configuring HTTP client with base URL: " & baseUrl
    
    ' Send request
    With httpClient
        .Open "POST", baseUrl & "/v1/chat/completions", False
        .SetRequestHeader "Content-Type", "application/json"
        .Send requestBody
    End With
    
    ' Process response
    If httpClient.status = 200 Then
        OCRUtils.LogToFile "LMStudio: Received HTTP " & httpClient.status & ". Response length: " & Len(httpClient.responseText)
        
        Dim parsedResponse As Object
        Set parsedResponse = JsonConverter.ParseJson(httpClient.responseText)
        
        ' Extract the response content - Fixed indexing and error handling
        On Error Resume Next
        If parsedResponse.Exists("choices") Then
            Dim choices As Object
            Set choices = parsedResponse("choices")
            If TypeName(choices) = "Collection" And choices.Count > 0 Then
                Dim firstChoice As Object
                Set firstChoice = choices(1) ' VBA Collections are 1-based
                If firstChoice.Exists("message") Then
                    Dim message As Object
                    Set message = firstChoice("message")
                    If message.Exists("content") Then
                        SendTextToLMStudio = message("content")
                        OCRUtils.LogToFile "LMStudio: Successfully extracted text response, length: " & Len(SendTextToLMStudio)
                    Else
                        OCRUtils.LogToFile "LMStudio: No content field in message"
                        SendTextToLMStudio = ""
                    End If
                Else
                    OCRUtils.LogToFile "LMStudio: No message field in choice"
                    SendTextToLMStudio = ""
                End If
            Else
                OCRUtils.LogToFile "LMStudio: No choices in response or empty choices"
                SendTextToLMStudio = ""
            End If
        Else
            OCRUtils.LogToFile "LMStudio: No choices field in response"
            SendTextToLMStudio = ""
        End If
        On Error GoTo ErrorHandler
        
    Else
        OCRUtils.LogToFile "LMStudio: HTTP Error " & httpClient.status & ": " & httpClient.statusText
        SendTextToLMStudio = ""
    End If
    
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "LMStudio: Error in SendTextToLMStudio: " & Err.Description
    SendTextToLMStudio = ""
End Function

' Get JSON string of available LM Studio models
Public Function GetLMStudioModelsJson() As String
    On Error GoTo ErrorHandler
    
    Dim Client As New WebClient
    Dim Request As New WebRequest
    Dim Response As WebResponse
    Dim baseUrl As String
    Dim startTime As Double
    
    ' Initialize return value
    GetLMStudioModelsJson = ""
    startTime = Timer
    
    OCRUtils.LogToFile "LMStudio: GetLMStudioModelsJson - Starting model detection with VBA-Web"
    
    ' Validate configuration first
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_LMSTUDIO)
    If baseUrl = "" Then
        OCRUtils.LogToFile "LMStudio: ERROR - API URL not configured. Cannot fetch models."
        Exit Function
    End If
    
    ' Configure VBA-Web client
    Client.TimeoutMS = 5000 ' 5 second timeout
    Client.Insecure = True ' Allow self-signed certificates for local development
    
    ' Extract base URL from full endpoint
    Dim clientBaseUrl As String
    Dim v1Pos As Long
    v1Pos = InStr(baseUrl, "/v1/")
    If v1Pos > 0 Then
        clientBaseUrl = Left(baseUrl, v1Pos - 1) ' e.g., "http://localhost:1234"
    Else
        If Right(baseUrl, 1) = "/" Then
            clientBaseUrl = Left(baseUrl, Len(baseUrl) - 1)
        Else
            clientBaseUrl = baseUrl
        End If
    End If
    
    Client.baseUrl = clientBaseUrl
    
    ' Configure request for models endpoint
    Request.Resource = "v1/models"
    Request.Method = VbWebMethod.vbWebHttpGet
    Request.Format = VbWebFormat.vbWebFormatJson
    
    OCRUtils.LogToFile "LMStudio: Attempting to fetch models from: " & Client.baseUrl & "/v1/models"
    
    ' Execute request
    Set Response = Client.Execute(Request)
    
    ' Process response
    If Response.StatusCode = 200 Then
        If Len(Response.content) > 0 Then
            GetLMStudioModelsJson = Response.content
            OCRUtils.LogToFile "LMStudio: SUCCESS - Fetched models via VBA-Web. Response length: " & Len(Response.content)
            ' Log first 200 chars for debugging
            OCRUtils.LogToFile "LMStudio: Response preview: " & Left(Response.content, 200) & IIf(Len(Response.content) > 200, "...", "")
        Else
            OCRUtils.LogToFile "LMStudio: ERROR - Empty response from LM Studio API"
        End If
    ElseIf Response.StatusCode = 404 Then
        OCRUtils.LogToFile "LMStudio: ERROR - Endpoint not found (404). Check if LM Studio server is running."
    ElseIf Response.StatusCode = VbWebStatusCode.vbWebStatusRequestTimeout Then
        OCRUtils.LogToFile "LMStudio: ERROR - Request timed out. LM Studio may not be running or is unresponsive."
    ElseIf Response.StatusCode = 0 Then
        OCRUtils.LogToFile "LMStudio: ERROR - Connection failed. LM Studio may not be running at: " & Client.baseUrl
    Else
        OCRUtils.LogToFile "LMStudio: ERROR - HTTP " & Response.StatusCode & " - " & Response.StatusDescription
    End If
    
    Dim totalTime As Double
    totalTime = Timer - startTime
    OCRUtils.LogToFile "LMStudio: Model detection completed in " & Format(totalTime, "0.00") & " seconds"
    
    Exit Function
    
ErrorHandler:
    Dim errorMsg As String
    errorMsg = "LMStudio: ERROR in GetLMStudioModelsJson (VBA-Web): " & Err.Description & " (Number: " & Err.Number & ")"
    OCRUtils.LogToFile errorMsg
    GetLMStudioModelsJson = ""
End Function

' Get a collection of available LM Studio model names
Public Function GetLMStudioModelNames() As Collection
    On Error GoTo ErrorHandler
    Dim jsonString As String
    Dim parsedResponse As Object
    Dim dataArray As Object
    Dim modelEntry As Object
    Dim i As Long
    Dim modelCount As Long
    
    ' Initialize with empty collection
    Set GetLMStudioModelNames = New Collection
    
    OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: Starting model name extraction"
    
    ' Get JSON from API
    jsonString = GetLMStudioModelsJson()
    If jsonString = "" Then
        OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: No JSON response from API - likely LM Studio not running or API error"
        Exit Function ' Return empty collection
    End If
    
    ' Parse JSON
    Set parsedResponse = JsonConverter.ParseJson(jsonString)
    
    If parsedResponse Is Nothing Then
        OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: JSON parser returned Nothing"
        Exit Function
    End If
    
    ' LM Studio returns models in "data" array
    If Not parsedResponse.Exists("data") Then
        OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: Missing 'data' key in JSON response"
        Exit Function
    End If
    
    Set dataArray = parsedResponse("data")
    If dataArray Is Nothing Then
        OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: 'data' key exists but value is null"
        Exit Function
    End If
    
    modelCount = dataArray.Count
    If modelCount = 0 Then
        OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: 'data' array is empty - no models loaded"
        Exit Function
    End If
    
    OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: Processing " & modelCount & " model entries"
    
    ' Extract model names
    For i = 1 To modelCount
        Set modelEntry = dataArray(i)
        
        If Not modelEntry Is Nothing Then
            If modelEntry.Exists("id") Then
                Dim modelId As String
                modelId = CStr(modelEntry("id"))
                If Len(modelId) > 0 Then
                    GetLMStudioModelNames.Add modelId
                    OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: Added model: " & modelId
                End If
            End If
        End If
    Next i
    
    OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: SUCCESS - Extracted " & GetLMStudioModelNames.Count & " model names"
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "LMStudio.GetLMStudioModelNames: Error: " & Err.Description
    Set GetLMStudioModelNames = New Collection
End Function

' Helper function to get a valid LM Studio model name
Private Function GetActiveLMStudioModelName() As String
    On Error GoTo ErrorHandler
    Dim modelsJson As String
    Dim parsedResponse As Object
    Dim defaultModelName As String
    Dim selectedModelName As String
    Dim i As Long
    Dim modelEntry As Object
    Dim modelsList As Object
    
    selectedModelName = "" ' Initialize
    
    ' Get available models
    modelsJson = GetLMStudioModelsJson()
    
    If modelsJson = "" Then
        OCRUtils.LogToFile "LMStudio: Could not retrieve model list from LM Studio server."
        GetActiveLMStudioModelName = ""
        Exit Function
    End If
    
    Set parsedResponse = JsonConverter.ParseJson(modelsJson)
    
    If parsedResponse Is Nothing Then
        OCRUtils.LogToFile "LMStudio: Failed to parse JSON for models list"
        GetActiveLMStudioModelName = ""
        Exit Function
    End If
    
    If Not parsedResponse.Exists("data") Then
        OCRUtils.LogToFile "LMStudio: Parsed JSON does not contain 'data' array"
        GetActiveLMStudioModelName = ""
        Exit Function
    End If
    
    Set modelsList = parsedResponse("data")
    
    If modelsList Is Nothing Or modelsList.Count = 0 Then
        OCRUtils.LogToFile "LMStudio: No models loaded in LM Studio server."
        GetActiveLMStudioModelName = ""
        Exit Function
    End If
    
    ' Try to find the default configured model
    defaultModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_LMSTUDIO)
    OCRUtils.LogToFile "LMStudio: Configured default model: '" & defaultModelName & "'"
    OCRUtils.LogToFile "LMStudio: Checking against " & modelsList.Count & " loaded model(s)."
    
    For i = 1 To modelsList.Count
        Set modelEntry = modelsList(i)
        If modelEntry.Exists("id") Then
            OCRUtils.LogToFile "LMStudio: Checking model: '" & modelEntry("id") & "'"
            If LCase(modelEntry("id")) = LCase(defaultModelName) Then
                selectedModelName = modelEntry("id")
                OCRUtils.LogToFile "LMStudio: Found configured default model '" & selectedModelName & "'"
                Exit For
            End If
        End If
    Next i
    
    ' If default model not found, use the first available model
    If selectedModelName = "" Then
        OCRUtils.LogToFile "LMStudio: Configured default model '" & defaultModelName & "' not found."
        Set modelEntry = modelsList(1)
        If modelEntry.Exists("id") Then
            selectedModelName = modelEntry("id")
            OCRUtils.LogToFile "LMStudio: Using first available model: '" & selectedModelName & "'"
        End If
    End If
    
    If selectedModelName = "" Then
        OCRUtils.LogToFile "LMStudio: CRITICAL - Could not determine a valid model name to use."
    Else
        OCRUtils.LogToFile "LMStudio: Selected model: '" & selectedModelName & "'"
    End If
    
    GetActiveLMStudioModelName = selectedModelName
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "LMStudio: Error in GetActiveLMStudioModelName: " & Err.Description
    GetActiveLMStudioModelName = ""
End Function

' Check if LM Studio service is running
Public Function isLMStudioRunning(Optional quickCheck As Boolean = False) As Boolean
    On Error GoTo ErrorHandler
    
    ' Check cache first
    Dim cacheResult As Variant
    cacheResult = OCRConfig.GetCachedProviderStatus(OCRConfig.PID_LMSTUDIO)
    If cacheResult(1) Then ' Is cached
        isLMStudioRunning = cacheResult(0)
        Exit Function
    End If
    
    Dim Client As New WebClient
    Dim Request As New WebRequest
    Dim Response As WebResponse
    Dim baseUrl As String
    
    ' Initialize
    isLMStudioRunning = False
    
    OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: Starting service health check"
    
    ' Get base URL
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_LMSTUDIO)
    If baseUrl = "" Then
        OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: No API URL configured"
        Exit Function
    End If
    
    ' Configure client for quick health check
    Client.TimeoutMS = IIf(quickCheck, 500, 2000) ' 500ms for quick check, 2s for normal
    Client.Insecure = True
    
    ' Extract base URL
    Dim clientBaseUrl As String
    Dim v1Pos As Long
    v1Pos = InStr(baseUrl, "/v1/")
    If v1Pos > 0 Then
        clientBaseUrl = Left(baseUrl, v1Pos - 1)
    Else
        clientBaseUrl = baseUrl
    End If
    
    Client.baseUrl = clientBaseUrl
    
    ' Configure request - check models endpoint as health check
    Request.Resource = "v1/models"
    Request.Method = VbWebMethod.vbWebHttpGet
    Request.Format = VbWebFormat.vbWebFormatJson
    
    OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: Checking service at: " & Client.baseUrl & "/v1/models"
    
    ' Execute request
    Set Response = Client.Execute(Request)
    
    ' Process response
    If Response.StatusCode = 200 Then
        isLMStudioRunning = True
        OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: Service is RUNNING"
    ElseIf Response.StatusCode = VbWebStatusCode.vbWebStatusRequestTimeout Then
        isLMStudioRunning = False
        OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: Service health check TIMED OUT"
    ElseIf Response.StatusCode = 0 Then
        isLMStudioRunning = False
        OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: Connection FAILED - service not running"
    Else
        isLMStudioRunning = False
        OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: Service not responding properly (HTTP " & Response.StatusCode & ")"
    End If
    
    ' Update cache with result
    OCRConfig.UpdateProviderStatusCache OCRConfig.PID_LMSTUDIO, isLMStudioRunning
    
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "LMStudio.IsLMStudioRunning: ERROR in health check: " & Err.Description
    isLMStudioRunning = False
    ' Update cache even on error
    OCRConfig.UpdateProviderStatusCache OCRConfig.PID_LMSTUDIO, isLMStudioRunning
End Function

' Check if LM Studio model supports vision
Private Function IsVisionModelAvailable() As Boolean
    Dim activeModelName As String
    activeModelName = GetActiveLMStudioModelName()
    
    If activeModelName = "" Then
        IsVisionModelAvailable = False
        Exit Function
    End If
    
    ' Check if model name suggests vision capabilities
    ' Common vision model indicators: vision, vl, llava, bakllava, cogvlm, etc.
    Dim lowerModelName As String
    lowerModelName = LCase(activeModelName)
    
    IsVisionModelAvailable = (InStr(lowerModelName, "vision") > 0 Or _
                             InStr(lowerModelName, "vl") > 0 Or _
                             InStr(lowerModelName, "llava") > 0 Or _
                             InStr(lowerModelName, "bakllava") > 0 Or _
                             InStr(lowerModelName, "cogvlm") > 0)
    
    If IsVisionModelAvailable Then
        OCRUtils.LogToFile "LMStudio: Model '" & activeModelName & "' appears to be vision-capable."
    Else
        OCRUtils.LogToFile "LMStudio: Model '" & activeModelName & "' does not appear to be vision-capable."
    End If
End Function


' ================================
' STANDARDIZED PROVIDER INTERFACE
' ================================

' Check if the LM Studio provider is available (i.e., service is running)
Private Function IsProviderAvailable() As Boolean
    IsProviderAvailable = isLMStudioRunning()
End Function

' Get a collection of available model names for this provider
Private Function GetAvailableModels() As Collection
    Set GetAvailableModels = GetLMStudioModelNames()
End Function

' Get the type of this provider
Private Function GetProviderType() As String
    GetProviderType = "local" ' LM Studio is a local provider
End Function




