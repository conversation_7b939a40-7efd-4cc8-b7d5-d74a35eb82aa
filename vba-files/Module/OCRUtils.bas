Attribute VB_Name = "OCRUtils"
'
' OCR Utilities Module
' Shared utility functions for file operations, PDF processing, and data conversion
' Author: AI Assistant
'

Option Explicit

' Global logging enabled flag
Public LoggingEnabled As Boolean

' Ghostscript API instance (singleton pattern)
Private m_GhostscriptAPI As clsGhostscriptAPI

' Get or create Ghostscript API instance
Private Function GetGhostscriptAPI() As clsGhostscriptAPI
    If m_GhostscriptAPI Is Nothing Then
        Set m_GhostscriptAPI = New clsGhostscriptAPI
    End If
    Set GetGhostscriptAPI = m_GhostscriptAPI
End Function

' Enable or disable logging
Public Sub SetLogging(enabled As Boolean)
    LoggingEnabled = enabled
    
    ' Log the status change
    If enabled Then
        Debug.Print "OCR Logging enabled"
        LogToFile "OCR Logging enabled"
    Else
        Debug.Print "OCR Logging disabled"
    End If
End Sub

' Initialize logging (call this at application startup)
Public Sub InitializeLogging()
    ' Default to enabled
    LoggingEnabled = True
    
    ' Create logs directory if it doesn't exist
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    If Not fso.FolderExists(ThisWorkbook.path & "\logs") Then
        fso.CreateFolder ThisWorkbook.path & "\logs"
    End If
    
    ' Log initialization
    LogToFile "=== OCR LOGGING INITIALIZED ==="
    LogToFile "Workbook path: " & ThisWorkbook.path
    LogToFile "Log file: " & GetFullLogPath()
End Sub

' ================================
' FILE OPERATIONS
' ================================

' Check if file exists
Public Function FileExists(ByVal FilePath As String) As Boolean
    On Error Resume Next
    FileExists = (Dir(FilePath) <> "")
    On Error GoTo 0
End Function

' Log messages to file for debugging
Public Sub LogToFile(ByVal logMessage As String, Optional ByVal logFileName As String = "")
    ' Exit if logging is disabled
    If Not LoggingEnabled Then
        Debug.Print "Logging disabled: " & logMessage
        Exit Sub
    End If
    
    On Error GoTo ErrorHandler
    
    Dim FilePath As String
    Dim FileNum As Integer
    Dim fso As Object
    Dim configLogPath As String
    
    ' Get log file path from config or use provided filename
    If logFileName = "" Then
        configLogPath = OCRConfig.GetLogFilePath()
        ' Handle relative path
        If InStr(configLogPath, "/") > 0 Then
            configLogPath = Replace(configLogPath, "/", "\")
        End If
        ' Check if path is absolute or relative
        If InStr(configLogPath, ":") > 0 Or Left(configLogPath, 2) = "\\" Then
            ' Absolute path
            FilePath = configLogPath
        Else
            ' Relative path
            FilePath = ThisWorkbook.path & "\" & configLogPath
        End If
    Else
        ' Use provided filename in logs directory
        FilePath = ThisWorkbook.path & "\logs\" & logFileName
    End If
    
    ' Ensure directory exists
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim logDir As String
    logDir = fso.GetParentFolderName(FilePath)
    If Not fso.FolderExists(logDir) Then
        fso.CreateFolder logDir
    End If
    
    FileNum = FreeFile
    
    Open FilePath For Append As #FileNum
    Print #FileNum, Format(Now, "yyyy-mm-dd hh:nn:ss") & " - " & logMessage
    Close #FileNum
    
    ' Also print to immediate window for debugging
    Debug.Print Format(Now, "yyyy-mm-dd hh:nn:ss") & " - " & logMessage
    
    Exit Sub
    
ErrorHandler:
    If FileNum > 0 Then Close #FileNum
    Debug.Print "Error logging to file: " & Err.Description & " - Original message: " & logMessage
End Sub


' ================================
' PDF PROCESSING FUNCTIONS
' ================================

' Get PDF page count with caching to avoid redundant calls
Public Function GetPDFPageCount(ByVal pdfPath As String) As Integer
    On Error GoTo ErrorHandler
    
    ' Try API approach first (though currently not implemented due to stdout capture complexity)
    Dim gsAPI As clsGhostscriptAPI
    Set gsAPI = GetGhostscriptAPI()
    
    If gsAPI.isAvailable Then
        Dim apiResult As Long
        apiResult = gsAPI.GetPDFPageCount(pdfPath)
        If apiResult > 0 Then
            LogToFile "GetPDFPageCount: Successfully used API approach, page count: " & apiResult
            GetPDFPageCount = CInt(apiResult)
            Exit Function
        End If
        ' API returns -1 when not implemented, so continue to fallback
    End If
    
    Static lastPdfPath As String
    Static lastPageCount As Integer
    Static lastCheck As Double
    
    ' Check cache first (valid for 5 minutes)
    If lastPdfPath = pdfPath And lastPageCount > 0 And (Timer - lastCheck) < 300 Then
        GetPDFPageCount = lastPageCount
        LogToFile "GetPDFPageCount: Using cached page count " & lastPageCount & " for: " & pdfPath
        Exit Function
    End If
    
    Dim WshShell As Object
    Dim oExec As Object
    Dim sOutput As String
    Dim ghostscriptCmd As String
    Dim startTime As Double
    Dim gsPath As String
    Dim i As Integer

    GetPDFPageCount = 1 ' Default value in case of any failure
    gsPath = GetGhostscriptPath()

    If gsPath = "" Or Not FileExists(gsPath) Then
        LogToFile "GetPDFPageCount: Ghostscript executable not found or path is empty. Defaulting to 1 page."
        Exit Function
    End If

    ' Use a more reliable method: use pdfinfo utility or direct PostScript query
    ' First try using %d instead of %02d pattern to see all pages, then count the files
    Dim tempTestDir As String
    Dim testCmd As String
    Dim testImagePattern As String
    Dim safeFileName As String
    
    tempTestDir = Environ("TMP")
    If Right(tempTestDir, 1) <> "\" Then tempTestDir = tempTestDir & "\"
    
    ' Create a safe filename for the test (avoid special characters)
    safeFileName = "test_" & Format(Now, "yyyymmddhhnnss") & "_page"
    testImagePattern = tempTestDir & safeFileName & "%d.png"
    
    ' Properly escape the PDF path and use %d for unlimited page numbering
    ' Use lower DPI for counting to save time and space
    testCmd = Chr(34) & gsPath & Chr(34) & " -dNOPAUSE -dBATCH -dSAFER -sDEVICE=png16m -r50 " & _
              "-dFirstPage=1 -dLastPage=20 " & _
              "-sOutputFile=" & Chr(34) & testImagePattern & Chr(34) & " " & Chr(34) & pdfPath & Chr(34)
    
    ghostscriptCmd = testCmd

    LogToFile "GetPDFPageCount: Executing improved Ghostscript command: " & ghostscriptCmd
    
    ' Execute the test conversion command with proper quoting
    Set WshShell = CreateObject("WScript.Shell")
    Set oExec = WshShell.Exec("cmd.exe /c """ & ghostscriptCmd & """")

    startTime = Timer
    ' Wait for the command to finish, with a longer timeout for multi-page docs
    Do While oExec.status = 0 And (Timer - startTime < 60)
        Sleep 200
    Loop

    If oExec.status = 0 Then ' Still running, timeout occurred
        LogToFile "GetPDFPageCount: Ghostscript command timed out for " & pdfPath
        oExec.Terminate
        GoTo ErrorHandler_Cleanup
    End If
    
    ' Get any output from the command
    If Not oExec.StdOut.AtEndOfStream Then
        sOutput = oExec.StdOut.ReadAll
        LogToFile "GetPDFPageCount: Ghostscript StdOut: " & sOutput
    End If
    
    If Not oExec.StdErr.AtEndOfStream Then
        Dim errOutput As String
        errOutput = oExec.StdErr.ReadAll
        LogToFile "GetPDFPageCount: Ghostscript StdErr: " & errOutput
    End If

    ' Wait a moment for files to be written
    WaitSeconds 2
    
    ' Count the created test image files using the new pattern
    Dim testFileCount As Integer
    Dim checkFile As String
    testFileCount = 0
    
    For i = 1 To 50 ' Check up to 50 pages max
        checkFile = tempTestDir & safeFileName & CStr(i) & ".png"
        If FileExists(checkFile) Then
            testFileCount = testFileCount + 1
            LogToFile "GetPDFPageCount: Found test image file: " & checkFile
            ' Clean up test file immediately
            On Error Resume Next
            Kill checkFile
            On Error GoTo ErrorHandler
        Else
            Exit For ' No more pages
        End If
    Next i
    
    If testFileCount > 0 Then
        GetPDFPageCount = testFileCount
        LogToFile "GetPDFPageCount: Successfully counted " & testFileCount & " pages by examining created image files"
    Else
        ' Fallback: Try a simple PostScript command as last resort
        LogToFile "GetPDFPageCount: No test image files created. Trying PostScript method as fallback."
        GetPDFPageCount = GetPDFPageCountUsingPostScript(pdfPath)
    End If

    ' Cache the result
    lastPdfPath = pdfPath
    lastPageCount = GetPDFPageCount
    lastCheck = Timer
    
    LogToFile "GetPDFPageCount: Final determined page count for '" & pdfPath & "': " & GetPDFPageCount
    
ErrorHandler_Cleanup:
    ' Clean up any remaining test files
    On Error Resume Next
    For i = 1 To 50
        checkFile = tempTestDir & safeFileName & CStr(i) & ".png"
        If FileExists(checkFile) Then Kill checkFile
    Next i
    On Error GoTo 0
    
    If Not oExec Is Nothing Then
        If oExec.status = 0 Then oExec.Terminate
    End If
    Set oExec = Nothing
    Set WshShell = Nothing
    Exit Function
    
ErrorHandler:
    GetPDFPageCount = 1 ' Default to 1 on any error
    LogToFile "ERROR in GetPDFPageCount for '" & pdfPath & "': " & Err.Description & ". Defaulting to 1 page."
    Resume ErrorHandler_Cleanup
End Function

' Fallback method to get PDF page count using PostScript
Private Function GetPDFPageCountUsingPostScript(ByVal pdfPath As String) As Integer
    On Error GoTo ErrorHandler
    
    Dim WshShell As Object
    Dim oExec As Object
    Dim sOutput As String
    Dim ghostscriptCmd As String
    Dim gsPath As String
    Dim startTime As Double
    
    GetPDFPageCountUsingPostScript = 1 ' Default
    gsPath = GetGhostscriptPath()
    
    ' Simple PostScript command to get page count with proper quoting
    ghostscriptCmd = Chr(34) & gsPath & Chr(34) & " -q -dNODISPLAY -dBATCH -dSAFER " & _
                    "-c " & Chr(34) & "(" & pdfPath & ") (r) file runpdfbegin pdfpagecount = quit" & Chr(34)
    
    LogToFile "GetPDFPageCountUsingPostScript: Executing: " & ghostscriptCmd
    
    Set WshShell = CreateObject("WScript.Shell")
    Set oExec = WshShell.Exec("cmd.exe /c """ & ghostscriptCmd & """")
    
    startTime = Timer
    Do While oExec.status = 0 And (Timer - startTime < 30)
        Sleep 100
    Loop
    
    If oExec.status <> 0 And Not oExec.StdOut.AtEndOfStream Then
        sOutput = Trim(oExec.StdOut.ReadAll)
        LogToFile "GetPDFPageCountUsingPostScript: Output: " & sOutput
        
        If IsNumeric(sOutput) And Val(sOutput) > 0 Then
            GetPDFPageCountUsingPostScript = CInt(Val(sOutput))
            LogToFile "GetPDFPageCountUsingPostScript: Successfully got page count: " & GetPDFPageCountUsingPostScript
        Else
            LogToFile "GetPDFPageCountUsingPostScript: Non-numeric output, defaulting to 1"
        End If
    Else
        LogToFile "GetPDFPageCountUsingPostScript: No output or timeout, defaulting to 1"
    End If
    
    Exit Function
    
ErrorHandler:
    GetPDFPageCountUsingPostScript = 1
    LogToFile "ERROR in GetPDFPageCountUsingPostScript: " & Err.Description
End Function

' Convert all PDF pages to images at once, then return specific page path
Public Function ConvertPDFPageToImage(ByVal pdfPath As String, ByVal pageNum As Integer) As String
    On Error GoTo ErrorHandler
    
    ' Try API approach first to eliminate screen flashing
    Dim gsAPI As clsGhostscriptAPI
    Set gsAPI = GetGhostscriptAPI()
    
    If gsAPI.isAvailable Then
        Dim apiResult As String
        apiResult = gsAPI.ConvertPDFPageToImage(pdfPath, pageNum)
        If apiResult <> "" Then
            LogToFile "ConvertPDFPageToImage: Successfully used API approach for page " & pageNum
            ConvertPDFPageToImage = apiResult
            Exit Function
        Else
            LogToFile "ConvertPDFPageToImage: API approach failed, falling back to terminal method"
        End If
    End If
    
    Static lastPdfPath As String
    Static lastTimestamp As String
    Static lastPageCount As Integer
    
    Dim tempDir As String
    Dim baseImagePath As String
    Dim specificImagePath As String
    Dim ghostscriptCmd As String
    Dim currentTimestamp As String
    Dim WshShell As Object
    Dim oExec As Object
    Dim startTime As Double
    Dim pageCount As Integer
    
    tempDir = Environ("TMP")
    If Right(tempDir, 1) <> "\" Then tempDir = tempDir & "\"
    
    currentTimestamp = Format(Now, "yyyymmddhhnnss")
    
    ' Check if we need to convert all pages (first time or different PDF)
    If lastPdfPath <> pdfPath Or lastTimestamp = "" Or Abs(Timer - Val(Mid(lastTimestamp, 9, 6))) > 300 Then
        ' Reset static variables for new conversion
        lastPdfPath = pdfPath
        lastTimestamp = currentTimestamp
        pageCount = GetPDFPageCount(pdfPath)
        lastPageCount = pageCount
        
        LogToFile "ConvertPDFPageToImage: Starting new conversion of " & pageCount & " pages for: " & pdfPath
        LogToFile "ConvertPDFPageToImage: Using timestamp: " & currentTimestamp
        
        ' Use %d pattern for better compatibility and handle all pages
        baseImagePath = tempDir & "ocr_temp_" & currentTimestamp & "_%d.png"
        
        ' Improved Ghostscript command with proper quoting
        ghostscriptCmd = Chr(34) & GetGhostscriptPath() & Chr(34) & " " & _
                        "-dNOPAUSE -dBATCH -dSAFER -sDEVICE=png16m " & _
                        "-r" & OCRConfig.GetImageDPI() & " " & _
                        "-dFirstPage=1 -dLastPage=" & pageCount & " " & _
                        "-sOutputFile=" & Chr(34) & baseImagePath & Chr(34) & " " & _
                        Chr(34) & pdfPath & Chr(34)
        
        LogToFile "ConvertPDFPageToImage: Executing: " & ghostscriptCmd
        
        ' Use WScript.Shell.Exec for better control with proper command quoting
        Set WshShell = CreateObject("WScript.Shell")
        Set oExec = WshShell.Exec("cmd.exe /c """ & ghostscriptCmd & """")
        
        startTime = Timer
        Do While oExec.status = 0 And (Timer - startTime < 120) ' 2 minute timeout
            Sleep 500
        Loop
        
        If oExec.status = 0 Then
            LogToFile "ConvertPDFPageToImage: Command timed out"
            oExec.Terminate
        End If
        
        ' Log any output
        If Not oExec.StdOut.AtEndOfStream Then
            Dim cmdOutput As String
            cmdOutput = oExec.StdOut.ReadAll
            LogToFile "ConvertPDFPageToImage: Command output: " & cmdOutput
        End If
        
        WaitSeconds 3 ' Wait for files to be written
    Else
        LogToFile "ConvertPDFPageToImage: Reusing existing conversion with timestamp: " & lastTimestamp
    End If
    
    ' Return path to specific page using the current/last timestamp
    specificImagePath = tempDir & "ocr_temp_" & lastTimestamp & "_" & CStr(pageNum) & ".png"
    
    If FileExists(specificImagePath) Then
        ConvertPDFPageToImage = specificImagePath
        LogToFile "ConvertPDFPageToImage: Successfully found page " & pageNum & " at: " & specificImagePath
    Else
        ' Try alternative naming patterns
        Dim altImagePath As String
        altImagePath = tempDir & "ocr_temp_" & lastTimestamp & "_" & Format(pageNum, "00") & ".png"
        If FileExists(altImagePath) Then
            ConvertPDFPageToImage = altImagePath
            LogToFile "ConvertPDFPageToImage: Found page " & pageNum & " at alternative path: " & altImagePath
        Else
            ConvertPDFPageToImage = ""
            LogToFile "ConvertPDFPageToImage: Page " & pageNum & " NOT found. Expected at: " & specificImagePath
            LogToFile "ConvertPDFPageToImage: Also tried: " & altImagePath
        End If
    End If
    
    Exit Function
    
ErrorHandler:
    ConvertPDFPageToImage = ""
    LogToFile "ConvertPDFPageToImage ERROR: " & Err.Description
End Function

' Get Ghostscript path
Public Function GetGhostscriptPath() As String
    Dim commonPaths As Variant
    Dim i As Integer
    
    commonPaths = Array( _
        "C:\Program Files\gs\gs10.05.1\bin\gswin64c.exe", _
        "C:\Program Files\gs\gs10.02.0\bin\gswin64c.exe", _
        "C:\Program Files\gs\gs10.01.2\bin\gswin32c.exe", _
        "C:\Program Files (x86)\gs\gs10.05.1\bin\gswin32c.exe", _
        "C:\Program Files (x86)\gs\gs10.02.0\bin\gswin32c.exe", _
        "gswin64c.exe", _
        "gswin32c.exe", _
        "gs.exe" _
    )
    
    For i = LBound(commonPaths) To UBound(commonPaths)
        If FileExists(commonPaths(i)) Or InStr(commonPaths(i), ":\") = 0 Then
            GetGhostscriptPath = commonPaths(i)
            Exit Function
        End If
    Next i
    
    GetGhostscriptPath = "gswin64c.exe"
End Function

' ================================
' BASE64 ENCODING FUNCTIONS
' ================================

' Encode PDF to Base64
Public Function EncodePDFToBase64(ByVal pdfPath As String) As String
    On Error GoTo ErrorHandler
    
    Dim FileData() As Byte
    Dim FileNum As Integer
    
    FileNum = FreeFile
    Open pdfPath For Binary Access Read As FileNum
    ReDim FileData(LOF(FileNum) - 1)
    Get FileNum, , FileData
    Close FileNum
    
    EncodePDFToBase64 = EncodeBase64Binary(FileData)
    Debug.Print "PDF encoded to Base64, length: " & Len(EncodePDFToBase64)
    
    Exit Function
    
ErrorHandler:
    If FileNum > 0 Then Close FileNum
    EncodePDFToBase64 = ""
End Function

' Encode image to Base64
Public Function EncodeImageToBase64(ByVal imagePath As String) As String
    On Error GoTo ErrorHandler
    
    Dim FileData() As Byte
    Dim FileNum As Integer
    
    FileNum = FreeFile
    Open imagePath For Binary Access Read As FileNum
    ReDim FileData(LOF(FileNum) - 1)
    Get FileNum, , FileData
    Close FileNum
    
    EncodeImageToBase64 = EncodeBase64Binary(FileData)
    
    Exit Function
    
ErrorHandler:
    If FileNum > 0 Then Close FileNum
    EncodeImageToBase64 = ""
End Function

' Base64 encode binary data
Public Function EncodeBase64Binary(ByRef binaryData() As Byte) As String
    On Error GoTo ErrorHandler
    
    Dim Node As Object
    Set Node = CreateObject("Microsoft.XMLDOM").createElement("base64")
    
    Node.DataType = "bin.base64"
    Node.nodeTypedValue = binaryData
    
    EncodeBase64Binary = Replace(Node.Text, vbLf, "")
    
    Exit Function
    
ErrorHandler:
    EncodeBase64Binary = ""
End Function

' ================================
' JSON UTILITIES
' ================================

' Convert backtick-delimited strings to proper JSON double quotes
Private Function ConvertBackticksToQuotes(ByVal jsonText As String) As String
    On Error GoTo ErrorHandler
    
    Dim Result As String
    Dim i As Long
    Dim inString As Boolean
    Dim inBacktickString As Boolean
    Dim char As String
    Dim prevChar As String
    
    Result = ""
    inString = False
    inBacktickString = False
    prevChar = ""
    
    ' Process character by character
    For i = 1 To Len(jsonText)
        char = Mid(jsonText, i, 1)
        
        ' Track if we're inside a regular double-quoted string
        If char = """" And prevChar <> "\" And Not inBacktickString Then
            inString = Not inString
            Result = Result & char
        ' Handle backticks when not inside a double-quoted string
        ElseIf char = "`" And Not inString Then
            ' Check if this is likely a string delimiter (after : or [ or ,)
            If i > 1 Then
                Dim contextPos As Long
                For contextPos = i - 1 To 1 Step -1
                    Dim contextChar As String
                    contextChar = Mid(jsonText, contextPos, 1)
                    If contextChar <> " " And contextChar <> vbCr And contextChar <> vbLf And contextChar <> vbTab Then
                        ' Found non-whitespace character
                        If contextChar = ":" Or contextChar = "[" Or contextChar = "," Then
                            ' This backtick is likely a string delimiter
                            inBacktickString = Not inBacktickString
                            Result = Result & """"  ' Replace with double quote
                            GoTo NextChar
                        End If
                        Exit For
                    End If
                Next contextPos
            End If
            ' If we're already in a backtick string, this closes it
            If inBacktickString Then
                inBacktickString = False
                Result = Result & """"  ' Replace with double quote
            Else
                ' Not a string delimiter context, keep the backtick
                Result = Result & char
            End If
        Else
            ' Normal character
            Result = Result & char
        End If
        
NextChar:
        prevChar = char
    Next i
    
    ConvertBackticksToQuotes = Result
    
    ' Log if we made any changes
    If Result <> jsonText Then
        LogToFile "ConvertBackticksToQuotes: Replaced backtick delimiters with double quotes"
    End If
    
    Exit Function
    
ErrorHandler:
    LogToFile "ConvertBackticksToQuotes: ERROR - " & Err.Description
    ConvertBackticksToQuotes = jsonText ' Return original on error
End Function

' Get JSON value safely
Public Function GetJsonValue(ByVal jsonObject As Object, ByVal Key As String) As String
    On Error Resume Next
    
    If jsonObject.Exists(Key) Then
        GetJsonValue = CStr(jsonObject(Key))
    Else
        GetJsonValue = "Not Found"
    End If
    
    If GetJsonValue = "" Or GetJsonValue = "null" Then
        GetJsonValue = "Not Found"
    End If
    
    On Error GoTo 0
End Function

' Clean JSON from markdown or text response - Simplified version
Public Function CleanJsonFromMarkdown(ByVal markdownText As String) As String
    On Error GoTo ErrorHandler
    
    Dim cleanedJson As String
    Dim startPos As Long, endPos As Long
    
    ' Log the input for debugging
    LogToFile "CleanJsonFromMarkdown: Input length: " & Len(markdownText)
    LogToFile "CleanJsonFromMarkdown: First 100 chars: " & Left(markdownText, 100)
    
    ' Check for JSON block in triple backticks (```json ... ```)
    startPos = InStr(1, markdownText, "```json")
    If startPos > 0 Then
        startPos = startPos + 7 ' Length of "```json"
        endPos = InStr(startPos, markdownText, "```")
        If endPos > startPos Then
            cleanedJson = Mid(markdownText, startPos, endPos - startPos)
            LogToFile "CleanJsonFromMarkdown: Found JSON in triple backticks"
            GoTo FinalizeResult
        End If
    End If
    
    ' Check for JSON block in triple backticks without json tag (``` ... ```)
    startPos = InStr(1, markdownText, "```" & vbCrLf & "{")
    If startPos > 0 Then
        startPos = startPos + 5 ' Length of "```" + vbCrLf
        endPos = InStr(startPos, markdownText, "```")
        If endPos > startPos Then
            cleanedJson = Mid(markdownText, startPos, endPos - startPos)
            LogToFile "CleanJsonFromMarkdown: Found JSON in triple backticks (no json tag)"
            GoTo FinalizeResult
        End If
    End If
    
    ' Check for plain JSON object (starts with { and ends with })
    startPos = InStr(1, markdownText, "{")
    If startPos > 0 Then
        endPos = InStrRev(markdownText, "}")
        If endPos > startPos Then
            cleanedJson = Mid(markdownText, startPos, endPos - startPos + 1)
            LogToFile "CleanJsonFromMarkdown: Found plain JSON object"
            GoTo FinalizeResult
        End If
    End If
    
    ' If we couldn't find JSON, return the original text
    cleanedJson = markdownText
    LogToFile "CleanJsonFromMarkdown: No JSON pattern found, returning original text"
    
FinalizeResult:
    ' Just trim whitespace - let JsonConverter handle the rest
    cleanedJson = Trim(cleanedJson)
    
    ' Remove any leading text before the JSON (like "Based on the provided OCR text...")
    Dim jsonStart As Long
    jsonStart = InStr(1, cleanedJson, "{")
    If jsonStart > 1 Then
        cleanedJson = Mid(cleanedJson, jsonStart)
        LogToFile "CleanJsonFromMarkdown: Removed " & (jsonStart - 1) & " chars of leading text"
    End If
    
    ' Replace backtick-delimited strings with proper JSON double quotes
    ' This handles cases where LLMs use `string` instead of "string"
    cleanedJson = ConvertBackticksToQuotes(cleanedJson)
    
    ' Fix triple-escaped quotes that some models produce
    ' Convert \\\" to just " (this is \" in the JSON which becomes " when parsed)
    cleanedJson = Replace(cleanedJson, "\\\""", "\""")
    
    ' Log the output for debugging
    LogToFile "CleanJsonFromMarkdown: Output length: " & Len(cleanedJson)
    LogToFile "CleanJsonFromMarkdown: First 100 chars of output: " & Left(cleanedJson, 100)
    
    CleanJsonFromMarkdown = cleanedJson
    Exit Function
    
ErrorHandler:
    LogToFile "CleanJsonFromMarkdown: ERROR - " & Err.Description
    CleanJsonFromMarkdown = markdownText ' Return original on error
End Function

' Universal JSON sanitization function - fixes common issues across all providers
Public Function SanitizeJsonValues(ByVal jsonText As String) As String
    On Error GoTo ErrorHandler
    
    Dim sanitizedJson As String
    sanitizedJson = jsonText
    
    LogToFile "SanitizeJsonValues: Starting sanitization process"
    
    ' Define patterns for unquoted values that need to be quoted
    Dim unquotedPatterns As Variant
    Dim quotedReplacements As Variant
    Dim i As Integer
    
    ' Common unquoted values that should be strings
    unquotedPatterns = Array( _
        ": Not Found,", ": Not Found}", _
        ": N/A,", ": N/A}", _
        ": null,", ": null}", _
        ": undefined,", ": undefined}", _
        ": None,", ": None}", _
        ": ,", ": }" _
    )
    
    quotedReplacements = Array( _
        ": ""Not Found"",", ": ""Not Found""}", _
        ": ""N/A"",", ": ""N/A""}", _
        ": ""null"",", ": ""null""}", _
        ": ""undefined"",", ": ""undefined""}", _
        ": ""None"",", ": ""None""}", _
        ": """",", ": """"}" _
    )
    
    ' Apply pattern-based replacements
    For i = LBound(unquotedPatterns) To UBound(unquotedPatterns)
        If InStr(1, sanitizedJson, unquotedPatterns(i)) > 0 Then
            sanitizedJson = Replace(sanitizedJson, unquotedPatterns(i), quotedReplacements(i))
            LogToFile "SanitizeJsonValues: Fixed pattern: " & unquotedPatterns(i)
        End If
    Next i
    
    ' Handle complex nested structures with unquoted values
    ' Fix array elements that are unquoted
    sanitizedJson = FixUnquotedArrayElements(sanitizedJson)
    
    ' Fix trailing commas which can cause parsing issues
    sanitizedJson = RemoveTrailingCommas(sanitizedJson)
    
    ' Validate basic JSON structure
    If ValidateBasicJsonStructure(sanitizedJson) Then
        LogToFile "SanitizeJsonValues: JSON structure validation passed"
    Else
        LogToFile "SanitizeJsonValues: WARNING - JSON structure validation failed, but continuing"
    End If
    
    SanitizeJsonValues = sanitizedJson
    LogToFile "SanitizeJsonValues: Sanitization completed"
    Exit Function
    
ErrorHandler:
    LogToFile "SanitizeJsonValues: ERROR - " & Err.Description
    SanitizeJsonValues = jsonText ' Return original on error
End Function

' Fix unquoted array elements
Private Function FixUnquotedArrayElements(ByVal jsonText As String) As String
    On Error Resume Next
    
    Dim Result As String
    Result = jsonText
    
    ' Common patterns for unquoted array elements
    Result = Replace(Result, "[Not Found]", "[""Not Found""]")
    Result = Replace(Result, "[N/A]", "[""N/A""]")
    Result = Replace(Result, "[null]", "[""null""]")
    Result = Replace(Result, "[undefined]", "[""undefined""]")
    Result = Replace(Result, "[None]", "[""None""]")
    
    ' Fix comma-separated unquoted values in arrays
    Result = Replace(Result, "Not Found,", """Not Found"",")
    Result = Replace(Result, "N/A,", """N/A"",")
    Result = Replace(Result, "null,", """null"",")
    Result = Replace(Result, "undefined,", """undefined"",")
    Result = Replace(Result, "None,", """None"",")
    
    FixUnquotedArrayElements = Result
End Function

' Remove trailing commas that can cause JSON parsing issues
Private Function RemoveTrailingCommas(ByVal jsonText As String) As String
    On Error Resume Next
    
    Dim Result As String
    Result = jsonText
    
    ' Remove trailing commas before closing braces and brackets
    Result = Replace(Result, ",}", "}")
    Result = Replace(Result, ",]", "]")
    
    ' Handle multi-line cases with whitespace
    Result = Replace(Result, "," & vbCrLf & "}", vbCrLf & "}")
    Result = Replace(Result, "," & vbLf & "}", vbLf & "}")
    Result = Replace(Result, "," & Chr(13) & "}", Chr(13) & "}")
    Result = Replace(Result, "," & Chr(10) & "}", Chr(10) & "}")
    
    Result = Replace(Result, "," & vbCrLf & "]", vbCrLf & "]")
    Result = Replace(Result, "," & vbLf & "]", vbLf & "]")
    Result = Replace(Result, "," & Chr(13) & "]", Chr(13) & "]")
    Result = Replace(Result, "," & Chr(10) & "]", Chr(10) & "]")
    
    RemoveTrailingCommas = Result
End Function

' Basic JSON structure validation
Private Function ValidateBasicJsonStructure(ByVal jsonText As String) As Boolean
    On Error Resume Next
    
    Dim openBraces As Integer, closeBraces As Integer
    Dim openBrackets As Integer, closeBrackets As Integer
    Dim i As Integer
    Dim char As String
    
    openBraces = 0
    closeBraces = 0
    openBrackets = 0
    closeBrackets = 0
    
    For i = 1 To Len(jsonText)
        char = Mid(jsonText, i, 1)
        Select Case char
            Case "{"
                openBraces = openBraces + 1
            Case "}"
                closeBraces = closeBraces + 1
            Case "["
                openBrackets = openBrackets + 1
            Case "]"
                closeBrackets = closeBrackets + 1
        End Select
    Next i
    
    ' Basic check: braces and brackets should match
    ValidateBasicJsonStructure = (openBraces = closeBraces) And (openBrackets = closeBrackets)
End Function

' Escape JSON string
Public Function EscapeJsonString(ByVal inputString As String) As String
    EscapeJsonString = inputString
    EscapeJsonString = Replace(EscapeJsonString, "\", "\\")
    EscapeJsonString = Replace(EscapeJsonString, """", "\""")
    EscapeJsonString = Replace(EscapeJsonString, Chr(10), "\n")
    EscapeJsonString = Replace(EscapeJsonString, Chr(13), "\r")
    EscapeJsonString = Replace(EscapeJsonString, Chr(9), "\t")
End Function

' ================================
' TIMING AND SYNCHRONIZATION
' ================================

' Wait function
Public Sub WaitSeconds(ByVal seconds As Double)
    Dim endTime As Double
    endTime = Timer + seconds
    Do While Timer < endTime
        DoEvents
    Loop
End Sub

' ================================
' PATH AND STRING UTILITIES
' ================================

' Helper function to escape paths for shell commands
Public Function EscapePathForShell(ByVal path As String) As String
    ' Simple quote for paths with spaces, more complex escaping might be needed for other special chars
    If InStr(1, path, " ") > 0 Then
        EscapePathForShell = """" & path & """"
    Else
        EscapePathForShell = path
    End If
End Function

' Extract filename from full path
Public Function ExtractFileName(ByVal fullPath As String) As String
    Dim pos As Integer
    pos = InStrRev(fullPath, "\")
    If pos > 0 Then
        ExtractFileName = Mid(fullPath, pos + 1)
    Else
        ExtractFileName = fullPath
    End If
End Function

' Get full path to log file from configuration
Public Function GetFullLogPath() As String
    On Error Resume Next
    
    Dim configLogPath As String
    configLogPath = OCRConfig.GetLogFilePath()
    
    ' Handle relative path
    If InStr(configLogPath, "/") > 0 Then
        configLogPath = Replace(configLogPath, "/", "\")
    End If
    
    ' Check if path is absolute or relative
    If InStr(configLogPath, ":") > 0 Or Left(configLogPath, 2) = "\\" Then
        ' Absolute path
        GetFullLogPath = configLogPath
    Else
        ' Relative path
        GetFullLogPath = ThisWorkbook.path & "\" & configLogPath
    End If
    
    On Error GoTo 0
End Function

' ================================
' ERROR HANDLING UTILITIES
' ================================

' Parse OpenRouter error response
Public Sub ParseOpenRouterError(ByVal errorResponse As String)
    On Error Resume Next
    Dim errorObj As Object
    Set errorObj = JsonConverter.ParseJson(errorResponse)
    If Not errorObj Is Nothing And errorObj.Exists("error") Then
        If errorObj("error").Exists("message") Then
            MsgBox "OpenRouter Error: " & errorObj("error")("message"), vbCritical
        End If
    End If
End Sub

' ================================
' GHOSTSCRIPT API TESTING
' ================================

' Test Ghostscript API functionality
Public Sub TestGhostscriptAPI()
    Dim gsAPI As clsGhostscriptAPI
    Set gsAPI = GetGhostscriptAPI()
    gsAPI.TestAPI
End Sub
