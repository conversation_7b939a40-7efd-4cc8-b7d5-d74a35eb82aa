Attribute VB_Name = "UserFromSettings"
Option Explicit

' Cache local provider availability
Private g_OllamaRunning As Boolean
Private g_LMStudioRunning As Boolean

' --- Public Methods to be called from FrmSettings ---

Public Sub SetProviderAvailability(ollamaRunning As Boolean, lmStudioRunning As Boolean)
    ' Cache the provider availability for use in PopulateProviderList
    g_OllamaRunning = ollamaRunning
    g_LMStudioRunning = lmStudioRunning
    OCRUtils.LogToFile "UserFormSettings: SetProviderAvailability - Ollama: " & ollamaRunning & ", LMStudio: " & lmStudioRunning
End Sub

Public Sub InitializeSettingsForm(frm As frmSettings)
    On Error GoTo ErrorHandler

    ' Speed up load: disable logging
    OCRUtils.SetLogging False

    ' Show progress to user
    ProgressHelpers.ShowProgress "Checking provider availability..."
    RefreshLocalModels frm

    ProgressHelpers.ShowProgress "Loading OCR providers..."
    PopulateProviderList frm.lstOCRProvider, False ' False for OCR providers

    ProgressHelpers.ShowProgress "Loading Extraction providers..."
    PopulateProviderList frm.lstExtractionProvider, True ' True for extraction providers

    ProgressHelpers.ShowProgress "Applying saved settings..."
    LoadAndApplySettings frm

    ProgressHelpers.ShowProgress "Loading OCR models..."
    UpdateModelListForOCR frm

    ProgressHelpers.ShowProgress "Loading Extraction models..."
    UpdateModelListForExtraction frm

    ProgressHelpers.ShowProgress "Initializing log configuration..."
    InitializeLogConfigurationControls frm

    ' Set multipage to first page (index 0)
    On Error Resume Next
    If Not frm.MultiPage1 Is Nothing Then
        frm.MultiPage1.Value = 0
        OCRUtils.LogToFile "UserFormSettings: Set MultiPage to page 0"
    End If
    On Error GoTo ErrorHandler
    
    ' Done initializing
    ProgressHelpers.HideProgress
    OCRUtils.SetLogging True
    Exit Sub

ErrorHandler:
    ' Ensure progress dialog is closed and logging is restored
    ProgressHelpers.HideProgress
    OCRUtils.SetLogging True
    OCRUtils.LogToFile "UserFormSettings: ERROR in InitializeSettingsForm: " & Err.Description
    MsgBox "Error initializing settings form: " & Err.Description, vbCritical
End Sub

Public Sub SaveSettingsFromForm(frm As frmSettings)
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "UserFormSettings: Saving settings from FrmSettings using new workflow structure..."
    
    ' Save OCR Provider (but NOT primary_ocr_source - that's controlled by the ribbon toggle)
    Dim primaryOCRProviderID As String
    primaryOCRProviderID = ""
    If frm.lstOCRProvider.ListIndex <> -1 Then primaryOCRProviderID = frm.lstOCRProvider.Value
    
    ' Get the current primary_ocr_source (don't change it)
    Dim currentPrimarySource As String
    currentPrimarySource = OCRConfig.GetWorkflowSetting("primary_ocr_source", "VLM")
    
    ' Save ocr_provider for VLM mode (this preserves user's VLM preference)
    ' The ribbon toggle button controls whether we use VLM or Docling
    OCRConfig.SetWorkflowSetting "ocr_provider", primaryOCRProviderID
    
    ' Save OCR Model
    If frm.lstOCRModel.ListIndex <> -1 And frm.lstOCRModel.Value <> "N/A - No models" And frm.lstOCRModel.Value <> "No provider selected" Then
        OCRConfig.SetWorkflowSetting "ocr_model", frm.lstOCRModel.Value
    Else
        OCRConfig.SetWorkflowSetting "ocr_model", "" ' No model selected
    End If
    
    ' Save Extraction Settings (extraction is now controlled by ribbon buttons)
    OCRConfig.SetWorkflowSetting "extraction.provider", frm.lstExtractionProvider.Value
    
    If frm.lstExtractionModel.ListIndex <> -1 Then
        OCRConfig.SetWorkflowSetting "extraction.model", frm.lstExtractionModel.Value
    Else
        OCRConfig.SetWorkflowSetting "extraction.model", "" ' No model selected
    End If
    
    ' Save Field Selection in new nested structure
    OCRConfig.SetWorkflowSetting "extraction.fields.From", frm.chkFrom.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.To", frm.chkTo.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.LetterRef", frm.chkLetterReference.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.LetterDate", frm.chkLetterDate.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.Subject", frm.chkSubject.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.References", frm.chkReferences.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.Body", frm.chkBody.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.Summary", frm.chkSummary.Value
    OCRConfig.SetWorkflowSetting "extraction.fields.Tags", frm.chkTopicsTags.Value
    
    ' Save OpenRouter PDF settings if available
    SaveOpenRouterPDFSettings frm
    
    ' Save log configuration from page 2
    SaveLogConfigurationFromForm frm
    
    OCRConfig.SaveConfigurationToJSON
    OCRUtils.LogToFile "UserFormSettings: Settings saved successfully using new workflow structure."
    
    ' Refresh ribbon to sync checkbox states
    If Not RibbonCallbacks.gRibbonUI Is Nothing Then
        RibbonCallbacks.RefreshRibbon
        OCRUtils.LogToFile "UserFormSettings: Ribbon refreshed after saving settings"
    End If
    
    MsgBox "Settings saved successfully!", vbInformation
    Unload frm
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: ERROR in SaveSettingsFromForm: " & Err.Description
    MsgBox "Error saving settings: " & Err.Description, vbCritical
End Sub

Public Sub RefreshFormControls(frm As frmSettings)
    ' This sub can be expanded to handle dynamic updates, e.g., refreshing local models
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "UserFormSettings: Refreshing form controls..."
    
    ' Re-populate provider lists (in case new ones are available, e.g. Ollama/LMStudio)
    Dim currentOCRProvider As String
    Dim currentExtractionProvider As String
    
    If frm.lstOCRProvider.ListIndex <> -1 Then currentOCRProvider = frm.lstOCRProvider.Value
    If frm.lstExtractionProvider.ListIndex <> -1 Then currentExtractionProvider = frm.lstExtractionProvider.Value
    
    ' Populate provider lists - False for OCR list, True for Extraction list
    PopulateProviderList frm.lstOCRProvider, False
    PopulateProviderList frm.lstExtractionProvider, True
      ' Try to restore previously selected providers
    If currentOCRProvider <> "" Then
        SetListboxByValue frm.lstOCRProvider, currentOCRProvider
    Else
        ' Set to configured default if no previous selection
        SetListboxByValue frm.lstOCRProvider, OCRConfig.GetWorkflowSetting("ocr_provider", "")
    End If
    
    If currentExtractionProvider <> "" Then
        SetListboxByValue frm.lstExtractionProvider, currentExtractionProvider
    Else
        ' Set to configured default if no previous selection
        SetListboxByValue frm.lstExtractionProvider, OCRConfig.GetWorkflowSetting("extraction.provider", "")
    End If
    
    ' Update model lists based on selected providers
    UpdateModelListForOCR frm
    UpdateModelListForExtraction frm
    
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error refreshing form controls - " & Err.Description
End Sub

' Change visibility to Public for external calls
Public Sub RefreshLocalModels(frm As frmSettings)
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "UserFormSettings: Refreshing dynamic provider models..."

    ' Cache health checks once
    g_OllamaRunning = Prov_Ollama.isOllamaRunning()
    g_LMStudioRunning = Prov_LMStudio.isLMStudioRunning()
    OCRUtils.LogToFile "UserFormSettings: Cached provider status - Ollama: " & g_OllamaRunning & ", LM Studio: " & g_LMStudioRunning

    Dim startTime As Double
    Dim refreshTime As Double
    Dim successMsg As String
    Dim errorMsg As String
    Dim refreshedProviders As Collection
    Set refreshedProviders = New Collection
    Dim failedProviders As Collection
    Set failedProviders = New Collection
    
    startTime = Timer
    Application.ScreenUpdating = False
    Application.StatusBar = "Refreshing provider models..."
    
    ' Get all provider IDs from the config
    Dim allProviderIDs As Variant ' Expects an array of strings
    Dim providerDetails As Object
    Dim providerID As Variant
    Dim providerType As String
    Dim providerModule As String
    Dim isAvailable As Boolean
    
    ' We need a way to get all configured provider IDs.
    ' For now, let's hardcode the ones we know support dynamic refresh.
    ' Ideally, OCRConfig would provide a GetConfiguredProviderIDs() function.
    Dim providersToRefresh As Variant
    providersToRefresh = Array(OCRConfig.PID_OLLAMA, OCRConfig.PID_LMSTUDIO, OCRConfig.PID_OPENROUTER, OCRConfig.PID_OPENROUTER_OCR)

    For Each providerID In providersToRefresh
        Set providerDetails = OCRConfig.GetProviderDetails(CStr(providerID))
        If Not providerDetails Is Nothing Then
            providerModule = ""
            If providerDetails.Exists("module") Then providerModule = providerDetails("module")
            
            providerType = ""
            If providerDetails.Exists("type") Then providerType = providerDetails("type")
            
            ' Dynamically get actual type if module exists
            If providerModule <> "" Then
                Dim dynamicType As String
                On Error Resume Next
                dynamicType = Application.Run(providerModule & ".GetProviderType")
                If Err.Number = 0 And dynamicType <> "" Then providerType = dynamicType
                Err.Clear
                On Error GoTo ErrorHandler
            End If

            If LCase(providerType) = "local" Or LCase(providerType) = "cloud_api" Then
                Application.StatusBar = "Refreshing " & OCRConfig.GetProviderName(CStr(providerID)) & " models..."
                OCRUtils.LogToFile "UserFormSettings: Attempting to refresh models for " & providerID
                
                On Error Resume Next ' To catch errors from RefreshProviderModels itself
                OCRConfig.RefreshProviderModels CStr(providerID)
                If Err.Number = 0 Then
                    refreshedProviders.Add OCRConfig.GetProviderName(CStr(providerID))
                Else
                    failedProviders.Add OCRConfig.GetProviderName(CStr(providerID)) & " (Error: " & Err.Description & ")"
                    OCRUtils.LogToFile "UserFormSettings: Error refreshing " & providerID & ": " & Err.Description
                End If
                Err.Clear
                On Error GoTo ErrorHandler
            End If
        End If
    Next providerID
    
    refreshTime = Timer - startTime

    ' Remove user-facing message boxes - use only logging
    ' If refreshedProviders.Count = 0 And failedProviders.Count = 0 Then
    '     MsgBox "No dynamic providers found or configured for refresh.", vbInformation, "No Providers to Refresh"
    ' Else
    '     ... build successMsg ...
    '     MsgBox successMsg, vbInformation, "Provider Models Refreshed"
    ' End If

    ' Do not auto-repopulate lists here; leave UI updates to caller
    ' PopulateProviderList frm.lstOCRProvider, False
    ' PopulateProviderList frm.lstExtractionProvider, True
    ' UpdateModelListForOCR frm
    ' UpdateModelListForExtraction frm

    Application.StatusBar = False
    Application.ScreenUpdating = True
    OCRUtils.LogToFile "UserFormSettings: Local model refresh completed in " & Format(refreshTime, "0.00") & " seconds."
    Exit Sub

ErrorHandler:
    Application.StatusBar = False
    Application.ScreenUpdating = True
    
    errorMsg = "Error refreshing local models:" & vbCrLf & vbCrLf & _
               Err.Description & vbCrLf & vbCrLf & _
               "Please check that local providers are running and accessible, then try again."
    
    MsgBox errorMsg, vbCritical, "Refresh Failed"
    OCRUtils.LogToFile "UserFormSettings: Local model refresh failed: " & Err.Description
End Sub

' --- Helper Procedures ---

Public Sub PopulateProviderList(lb As MSForms.ListBox, isExtractionList As Boolean)
    lb.Clear
    OCRUtils.LogToFile "UserFormSettings: PopulateProviderList - isExtractionList: " & isExtractionList

    ' Both lists will now show the same set of general VLM providers
    ' Only show providers that are actually running/available
    
    If g_OllamaRunning Then
        lb.AddItem OCRConfig.PID_OLLAMA
        OCRUtils.LogToFile "UserFormSettings: Added Ollama to list (running)."
    End If
    
    lb.AddItem OCRConfig.PID_OPENROUTER ' Standard OpenRouter VLM
    OCRUtils.LogToFile "UserFormSettings: Added OpenRouter (PID_OPENROUTER) to list."
            
    If g_LMStudioRunning Then
        lb.AddItem OCRConfig.PID_LMSTUDIO
        OCRUtils.LogToFile "UserFormSettings: Added LM Studio to list (running)."
    End If
    
    ' Docling is now controlled via the Direct OCR toggle button on the ribbon
    ' So we don't add it to the provider list anymore
    
    If Not isExtractionList Then
        OCRUtils.LogToFile "UserFormSettings: Populated OCR Provider list with general VLMs."
    Else
        OCRUtils.LogToFile "UserFormSettings: Populated Extraction Provider list with general VLMs."
    End If
    
    If lb.ListCount > 0 Then
        lb.ListIndex = 0 ' Select first item by default
    Else
        lb.AddItem "No Providers Available"
        lb.ListIndex = 0
        OCRUtils.LogToFile "UserFormSettings: No providers available for list (isExtractionList: " & isExtractionList & ")"
    End If
End Sub

Private Sub PopulateModelList(lb As MSForms.ListBox, providerID As String)
    lb.Clear
    
    ' Validate provider ID
    If providerID = "" Then
        OCRUtils.LogToFile "UserFormSettings: PopulateModelList - Empty provider ID provided"
        lb.AddItem "No provider selected"
        lb.ListIndex = 0
        Exit Sub
    End If
    
    ' Check if provider exists in config (and handle missing providers gracefully)
    On Error Resume Next
    Dim providerExists As Boolean
    providerExists = Not (OCRConfig.GetProviderDetails(providerID) Is Nothing)
    On Error GoTo 0
    
    If Not providerExists And providerID <> OCRConfig.PID_DOCLING Then
        OCRUtils.LogToFile "UserFormSettings: PopulateModelList - Provider not found in config: " & providerID
        lb.AddItem "Provider not configured"
        lb.ListIndex = 0
        Exit Sub
    End If
    
    OCRUtils.LogToFile "UserFormSettings: PopulateModelList - Getting models for provider: " & providerID
    
    Dim models As Variant
    
    ' Special handling for different provider types
    If providerID = OCRConfig.PID_OPENROUTER_OCR Then
        models = OCRConfig.GetOpenRouterFileCompatibleModels()
    ElseIf providerID = OCRConfig.PID_DOCLING Then
        ' Docling doesn't have models - it's just an OCR service
        models = Array("N/A - Docling has no models")
    Else
        models = OCRConfig.GetProviderModels(providerID)
    End If
    
    If IsArray(models) Then
        Dim i As Long
        Dim modelCount As Long
        modelCount = 0
        
        For i = LBound(models) To UBound(models)
            If models(i) <> "" And models(i) <> "- No models running -" And models(i) <> "- No models loaded -" Then
                lb.AddItem models(i)
                modelCount = modelCount + 1
            ElseIf models(i) = "- No models running -" Or models(i) = "- No models loaded -" Then
                 lb.AddItem models(i) ' Show placeholder if it's the only thing
                 modelCount = modelCount + 1
            End If
        Next i
        
        OCRUtils.LogToFile "UserFormSettings: PopulateModelList - Added " & modelCount & " models for provider: " & providerID
    Else
        OCRUtils.LogToFile "UserFormSettings: PopulateModelList - No models array returned for provider: " & providerID
    End If
    
    If lb.ListCount > 0 Then
        lb.ListIndex = 0 ' Select first available model
    Else
        lb.AddItem "N/A - No models" ' Placeholder if list is empty
        lb.ListIndex = 0
        OCRUtils.LogToFile "UserFormSettings: PopulateModelList - No models available for provider: " & providerID
    End If
End Sub

Public Sub LoadAndApplySettings(frm As frmSettings)
    ' Load settings using new workflow structure
    OCRUtils.LogToFile "UserFormSettings: Loading settings using new workflow structure..."
    
    ' Load Primary OCR Source and Provider
    Dim primaryOCRSource As String
    Dim ocrProvider As String
    primaryOCRSource = OCRConfig.GetWorkflowSetting("primary_ocr_source", "VLM")
    ocrProvider = OCRConfig.GetWorkflowSetting("ocr_provider", "")
    
    ' Set the OCR provider in the form
    SetListboxByValue frm.lstOCRProvider, ocrProvider
    
    ' Log the state after setting OCR provider
    OCRUtils.LogToFile "UserFormSettings: After SetListboxByValue for OCR - ListIndex: " & frm.lstOCRProvider.ListIndex
    If frm.lstOCRProvider.ListIndex <> -1 Then
        OCRUtils.LogToFile "UserFormSettings: OCR Provider selected: " & frm.lstOCRProvider.Value
    End If
    
    ' Load Extraction Settings
    ' chkExtractLogFields removed - extraction is now controlled by ribbon buttons
    
    ' Load Extraction Provider
    Dim extractionProvider As String
    extractionProvider = OCRConfig.GetWorkflowSetting("extraction.provider", "")
    SetListboxByValue frm.lstExtractionProvider, extractionProvider
    
    ' Log the state after setting Extraction provider
    OCRUtils.LogToFile "UserFormSettings: After SetListboxByValue for Extraction - ListIndex: " & frm.lstExtractionProvider.ListIndex
    If frm.lstExtractionProvider.ListIndex <> -1 Then
        OCRUtils.LogToFile "UserFormSettings: Extraction Provider selected: " & frm.lstExtractionProvider.Value
    End If
    
    ' Load Field Selection CheckBoxes from new nested structure
    frm.chkFrom.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.From", True))
    frm.chkTo.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.To", True))
    frm.chkLetterReference.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.LetterRef", True))
    frm.chkLetterDate.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.LetterDate", True))
    frm.chkSubject.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.Subject", True))
    frm.chkReferences.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.References", True))
    frm.chkBody.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.Body", True))
    frm.chkSummary.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.Summary", True))
    frm.chkTopicsTags.Value = CBool(OCRConfig.GetWorkflowSetting("extraction.fields.Tags", True))
    
    ' Always enable extraction section (extraction controlled by ribbon buttons)
    ToggleExtractionSection frm, True
    
    ' Initialize OpenRouter PDF settings if available
    InitializeOpenRouterPDFSettings frm
    
    ' Update model lists sequentially to avoid conflicts
    ' First update OCR models, then extraction models
    OCRUtils.LogToFile "UserFormSettings: LoadAndApplySettings - Updating OCR model list..."
    UpdateModelListForOCR frm
    
    ' Allow VBA to process the first update before proceeding
    DoEvents
    
    OCRUtils.LogToFile "UserFormSettings: LoadAndApplySettings - Updating Extraction model list..."
    UpdateModelListForExtraction frm
End Sub

Public Sub UpdateModelListForOCR(frm As frmSettings)
    ' Get the selected provider ID
    Dim providerID As String
    
    ' Make sure we have a valid selection
    If frm.lstOCRProvider.ListIndex <> -1 Then
        providerID = frm.lstOCRProvider.Value
    Else
        ' No provider selected, clear the model list
        OCRUtils.LogToFile "UserFormSettings: UpdateModelListForOCR - No provider selected"
        frm.lstOCRModel.Clear
        frm.lstOCRModel.AddItem "Select a provider first"
        frm.lstOCRModel.ListIndex = 0
        Exit Sub
    End If
    
    ' Validate provider ID before proceeding
    If providerID = "" Or providerID = "No Providers Available" Then
        OCRUtils.LogToFile "UserFormSettings: UpdateModelListForOCR - Invalid provider ID: " & providerID
        frm.lstOCRModel.Clear
        frm.lstOCRModel.AddItem "Select a valid provider"
        frm.lstOCRModel.ListIndex = 0
        Exit Sub
    End If
    
    OCRUtils.LogToFile "UserFormSettings: UpdateModelListForOCR - Updating models for provider: " & providerID
    
    ' Check if provider is running (for local providers)
    Dim providerRunning As Boolean
    providerRunning = True ' Assume cloud providers are always available
    
    If providerID = OCRConfig.PID_OLLAMA Then
        providerRunning = g_OllamaRunning
    ElseIf providerID = OCRConfig.PID_LMSTUDIO Then
        providerRunning = g_LMStudioRunning
    End If
    
    If Not providerRunning Then
        OCRUtils.LogToFile "UserFormSettings: UpdateModelListForOCR - Provider " & providerID & " is not running"
        frm.lstOCRModel.Clear
        frm.lstOCRModel.AddItem "Provider not running"
        frm.lstOCRModel.ListIndex = 0
        Exit Sub
    End If
    
    ' For dynamic providers, refresh their models before populating the list
    If providerID = OCRConfig.PID_OLLAMA Or providerID = OCRConfig.PID_LMSTUDIO Then
        OCRUtils.LogToFile "UserFormSettings: UpdateModelListForOCR - Refreshing models for dynamic provider: " & providerID
        On Error Resume Next
        OCRConfig.RefreshProviderModels providerID
        If Err.Number <> 0 Then
            OCRUtils.LogToFile "UserFormSettings: UpdateModelListForOCR - Error refreshing models for " & providerID & ": " & Err.Description
            Err.Clear
        Else
            OCRUtils.LogToFile "UserFormSettings: UpdateModelListForOCR - Successfully refreshed models for " & providerID
        End If
        On Error GoTo 0
    End If
    
    ' Populate the model list for the selected provider
    PopulateModelList frm.lstOCRModel, providerID
    
    ' Update the selected model to match the current config for the primary OCR role
    Dim configuredPrimaryModel As String
    Dim currentOCRProvider As String
    currentOCRProvider = OCRConfig.GetWorkflowSetting("ocr_provider", "")
    
    ' Check if this provider is the current OCR provider
    If providerID = currentOCRProvider Then
        configuredPrimaryModel = OCRConfig.GetWorkflowSetting("ocr_model", "")
    Else
        ' If the selected provider in the list is not the currently configured primary one,
        ' don't pre-select any model - let the user choose
        configuredPrimaryModel = ""
    End If
    
    SetListboxByValue frm.lstOCRModel, configuredPrimaryModel
    
    ' If after SetListboxByValue, nothing is selected and list has items, select first available model (not placeholder)
    If frm.lstOCRModel.ListIndex = -1 And frm.lstOCRModel.ListCount > 0 Then
        ' Only auto-select if it's not a placeholder message
        Dim firstItem As String
        firstItem = frm.lstOCRModel.List(0)
        If Not (InStr(firstItem, "- ") = 1 And InStr(firstItem, " -") > 0) Then
            frm.lstOCRModel.ListIndex = 0
        End If
    End If
End Sub

Public Sub UpdateModelListForExtraction(frm As frmSettings)
    Dim providerID As String
    
    OCRUtils.LogToFile "UserFormSettings: UpdateModelListForExtraction - ListIndex: " & frm.lstExtractionProvider.ListIndex
    
    If frm.lstExtractionProvider.ListIndex <> -1 Then
        providerID = frm.lstExtractionProvider.Value
        OCRUtils.LogToFile "UserFormSettings: UpdateModelListForExtraction - Provider selected: " & providerID
        PopulateModelList frm.lstExtractionModel, providerID
        ' Try to select the currently configured extraction model
        Dim configuredExtractionModel As String
        Dim currentExtractionProvider As String
        currentExtractionProvider = OCRConfig.GetWorkflowSetting("extraction.provider", "")
          If providerID = currentExtractionProvider Then
            configuredExtractionModel = OCRConfig.GetWorkflowSetting("extraction.model", "")
        Else
            ' Don't pre-select any model for non-current providers - let user choose
            configuredExtractionModel = ""
        End If
        SetListboxByValue frm.lstExtractionModel, configuredExtractionModel
        
        ' If after SetListboxByValue, nothing is selected and list has items, select first available model (not placeholder)
        If frm.lstExtractionModel.ListIndex = -1 And frm.lstExtractionModel.ListCount > 0 Then
            Dim firstItem As String
            firstItem = frm.lstExtractionModel.List(0)
            If Not (InStr(firstItem, "- ") = 1 And InStr(firstItem, " -") > 0) Then
                frm.lstExtractionModel.ListIndex = 0
            End If
        End If
    Else
        frm.lstExtractionModel.Clear
        frm.lstExtractionModel.AddItem "N/A - Select Provider"
        frm.lstExtractionModel.ListIndex = 0
    End If
End Sub

Public Sub ToggleExtractionSection(frm As frmSettings, enabled As Boolean)
    frm.fraExtractionModel.enabled = enabled ' Frame for extraction provider/model
    frm.lblExtractionProvider.enabled = enabled
    frm.lstExtractionProvider.enabled = enabled
    frm.lblExtractionModel.enabled = enabled
    frm.lstExtractionModel.enabled = enabled
    
    frm.fraExtractionFields.enabled = enabled ' Frame for field checkboxes
    frm.chkFrom.enabled = enabled
    frm.chkTo.enabled = enabled
    frm.chkLetterReference.enabled = enabled
    frm.chkLetterDate.enabled = enabled
    frm.chkSubject.enabled = enabled
    frm.chkReferences.enabled = enabled
    frm.chkBody.enabled = enabled
    frm.chkSummary.enabled = enabled
    frm.chkTopicsTags.enabled = enabled
End Sub


Public Sub SetListboxByValue(lb As MSForms.ListBox, valueToSelect As String)
    Dim i As Long
    
    ' First check if the list has any items
    If lb.ListCount = 0 Then
        OCRUtils.LogToFile "UserFormSettings: SetListboxByValue - List is empty, cannot select value: " & valueToSelect
        Exit Sub
    End If
    
    ' If valueToSelect is empty, select first item
    If valueToSelect = "" Then
        If lb.ListCount > 0 Then
            lb.ListIndex = 0
            OCRUtils.LogToFile "UserFormSettings: SetListboxByValue - Empty value provided, selected first item: " & lb.List(0)
        End If
        Exit Sub
    End If
    
    ' Try to find and select the requested value
    For i = 0 To lb.ListCount - 1
        If lb.List(i) = valueToSelect Then
            lb.ListIndex = i
            OCRUtils.LogToFile "UserFormSettings: SetListboxByValue - Selected value: " & valueToSelect & " at index: " & i
            Exit Sub
        End If
    Next i
    
    ' If value not found, and list has items, select first item
    If lb.ListCount > 0 And lb.ListIndex = -1 Then
        lb.ListIndex = 0
        OCRUtils.LogToFile "UserFormSettings: SetListboxByValue - Value not found (" & valueToSelect & "), selected first item: " & lb.List(0)
    End If
End Sub

' ================================
' OpenRouter PDF Settings Functions
' ================================

' Initialize OpenRouter PDF Settings page
Public Sub InitializeOpenRouterPDFSettings(frm As Object)
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "UserFormSettings: Initializing OpenRouter PDF Settings page"
    
    ' Check if any PDF engine controls exist
    If Not (HasControl(frm, "optMistralOCR") Or HasControl(frm, "optPDFText") Or HasControl(frm, "optNative")) Then
        OCRUtils.LogToFile "UserFormSettings: No PDF engine controls found, skipping PDF settings initialization"
        Exit Sub
    End If
    
    ' Load current PDF engine selection
    Dim currentEngine As String
    currentEngine = OCRConfig.GetOpenRouterPDFEngine()
    
    OCRUtils.LogToFile "UserFormSettings: Current PDF engine from config: " & currentEngine
    
    ' Set the appropriate option button based on current engine
    Select Case currentEngine
        Case "mistral-ocr"
            If HasControl(frm, "optMistralOCR") Then
                frm.optMistralOCR.Value = True
                OCRUtils.LogToFile "UserFormSettings: Set optMistralOCR to True"
            End If
        Case "pdf-text"
            If HasControl(frm, "optPDFText") Then
                frm.optPDFText.Value = True
                OCRUtils.LogToFile "UserFormSettings: Set optPDFText to True"
            End If
        Case "native"
            If HasControl(frm, "optNative") Then
                frm.optNative.Value = True
                OCRUtils.LogToFile "UserFormSettings: Set optNative to True"
            End If
        Case Else
            ' Default to pdf-text if none selected
            If HasControl(frm, "optPDFText") Then
                frm.optPDFText.Value = True
                OCRUtils.LogToFile "UserFormSettings: Set optPDFText to True (default)"
            End If
    End Select
    
    ' Load file-compatible models
    LoadOpenRouterPDFModels frm
    
    ' Update model visibility based on engine selection
    UpdateOpenRouterPDFModelVisibility frm
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error in InitializeOpenRouterPDFSettings: " & Err.Description
End Sub

' Load file-compatible models for OpenRouter PDF
Public Sub LoadOpenRouterPDFModels(frm As Object)
    On Error GoTo ErrorHandler
    
    If Not HasControl(frm, "lstOpenRouterPDFModel") Then Exit Sub
    
    Dim modelList As MSForms.ListBox
    Set modelList = frm.lstOpenRouterPDFModel
    modelList.Clear
    
    ' Get file-compatible models
    Dim compatibleModels As Collection
    Set compatibleModels = Prov_OpenRouter.GetFileCompatibleModels()
    
    If compatibleModels.Count > 0 Then
        Dim model As Variant
        For Each model In compatibleModels
            modelList.AddItem CStr(model)
        Next model
        
        ' Select current PDF model if set
        Dim currentModel As String
        currentModel = OCRConfig.GetOpenRouterPDFModel()
        If currentModel <> "" Then
            SetListboxByValue modelList, currentModel
        ElseIf modelList.ListCount > 0 Then
            modelList.ListIndex = 0
        End If
    Else
        modelList.AddItem "No file-compatible models available"
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error in LoadOpenRouterPDFModels: " & Err.Description
End Sub

' Update model visibility based on selected PDF engine
Public Sub UpdateOpenRouterPDFModelVisibility(frm As Object)
    On Error GoTo ErrorHandler
    
    Dim showModelList As Boolean
    showModelList = True
    
    ' Check which engine is selected
    If HasControl(frm, "optMistralOCR") And frm.optMistralOCR.Value Then
        ' mistral-ocr doesn't need model selection
        showModelList = False
    End If
    
    ' Show/hide model selection controls
    If HasControl(frm, "lblOpenRouterPDFModel") Then
        frm.lblOpenRouterPDFModel.Visible = showModelList
    End If
    If HasControl(frm, "lstOpenRouterPDFModel") Then
        frm.lstOpenRouterPDFModel.Visible = showModelList
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error in UpdateOpenRouterPDFModelVisibility: " & Err.Description
End Sub

' Save OpenRouter PDF Settings
Public Sub SaveOpenRouterPDFSettings(frm As Object)
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "UserFormSettings: Saving OpenRouter PDF Settings"
    
    ' Check if any PDF engine controls exist
    If Not (HasControl(frm, "optMistralOCR") Or HasControl(frm, "optPDFText") Or HasControl(frm, "optNative")) Then
        OCRUtils.LogToFile "UserFormSettings: No PDF engine controls found, skipping PDF settings save"
        Exit Sub
    End If
    
    ' Determine selected engine
    Dim selectedEngine As String
    If HasControl(frm, "optMistralOCR") And frm.optMistralOCR.Value Then
        selectedEngine = "mistral-ocr"
        OCRUtils.LogToFile "UserFormSettings: optMistralOCR selected"
    ElseIf HasControl(frm, "optPDFText") And frm.optPDFText.Value Then
        selectedEngine = "pdf-text"
        OCRUtils.LogToFile "UserFormSettings: optPDFText selected"
    ElseIf HasControl(frm, "optNative") And frm.optNative.Value Then
        selectedEngine = "native"
        OCRUtils.LogToFile "UserFormSettings: optNative selected"
    Else
        selectedEngine = "pdf-text" ' Default
        OCRUtils.LogToFile "UserFormSettings: No PDF engine option selected, defaulting to pdf-text"
    End If
    
    ' Save engine selection
    OCRConfig.SetOpenRouterPDFEngine selectedEngine
    OCRUtils.LogToFile "UserFormSettings: Saved PDF Engine: " & selectedEngine
    
    ' Save model selection (only if not mistral-ocr)
    If selectedEngine <> "mistral-ocr" And HasControl(frm, "lstOpenRouterPDFModel") Then
        If frm.lstOpenRouterPDFModel.ListIndex <> -1 Then
            Dim selectedModel As String
            selectedModel = frm.lstOpenRouterPDFModel.Value
            If selectedModel <> "No file-compatible models available" Then
                OCRConfig.SetOpenRouterPDFModel selectedModel
                ' Also add to file_compatible_models list if not already there
                OCRConfig.AddToOpenRouterFileCompatibleModels selectedModel
                OCRUtils.LogToFile "UserFormSettings: Saved PDF Model: " & selectedModel
            End If
        End If
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error in SaveOpenRouterPDFSettings: " & Err.Description
End Sub

' Helper function to check if a control exists on the form
Private Function HasControl(frm As Object, controlName As String) As Boolean
    On Error Resume Next
    Dim ctrl As Object
    Set ctrl = frm.Controls(controlName)
    HasControl = (Err.Number = 0)
    Err.Clear
    On Error GoTo 0
End Function

' Refresh OpenRouter PDF models
Public Sub RefreshOpenRouterPDFModels(frm As Object)
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "UserFormSettings: Refreshing OpenRouter PDF models"
    
    ' Refresh the main OpenRouterOCR provider models first
    OCRConfig.RefreshProviderModels OCRConfig.PID_OPENROUTER_OCR
    
    ' Reload the PDF models list
    LoadOpenRouterPDFModels frm
    
    MsgBox "OpenRouter PDF models refreshed successfully!", vbInformation
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error in RefreshOpenRouterPDFModels: " & Err.Description
    MsgBox "Error refreshing models: " & Err.Description, vbCritical
End Sub

'===============================================================================
' LOG CONFIGURATION SUPPORT (Page 2 of FrmSettings)
'===============================================================================

' Initialize log configuration controls on settings form page 2
Public Sub InitializeLogConfigurationControls(frm As frmSettings)
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "UserFormSettings: Initializing log configuration controls"
    
    ' Get current log field configurations
    Dim logConfig As Object
    Set logConfig = OCRConfig.GetAllLogFieldConfigurations()
    
    ' Initialize Essential Fields
    Call InitializeLogFieldGroup(frm, logConfig, "Essential", Array("From", "To", "LetterRef", "LetterDate", "Subject", "References", "Body"))
    
    ' Initialize Helper Fields  
    Call InitializeLogFieldGroup(frm, logConfig, "Helper", Array("Summary", "Tags"))
    
    ' Initialize Tracing Fields
    Call InitializeLogFieldGroup(frm, logConfig, "Tracing", Array("ResponseLetter", "ResponseDate", "ReferringLetter", "ReferringDate"))
    
    OCRUtils.LogToFile "UserFormSettings: Log configuration controls initialized successfully"
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error in InitializeLogConfigurationControls: " & Err.Description
End Sub

' Initialize a group of log fields (Essential, Helper, or Tracing)
Private Sub InitializeLogFieldGroup(frm As frmSettings, logConfig As Object, groupName As String, fieldNames As Variant)
    On Error GoTo ErrorHandler
    
    Dim i As Integer
    For i = 0 To UBound(fieldNames)
        Dim fieldName As String
        fieldName = fieldNames(i)
        
        If logConfig.Exists(fieldName) Then
            Dim fieldConfig As Object
            Set fieldConfig = logConfig(fieldName)
            
            ' Set checkbox state
            Call SetLogCheckboxValue(frm, fieldName, fieldConfig("enabled"))
            
            ' Set column RefEdit value
            Call SetLogRefEditValue(frm, fieldName, fieldConfig("column"))
            
            ' Set header name TextBox value
            Call SetLogTextBoxValue(frm, fieldName, fieldConfig("headerName"))
            
            ' Update control states based on checkbox
            Call UpdateLogControlStates(frm, fieldName, fieldConfig("enabled"))
            
            OCRUtils.LogToFile "UserFormSettings: Initialized " & groupName & " field: " & fieldName
        End If
    Next i
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error initializing " & groupName & " field group: " & Err.Description
End Sub

' Set log checkbox value by field name
Private Sub SetLogCheckboxValue(frm As frmSettings, fieldName As String, enabled As Boolean)
    On Error Resume Next
    
    Select Case fieldName
        Case "From": frm.chkLogFrom.Value = enabled
        Case "To": frm.chkLogTo.Value = enabled
        Case "LetterRef": frm.chkLogReference.Value = enabled
        Case "LetterDate": frm.chkLogDate.Value = enabled
        Case "Subject": frm.chkLogSubject.Value = enabled
        Case "References": frm.chkLogReferences.Value = enabled
        Case "Body": frm.chkLogBody.Value = enabled
        Case "Summary": frm.chkLogSummary.Value = enabled
        Case "Tags": frm.chkLLogTags.Value = enabled
        Case "ResponseLetter": frm.chkResponse.Value = enabled
        Case "ResponseDate": frm.chkResponseDate.Value = enabled
        Case "ReferringLetter": frm.chkReferring.Value = enabled
        Case "ReferringDate": frm.chkReferringDate.Value = enabled
    End Select
    
    On Error GoTo 0
End Sub

' Set log RefEdit value by field name
Private Sub SetLogRefEditValue(frm As frmSettings, fieldName As String, column As String)
    On Error Resume Next
    
    Select Case fieldName
        Case "From": frm.refFrom.Value = column & ":" & column
        Case "To": frm.refTo.Value = column & ":" & column
        Case "LetterRef": frm.refReference.Value = column & ":" & column
        Case "LetterDate": frm.refDate.Value = column & ":" & column
        Case "Subject": frm.refSubject.Value = column & ":" & column
        Case "References": frm.refReferences.Value = column & ":" & column
        Case "Body": frm.refBody.Value = column & ":" & column
        Case "Summary": frm.refSummary.Value = column & ":" & column
        Case "Tags": frm.refTags.Value = column & ":" & column
        Case "ResponseLetter": frm.refResponse.Value = column & ":" & column
        Case "ResponseDate": frm.refResponseDate.Value = column & ":" & column
        Case "ReferringLetter": frm.refReferring.Value = column & ":" & column
        Case "ReferringDate": frm.refReferringDate.Value = column & ":" & column
    End Select
    
    On Error GoTo 0
End Sub

' Set log TextBox value by field name
Private Sub SetLogTextBoxValue(frm As frmSettings, fieldName As String, headerName As String)
    On Error Resume Next
    
    Select Case fieldName
        Case "From": frm.txtFrom.Value = headerName
        Case "To": frm.txtTo.Value = headerName
        Case "LetterRef": frm.txtReference.Value = headerName
        Case "LetterDate": frm.txtDate.Value = headerName
        Case "Subject": frm.txtSubject.Value = headerName
        Case "References": frm.txtReferences.Value = headerName
        Case "Body": frm.txtBody.Value = headerName
        Case "Summary": frm.txtSummary.Value = headerName
        Case "Tags": frm.txtTags.Value = headerName
        Case "ResponseLetter": frm.txtResponse.Value = headerName
        Case "ResponseDate": frm.txtResponseDate.Value = headerName
        Case "ReferringLetter": frm.txtRefering.Value = headerName
        Case "ReferringDate": frm.txtReferingDate.Value = headerName
    End Select
    
    On Error GoTo 0
End Sub

' Update control states (enable/disable RefEdit and TextBox based on checkbox)
Private Sub UpdateLogControlStates(frm As frmSettings, fieldName As String, enabled As Boolean)
    On Error Resume Next
    
    Select Case fieldName
        Case "From"
            frm.refFrom.enabled = enabled
            frm.txtFrom.enabled = enabled
        Case "To"
            frm.refTo.enabled = enabled
            frm.txtTo.enabled = enabled
        Case "LetterRef"
            frm.refReference.enabled = enabled
            frm.txtReference.enabled = enabled
        Case "LetterDate"
            frm.refDate.enabled = enabled
            frm.txtDate.enabled = enabled
        Case "Subject"
            frm.refSubject.enabled = enabled
            frm.txtSubject.enabled = enabled
        Case "References"
            frm.refReferences.enabled = enabled
            frm.txtReferences.enabled = enabled
        Case "Body"
            frm.refBody.enabled = enabled
            frm.txtBody.enabled = enabled
        Case "Summary"
            frm.refSummary.enabled = enabled
            frm.txtSummary.enabled = enabled
        Case "Tags"
            frm.refTags.enabled = enabled
            frm.txtTags.enabled = enabled
        Case "ResponseLetter"
            frm.refResponse.enabled = enabled
            frm.txtResponse.enabled = enabled
        Case "ResponseDate"
            frm.refResponseDate.enabled = enabled
            frm.txtResponseDate.enabled = enabled
        Case "ReferringLetter"
            frm.refReferring.enabled = enabled
            frm.txtRefering.enabled = enabled
        Case "ReferringDate"
            frm.refReferringDate.enabled = enabled
            frm.txtReferingDate.enabled = enabled
    End Select
    
    On Error GoTo 0
End Sub

' Save log configuration from form controls
Public Sub SaveLogConfigurationFromForm(frm As frmSettings)
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "UserFormSettings: Saving log configuration from form"
    
    ' Save Essential Fields
    Call SaveLogFieldGroup(frm, "Essential", Array("From", "To", "LetterRef", "LetterDate", "Subject", "References", "Body"))
    
    ' Save Helper Fields
    Call SaveLogFieldGroup(frm, "Helper", Array("Summary", "Tags"))
    
    ' Save Tracing Fields  
    Call SaveLogFieldGroup(frm, "Tracing", Array("ResponseLetter", "ResponseDate", "ReferringLetter", "ReferringDate"))
    
    OCRUtils.LogToFile "UserFormSettings: Log configuration saved successfully"
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error in SaveLogConfigurationFromForm: " & Err.Description
End Sub

' Save a group of log fields from form controls
Private Sub SaveLogFieldGroup(frm As frmSettings, groupName As String, fieldNames As Variant)
    On Error GoTo ErrorHandler
    
    Dim i As Integer
    For i = 0 To UBound(fieldNames)
        Dim fieldName As String
        fieldName = fieldNames(i)
        
        ' Get values from form controls
        Dim enabled As Boolean
        Dim columnRef As String
        Dim headerName As String
        
        enabled = GetLogCheckboxValue(frm, fieldName)
        columnRef = GetLogRefEditValue(frm, fieldName)
        headerName = GetLogTextBoxValue(frm, fieldName)
        
        ' Validate and extract column letter from RefEdit value
        Dim columnLetter As String
        columnLetter = ExtractColumnLetterFromRefEdit(columnRef)
        
        ' Validate column assignment
        If enabled And columnLetter <> "" Then
            If Not OCRConfig.ValidateColumnAssignment(fieldName, columnLetter) Then
                MsgBox "Invalid column assignment for " & fieldName & ": " & columnLetter & ". Please choose a different column.", vbExclamation
                Exit Sub
            End If
        End If
        
        ' Save to configuration
        OCRConfig.SetLogFieldEnabled fieldName, enabled
        If columnLetter <> "" Then
            OCRConfig.SetColumnMapping fieldName, columnLetter
        End If
        If headerName <> "" Then
            OCRConfig.SetHeaderName fieldName, headerName
        End If
        
        OCRUtils.LogToFile "UserFormSettings: Saved " & groupName & " field: " & fieldName & " (enabled: " & enabled & ", column: " & columnLetter & ", header: " & headerName & ")"
    Next i
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "UserFormSettings: Error saving " & groupName & " field group: " & Err.Description
End Sub

' Get log checkbox value by field name
Private Function GetLogCheckboxValue(frm As frmSettings, fieldName As String) As Boolean
    On Error Resume Next
    
    Select Case fieldName
        Case "From": GetLogCheckboxValue = frm.chkLogFrom.Value
        Case "To": GetLogCheckboxValue = frm.chkLogTo.Value
        Case "LetterRef": GetLogCheckboxValue = frm.chkLogReference.Value
        Case "LetterDate": GetLogCheckboxValue = frm.chkLogDate.Value
        Case "Subject": GetLogCheckboxValue = frm.chkLogSubject.Value
        Case "References": GetLogCheckboxValue = frm.chkLogReferences.Value
        Case "Body": GetLogCheckboxValue = frm.chkLogBody.Value
        Case "Summary": GetLogCheckboxValue = frm.chkLogSummary.Value
        Case "Tags": GetLogCheckboxValue = frm.chkLLogTags.Value
        Case "ResponseLetter": GetLogCheckboxValue = frm.chkResponse.Value
        Case "ResponseDate": GetLogCheckboxValue = frm.chkResponseDate.Value
        Case "ReferringLetter": GetLogCheckboxValue = frm.chkReferring.Value
        Case "ReferringDate": GetLogCheckboxValue = frm.chkReferringDate.Value
        Case Else: GetLogCheckboxValue = False
    End Select
    
    On Error GoTo 0
End Function

' Get log RefEdit value by field name
Private Function GetLogRefEditValue(frm As frmSettings, fieldName As String) As String
    On Error Resume Next
    
    Select Case fieldName
        Case "From": GetLogRefEditValue = frm.refFrom.Value
        Case "To": GetLogRefEditValue = frm.refTo.Value
        Case "LetterRef": GetLogRefEditValue = frm.refReference.Value
        Case "LetterDate": GetLogRefEditValue = frm.refDate.Value
        Case "Subject": GetLogRefEditValue = frm.refSubject.Value
        Case "References": GetLogRefEditValue = frm.refReferences.Value
        Case "Body": GetLogRefEditValue = frm.refBody.Value
        Case "Summary": GetLogRefEditValue = frm.refSummary.Value
        Case "Tags": GetLogRefEditValue = frm.refTags.Value
        Case "ResponseLetter": GetLogRefEditValue = frm.refResponse.Value
        Case "ResponseDate": GetLogRefEditValue = frm.refResponseDate.Value
        Case "ReferringLetter": GetLogRefEditValue = frm.refReferring.Value
        Case "ReferringDate": GetLogRefEditValue = frm.refReferringDate.Value
        Case Else: GetLogRefEditValue = ""
    End Select
    
    On Error GoTo 0
End Function

' Get log TextBox value by field name
Private Function GetLogTextBoxValue(frm As frmSettings, fieldName As String) As String
    On Error Resume Next
    
    Select Case fieldName
        Case "From": GetLogTextBoxValue = frm.txtFrom.Value
        Case "To": GetLogTextBoxValue = frm.txtTo.Value
        Case "LetterRef": GetLogTextBoxValue = frm.txtReference.Value
        Case "LetterDate": GetLogTextBoxValue = frm.txtDate.Value
        Case "Subject": GetLogTextBoxValue = frm.txtSubject.Value
        Case "References": GetLogTextBoxValue = frm.txtReferences.Value
        Case "Body": GetLogTextBoxValue = frm.txtBody.Value
        Case "Summary": GetLogTextBoxValue = frm.txtSummary.Value
        Case "Tags": GetLogTextBoxValue = frm.txtTags.Value
        Case "ResponseLetter": GetLogTextBoxValue = frm.txtResponse.Value
        Case "ResponseDate": GetLogTextBoxValue = frm.txtResponseDate.Value
        Case "ReferringLetter": GetLogTextBoxValue = frm.txtRefering.Value
        Case "ReferringDate": GetLogTextBoxValue = frm.txtReferingDate.Value
        Case Else: GetLogTextBoxValue = ""
    End Select
    
    On Error GoTo 0
End Function

' Extract column letter from RefEdit value (e.g., "B:B" -> "B")
Private Function ExtractColumnLetterFromRefEdit(refEditValue As String) As String
    On Error Resume Next
    
    If refEditValue = "" Then
        ExtractColumnLetterFromRefEdit = ""
        Exit Function
    End If
    
    ' Handle formats like "B:B", "B1:B1", "$B:$B", etc.
    Dim parts() As String
    parts = Split(refEditValue, ":")
    
    If UBound(parts) >= 0 Then
        Dim columnPart As String
        columnPart = parts(0)
        
        ' Remove $ signs and numbers to get just the column letter
        columnPart = Replace(columnPart, "$", "")
        
        ' Extract letters only (remove numbers)
        Dim i As Integer
        Dim result As String
        result = ""
        
        For i = 1 To Len(columnPart)
            Dim char As String
            char = Mid(columnPart, i, 1)
            If char >= "A" And char <= "Z" Then
                result = result & char
            End If
        Next i
        
        ExtractColumnLetterFromRefEdit = result
    Else
        ExtractColumnLetterFromRefEdit = ""
    End If
    
    On Error GoTo 0
End Function

