Attribute VB_Name = "Prov_DoclingOCRWeb"
'
' Docling OCR Module using VBA-Web for Excel VBA
' Uses <PERSON> Hall's VBA-Web library for HTTP requests
' Author: AI Assistant
' Dependencies: VBA-JSON library, VBA-Web library, Docling server running locally
'

Option Explicit

' ================================
' CONSTANTS
' ================================

Private Const DOCLING_API_URL As String = "http://localhost:5001/v1alpha/convert/file"
Private Const DEFAULT_OUTPUT_FORMAT As String = "text"
Private Const DOCLING_OCR_ENGINE As String = "tesseract"
Private Const DOCLING_DO_OCR As Boolean = True
Private Const DOCLING_FORCE_OCR As Boolean = True
Private Const DOCLING_IMAGE_EXPORT_MODE As String = "placeholder"

' ================================
' PUBLIC INTERFACE
' ================================

' Main function to process PDF with Docling using VBA-Web hybrid approach
Public Function ProcessPDFWithDoclingWeb(ByVal pdfPath As String, Optional ByVal outputFormat As String = "text") As ocrResult
    On Error GoTo ErrorHandler
    
    Dim Result As ocrResult
    Dim Client As WebClient
    Dim Request As WebRequest
    Dim Response As WebResponse
    'Dim multipartBody As String ' No longer needed
    'Dim boundary As String ' No longer needed
    
    ' Initialize result
    Result.Success = False
    Result.LetterReference = "Not Found"
    Result.LetterDate = "Not Found"
    Result.Subject = "Not Found"
    Result.References = "Not Found"
    Result.Body = ""
    
    ' Log the start of processing
    OCRUtils.LogToFile "DoclingOCRWeb: Starting processing of: " & pdfPath
    OCRUtils.LogToFile "DoclingOCRWeb: Output format: " & outputFormat
    
    ' Validate file exists
    If Not FileExists(pdfPath) Then
        Result.ErrorMessage = "File not found: " & pdfPath
        OCRUtils.LogToFile "DoclingOCRWeb: ERROR - File not found: " & pdfPath
        GoTo ExitFunction
    End If
    
    ' Create VBA-Web client with proper BaseUrl
    Set Client = New WebClient
    Client.baseUrl = "http://localhost:5001"
    Client.TimeoutMS = 300000 ' 5 minutes timeout
    
    ' Create request with proper Resource path
    Set Request = New WebRequest
    Request.Resource = "/v1alpha/convert/file"
    Request.Method = VbWebMethod.vbWebHttpPost
    
    ' Add file and form fields using VBA-Web methods (fix parameter order)
    Request.AddFile "files", pdfPath, ExtractFileNameFromPathWeb(pdfPath), "application/pdf"
    Request.AddFormField "to_formats", outputFormat
    Request.AddFormField "do_ocr", IIf(DOCLING_DO_OCR, "true", "false")
    Request.AddFormField "force_ocr", IIf(DOCLING_FORCE_OCR, "true", "false")
    Request.AddFormField "ocr_engine", DOCLING_OCR_ENGINE
    Request.AddFormField "image_export_mode", DOCLING_IMAGE_EXPORT_MODE
    
    OCRUtils.LogToFile "DoclingOCRWeb: Built VBA-Web request using AddFile and AddFormParameter"
    OCRUtils.LogToFile "DoclingOCRWeb: image_export_mode set to: " & DOCLING_IMAGE_EXPORT_MODE
    
    ' Diagnostic logging disabled - VBA-Web caching fix applied

    ' Execute request
    Set Response = Client.Execute(Request)
    
    If Response.StatusCode <> 200 Then
        Result.ErrorMessage = "HTTP Error " & Response.StatusCode & ": " & Response.StatusDescription
        OCRUtils.LogToFile "DoclingOCRWeb: ERROR - " & Result.ErrorMessage
        OCRUtils.LogToFile "DoclingOCRWeb: Response content: " & Response.content
        GoTo ExitFunction
    End If
    
    ' Parse response
    Result = ParseDoclingResponseWeb(Response.content, outputFormat)
    
    If Result.Success Then
        OCRUtils.LogToFile "DoclingOCRWeb: Successfully processed PDF - extracted " & Len(Result.Body) & " characters"
    Else
        OCRUtils.LogToFile "DoclingOCRWeb: ERROR - Failed to parse response: " & Result.ErrorMessage
    End If
    
ExitFunction:
    ProcessPDFWithDoclingWeb = Result
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error in ProcessPDFWithDoclingWeb: " & Err.Description
    OCRUtils.LogToFile "DoclingOCRWeb: ERROR - " & Result.ErrorMessage
    ProcessPDFWithDoclingWeb = Result
End Function

' ================================
' PRIVATE HELPER FUNCTIONS
' ================================

' (Removed GenerateWebBoundary, GenerateRandomHexWeb, BuildMultipartBodyWeb, CreateFormFieldPartWeb, BytesToString, ReadFileToBytes, ExtractFileName as they are no longer needed or handled by VBA-Web)

' Parse Docling API response using VBA-Web
Private Function ParseDoclingResponseWeb(ByVal jsonString As String, ByVal outputFormat As String) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim Result As ocrResult
    Dim apiResponse As Object
    Dim document As Object
    Dim extractedText As String
    
    ' Initialize result
    Result.Success = False
    Result.LetterReference = "Not Found"
    Result.LetterDate = "Not Found"
    Result.Subject = "Not Found"
    Result.References = "Not Found"
    Result.Body = ""
    
    ' Log response for debugging
    OCRUtils.LogToFile "=== DOCLING WEB API RESPONSE START ==="
    OCRUtils.LogToFile "Response length: " & Len(jsonString) & " characters"
    OCRUtils.LogToFile "First 500 chars: " & Left(jsonString, 500)
    OCRUtils.LogToFile "=== DOCLING WEB API RESPONSE END ==="
    
    ' Parse JSON response using VBA-Web helper
    Set apiResponse = ParseJson(jsonString) ' WebHelpers.ParseJson(jsonString)
    
    If apiResponse Is Nothing Then
        Result.ErrorMessage = "Failed to parse JSON response"
        OCRUtils.LogToFile "DoclingOCRWeb: ERROR - Failed to parse JSON response"
        GoTo ExitFunction
    End If
    
    ' Check status
    If apiResponse.Exists("status") Then
        If apiResponse("status") <> "success" Then
            Result.ErrorMessage = "Docling API returned error status: " & apiResponse("status")
            If apiResponse.Exists("errors") Then
                Result.ErrorMessage = Result.ErrorMessage & " - " & ConvertToJson(apiResponse("errors")) 'WebHelpers.ConvertToJson(apiResponse("errors"))
            End If
            OCRUtils.LogToFile "DoclingOCRWeb: ERROR - " & Result.ErrorMessage
            GoTo ExitFunction
        End If
    End If
    
    ' Extract document content
    If apiResponse.Exists("document") Then
        Set document = apiResponse("document")
        
        ' Extract text based on output format
        Select Case LCase(outputFormat)
            Case "text"
                If document.Exists("text_content") And Not IsNull(document("text_content")) Then
                    extractedText = document("text_content")
                End If
            Case "md", "markdown"
                If document.Exists("md_content") And Not IsNull(document("md_content")) Then
                    extractedText = document("md_content")
                End If
            Case "json"
                If document.Exists("json_content") And Not IsNull(document("json_content")) Then
                    extractedText = ConvertToJson(document("json_content")) ' WebHelpers.ConvertToJson(document("json_content"))
                End If
            Case Else
                Result.ErrorMessage = "Unsupported output format: " & outputFormat
                GoTo ExitFunction
        End Select
        
        If extractedText <> "" Then
            ' For Docling, we get raw OCR text without structured extraction
            ' Set only the Body content - structured fields remain empty
            Result.Body = CleanDoclingTextWeb(extractedText)
            Result.Success = True
            
            OCRUtils.LogToFile "DoclingOCRWeb: Successfully extracted " & Len(Result.Body) & " characters of " & outputFormat & " content"
        Else
            Result.ErrorMessage = "No content found in " & outputFormat & " format"
            OCRUtils.LogToFile "DoclingOCRWeb: ERROR - No content found in " & outputFormat & " format"
        End If
    Else
        Result.ErrorMessage = "No document object in response"
        OCRUtils.LogToFile "DoclingOCRWeb: ERROR - No document object in response"
    End If
    
ExitFunction:
    ParseDoclingResponseWeb = Result
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error parsing Docling response: " & Err.Description
    OCRUtils.LogToFile "DoclingOCRWeb: ERROR - " & Result.ErrorMessage
    ParseDoclingResponseWeb = Result
End Function

' ================================
' UTILITY FUNCTIONS
' ================================

' Clean Docling text content (remove image placeholders)
Private Function CleanDoclingTextWeb(ByVal rawText As String) As String
    Dim cleanedText As String
    cleanedText = rawText
    
    ' Remove image placeholders
    cleanedText = Replace(cleanedText, "<!-- image -->", "")
    
    ' Clean up excessive line breaks (including those left by removed images)
    Do While InStr(cleanedText, vbCrLf & vbCrLf & vbCrLf) > 0
        cleanedText = Replace(cleanedText, vbCrLf & vbCrLf & vbCrLf, vbCrLf & vbCrLf)
    Loop
    
    ' Clean up double line breaks that might be left after image removal
    Do While InStr(cleanedText, vbCrLf & vbCrLf & vbCrLf) > 0
        cleanedText = Replace(cleanedText, vbCrLf & vbCrLf & vbCrLf, vbCrLf & vbCrLf)
    Loop
    
    ' Trim
    CleanDoclingTextWeb = Trim(cleanedText)
End Function

' Extract filename from full path
Private Function ExtractFileNameFromPathWeb(ByVal fullPath As String) As String
    Dim pos As Integer
    pos = InStrRev(fullPath, "\")
    If pos > 0 Then
        ExtractFileNameFromPathWeb = Mid(fullPath, pos + 1)
    Else
        ExtractFileNameFromPathWeb = fullPath
    End If
End Function

' FileExists is now in OCRUtils

' ================================
' STANDARDIZED PROVIDER INTERFACE
' ================================

' Check if the Docling provider (via VBA-Web) is available (i.e., server is running)
Private Function IsProviderAvailable() As Boolean
    On Error GoTo ErrorHandler
    Dim Client As New WebClient
    Dim Request As New WebRequest
    Dim Response As WebResponse
    Dim doclingHealthUrl As String
    
    ' Construct health URL from the base of DOCLING_API_URL
    Dim urlParts() As String
    Dim baseUrl As String
    
    urlParts = Split(DOCLING_API_URL, "/") ' DOCLING_API_URL is "http://localhost:5001/v1alpha/convert/file"
    If UBound(urlParts) >= 2 Then
        baseUrl = urlParts(0) & "//" & urlParts(2) ' Should give "http://localhost:5001"
    Else
        IsProviderAvailable = False ' Invalid base URL structure
        Exit Function
    End If

    Client.baseUrl = baseUrl
    Client.TimeoutMS = 2000 ' 2 second timeout for health check
    
    Request.Resource = "/health" ' Common health check endpoint
    Request.Method = VbWebMethod.vbWebHttpGet
    
    Set Response = Client.Execute(Request)
    
    IsProviderAvailable = (Response.StatusCode = 200)
    
    Exit Function
ErrorHandler:
    IsProviderAvailable = False
End Function

' Get a collection of available model names for this provider (output formats for Docling)
Private Function GetAvailableModels() As Collection
    Dim coll As Collection
    Set coll = New Collection
    coll.Add "text"
    coll.Add "markdown"
    coll.Add "json"
    Set GetAvailableModels = coll
End Function

' Get the type of this provider
Private Function GetProviderType() As String
    GetProviderType = "static" ' Docling models (formats) are static, transport is VBA-Web
End Function
