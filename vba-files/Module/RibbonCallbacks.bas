Attribute VB_Name = "RibbonCallbacks"
Option Explicit

' Stores the Ribbon UI object for invalidation/refresh
Public gRibbonUI As IRibbonUI

' --- Constants for ddPrimaryOCRSource Callbacks ---
' Defines the main OCR methods available.
' Item 0: VLM Direct (Ollama, OpenRouter)
' Item 1: Docling Server (followed by optional VLM for field extraction)
' Item 2: OpenRouter OCR (direct OCR processing)
Private Const PRIMARY_OCR_VLM_IDX As Integer = 0
Private Const PRIMARY_OCR_DOCLING_IDX As Integer = 1
Private Const PRIMARY_OCR_OPENROUTER_IDX As Integer = 2

' --- Constants for ddVLMProvider Callbacks ---
' VLM Providers: Ollama, OpenRouter, LMStudio
Private Const VLM_PROVIDER_OLLAMA_IDX As Integer = 0
Private Const VLM_PROVIDER_OPENROUTER_IDX As Integer = 1
Private Const VLM_PROVIDER_LMSTUDIO_IDX As Integer = 2

' --- Constants for ddDoclingTransport Callbacks ---
Private Const DOCLING_TRANSPORT_CUSTOM_IDX As Integer = 0
Private Const DOCLING_TRANSPORT_WEB_IDX As Integer = 1

Private Const NO_MODELS_RUNNING_PLACEHOLDER As String = "- No models running -"

'===============================================================================
' RIBBON LOAD CALLBACK
'===============================================================================

'Callback for customUI.onLoad
Public Sub Ribbon_OnLoad(ribbon As IRibbonUI)
    Set gRibbonUI = ribbon
    OCRUtils.LogToFile "RibbonCallbacks: Ribbon_OnLoad - gRibbonUI is set."
    
    ' Refresh models for all active local providers
    On Error Resume Next ' To prevent startup errors if providers are not available
      ' Check and refresh models for primary OCR provider if it's local
    Dim primaryProviderID As String
    primaryProviderID = OCRConfig.GetWorkflowSetting("ocr_provider", "")
    If primaryProviderID = OCRConfig.PID_OLLAMA Or primaryProviderID = OCRConfig.PID_LMSTUDIO Then
        OCRUtils.LogToFile "RibbonCallbacks: Ribbon_OnLoad - Refreshing models for primary OCR provider: " & primaryProviderID
        OCRConfig.RefreshProviderModels primaryProviderID
        If Err.Number <> 0 Then
            OCRUtils.LogToFile "RibbonCallbacks: Ribbon_OnLoad - Error refreshing primary provider models: " & Err.Description
            Err.Clear
        End If
    End If
    
    ' Check and refresh models for extraction provider if it's local
    Dim extractionProviderID As String
    extractionProviderID = OCRConfig.GetWorkflowSetting("extraction.provider", "")
    If extractionProviderID = OCRConfig.PID_OLLAMA Or extractionProviderID = OCRConfig.PID_LMSTUDIO Then
        ' Only refresh if different from primary (avoid duplicate refresh)
        If extractionProviderID <> primaryProviderID Then
            OCRUtils.LogToFile "RibbonCallbacks: Ribbon_OnLoad - Refreshing models for extraction provider: " & extractionProviderID
            OCRConfig.RefreshProviderModels extractionProviderID
            If Err.Number <> 0 Then
                OCRUtils.LogToFile "RibbonCallbacks: Ribbon_OnLoad - Error refreshing extraction provider models: " & Err.Description
                Err.Clear
            End If
        End If
    End If
    
    On Error GoTo 0
    
    ' Invalidate the entire ribbon to ensure all get* callbacks are called
    ' and controls reflect the current state from OCRConfig.
    If Not gRibbonUI Is Nothing Then
        gRibbonUI.Invalidate
        OCRUtils.LogToFile "RibbonCallbacks: Ribbon_OnLoad - Full ribbon invalidated to refresh controls."
    Else
        OCRUtils.LogToFile "RibbonCallbacks: Ribbon_OnLoad - ERROR - gRibbonUI is Nothing, cannot invalidate."
    End If
End Sub

'===============================================================================
' EXECUTION GROUP CALLBACKS
'===============================================================================

'Callback for btnProcessOCROnly onAction
Public Sub ProcessOCROnly_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'OCR Only' clicked."
    On Error Resume Next
    OCRManager.ProcessPDFOCR "OCR_ONLY"
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "An error occurred during OCR processing: " & Err.Description, vbCritical, "Processing Error"
    End If
    On Error GoTo 0
End Sub

'Callback for btnProcessFieldsOnly onAction
Public Sub ProcessFieldsOnly_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'Fields Only' clicked."
    On Error Resume Next
    OCRManager.ProcessPDFOCR "FIELDS_ONLY"
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "An error occurred during field extraction: " & Err.Description, vbCritical, "Processing Error"
    End If
    On Error GoTo 0
End Sub

'Callback for btnProcessOCRAndFields onAction
Public Sub ProcessOCRAndFields_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'OCR + Fields' clicked."
    On Error Resume Next
    OCRManager.ProcessPDFOCR "OCR_AND_FIELDS"
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "An error occurred during OCR and field extraction: " & Err.Description, vbCritical, "Processing Error"
    End If
    On Error GoTo 0
End Sub

'Callback for btnSetupWorksheet onAction - Opens settings form on page 2 (Log Settings)
Public Sub SetupWorksheet_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'Setup Log' clicked - opening settings form on page 2."
    On Error Resume Next
    Dim frm As Object
    Set frm = New frmSettings
    UserFromSettings.InitializeSettingsForm frm ' Call the initialization logic from UserFromSettings
    ' Set to page 2 (index 1) for Log Settings
    If Not frm.MultiPage1 Is Nothing Then
        frm.MultiPage1.Value = 1
    End If
    frm.Show
    
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "Error opening settings form: " & Err.Description, vbCritical, "Setup Error"
        OCRUtils.LogToFile "RibbonCallbacks: ERROR in SetupWorksheet_Clicked: " & Err.Description
    End If
    On Error GoTo 0
End Sub

'Callback for btnSource onAction - Opens settings form on page 3 (Source Settings)
Public Sub SetupSource_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'Setup Source' clicked - opening settings form on page 3."
    On Error Resume Next
    Dim frm As Object
    Set frm = New frmSettings
    UserFromSettings.InitializeSettingsForm frm ' Call the initialization logic from UserFromSettings
    ' Set to page 3 (index 2) for Source Settings
    If Not frm.MultiPage1 Is Nothing Then
        frm.MultiPage1.Value = 2
    End If
    frm.Show
    
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "Error opening settings form: " & Err.Description, vbCritical, "Setup Error"
        OCRUtils.LogToFile "RibbonCallbacks: ERROR in SetupSource_Clicked: " & Err.Description
    End If
    On Error GoTo 0
End Sub

'===============================================================================
' ADVANCED GROUP CALLBACKS (Initial Placeholders)
'===============================================================================


'Callback for btnCompareAllMethods onAction
Public Sub CompareAllMethods_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'Compare All Methods' clicked."
    On Error Resume Next
    OCRManager.CompareAllMethods
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "An error occurred during method comparison: " & Err.Description, vbCritical, "Comparison Error"
    End If
    On Error GoTo 0
End Sub

'Callback for btnTestConnections onAction
Public Sub TestConnections_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'Test Connections' clicked."
    On Error Resume Next
    OCRManager.TestAllConnections
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "An error occurred during connection testing: " & Err.Description, vbCritical, "Connection Test Error"
    End If
    On Error GoTo 0
End Sub

'Callback for btnRefreshOllamaModels onAction (now "Refresh Local Models")
Public Sub RefreshOllamaModels_Clicked(control As IRibbonControl)
    Dim activeVLMProviderID As String
    Dim providerName As String
    Dim startTime As Double
    Dim refreshTime As Double
    Dim successMsg As String
    Dim errorMsg As String
    
    startTime = Timer
    Application.ScreenUpdating = False
    
    ' Determine the currently active VLM provider for extraction
    activeVLMProviderID = OCRConfig.GetVLMProviderIDForExtraction()
    providerName = OCRConfig.GetProviderName(activeVLMProviderID) ' Get the display name
    
    OCRUtils.LogToFile "Ribbon: 'Refresh Local Models' clicked for provider: " & providerName & " (ID: " & activeVLMProviderID & ")"
    Application.StatusBar = "Refreshing models for " & providerName & "..."
    
    On Error GoTo ErrorHandler
    
    Select Case activeVLMProviderID
        Case OCRConfig.PID_OLLAMA
            OCRUtils.LogToFile "RibbonCallbacks: Manual model refresh initiated for Ollama by user"
            OCRConfig.RefreshProviderModels OCRConfig.PID_OLLAMA
            providerName = "Ollama" ' Ensure consistent naming for messages
            
        Case OCRConfig.PID_LMSTUDIO
            OCRUtils.LogToFile "RibbonCallbacks: Manual model refresh initiated for LM Studio by user"
            OCRConfig.RefreshProviderModels OCRConfig.PID_LMSTUDIO
            providerName = "LM Studio" ' Ensure consistent naming for messages
            
        Case Else
            Application.StatusBar = False
            Application.ScreenUpdating = True
            MessageBoxHelper.ShowMessage "Model refresh is only applicable for local providers (Ollama, LM Studio). " & _
                   "The currently selected VLM provider ('" & providerName & "') does not support this.", vbInformation, "Refresh Not Applicable"
            OCRUtils.LogToFile "RibbonCallbacks: Model refresh not applicable for provider: " & providerName
            Exit Sub
    End Select
    
    ' Calculate timing
    refreshTime = Timer - startTime
    
    ' Show success message
    successMsg = providerName & " models refreshed successfully!" & vbCrLf & _
                 "Refresh completed in " & Format(refreshTime, "0.00") & " seconds." & vbCrLf & vbCrLf & _
                 "The VLM Model dropdown will now show the latest available models for " & providerName & "."
    
    MessageBoxHelper.ShowMessage successMsg, vbInformation, providerName & " Models Refreshed"
    
    ' Force ribbon refresh to show updated models immediately
    If Not gRibbonUI Is Nothing Then
        gRibbonUI.Invalidate ' Full invalidation is safer for dropdowns
        OCRUtils.LogToFile "RibbonCallbacks: Forced FULL ribbon invalidation after manual refresh for " & providerName
    End If
    
    ' Restore application state
    Application.StatusBar = False
    Application.ScreenUpdating = True
    
    OCRUtils.LogToFile "RibbonCallbacks: Manual " & providerName & " model refresh completed successfully in " & Format(refreshTime, "0.00") & " seconds"
    Exit Sub
    
ErrorHandler:
    Application.StatusBar = False
    Application.ScreenUpdating = True
    
    ' Try to get provider name again in case it was not set before error
    If providerName = "" And activeVLMProviderID <> "" Then
        providerName = OCRConfig.GetProviderName(activeVLMProviderID)
    ElseIf providerName = "" Then
        providerName = "the selected local provider"
    End If
    
    errorMsg = "Error refreshing " & providerName & " models:" & vbCrLf & vbCrLf & _
               Err.Description & vbCrLf & vbCrLf & _
               "Please check that " & providerName & " is running and accessible, then try again." & vbCrLf & _
               "Check the debug log for more details."
    
    MessageBoxHelper.ShowMessage errorMsg, vbCritical, "Refresh Failed"
    OCRUtils.LogToFile "RibbonCallbacks: Manual " & providerName & " model refresh failed: " & Err.Description
    
    ' Still try to refresh the ribbon in case partial data was updated
    If Not gRibbonUI Is Nothing Then
        gRibbonUI.Invalidate ' Invalidate fully as the error might affect multiple controls
    End If
End Sub

'Callback for btnViewDebugLog onAction
Public Sub ViewDebugLog_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'View Debug Log' clicked."
    On Error Resume Next
    Dim logPath As String
    ' Get log path from configuration
    logPath = OCRUtils.GetFullLogPath()
    
    If OCRUtils.FileExists(logPath) Then
        CreateObject("Shell.Application").Open logPath
    Else
        MessageBoxHelper.ShowMessage "Debug log file not found at: " & logPath, vbExclamation, "Log Not Found"
    End If
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "Could not open debug log: " & Err.Description, vbCritical, "Error Opening Log"
    End If
    On Error GoTo 0
End Sub

'===============================================================================
' SETTINGS GROUP CALLBACK
'===============================================================================
Public Sub OpenSettingsForm_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'Configure OCR & Extraction...' clicked."
    On Error Resume Next
    ' Ensure UserFromSettings module is available and Button1_Click calls FrmSettings.Show
    ' Or directly:
    Dim frm As Object
    Set frm = New frmSettings
    UserFromSettings.InitializeSettingsForm frm ' Call the initialization logic from UserFromSettings
    frm.Show
    
    If Err.Number <> 0 Then
        MessageBoxHelper.ShowMessage "Error opening settings form: " & Err.Description, vbCritical
        OCRUtils.LogToFile "RibbonCallbacks: ERROR in OpenSettingsForm_Clicked: " & Err.Description
    End If
    On Error GoTo 0
End Sub

'Callback for btnCurrentConfiguration onAction - Shows current OCR and extraction configuration
Public Sub ShowConfiguration_Clicked(control As IRibbonControl)
    OCRUtils.LogToFile "Ribbon: 'Information' clicked."
    
    On Error GoTo ErrorHandler
    
    Dim msg As String
    Dim ocrProvider As String, ocrModel As String
    Dim extractionEnabled As Boolean
    Dim extractionProvider As String, extractionModel As String
    Dim workflowSource As String
    Dim selectedFields As String
    Dim fieldDict As Object
    Dim fieldName As Variant
    Dim fieldCount As Integer
    
    ' Get current workflow source
    workflowSource = OCRConfig.GetWorkflowSetting("primary_ocr_source", "VLM")
    
    ' Build the message header
    msg = "==== CURRENT OCR WORKFLOW CONFIGURATION ====" & vbCrLf & vbCrLf
    
    ' Show primary OCR source
    Select Case workflowSource
        Case "VLM"
            msg = msg & "PRIMARY OCR SOURCE: AI Vision Model" & vbCrLf
        Case "Docling"
            msg = msg & "PRIMARY OCR SOURCE: Direct OCR (Docling Server)" & vbCrLf
        Case "OpenRouterOCR"
            msg = msg & "PRIMARY OCR SOURCE: OpenRouter PDF Engine" & vbCrLf
        Case Else
            msg = msg & "PRIMARY OCR SOURCE: " & workflowSource & vbCrLf
    End Select
    
    msg = msg & vbCrLf & "---- OCR STEP ----" & vbCrLf
    
    If workflowSource = "Docling" Then
        ' Docling doesn't use provider/model selection
        msg = msg & "Method: Docling Server Direct OCR" & vbCrLf
        msg = msg & "Model: N/A (Docling built-in OCR engine)" & vbCrLf
    Else
        ' Get OCR provider and model for VLM mode
        ocrProvider = OCRConfig.GetWorkflowSetting("ocr_provider", "")
        ocrModel = OCRConfig.GetWorkflowSetting("ocr_model", "")
        
        If ocrProvider <> "" Then
            msg = msg & "Provider: " & OCRConfig.GetProviderName(ocrProvider) & vbCrLf
            If ocrModel <> "" Then
                msg = msg & "Model: " & ocrModel & vbCrLf
            Else
                msg = msg & "Model: (No model selected)" & vbCrLf
            End If
        Else
            msg = msg & "Provider: (Not configured)" & vbCrLf
        End If
    End If
    
    ' Get extraction settings
    extractionEnabled = OCRConfig.GetWorkflowSetting("extraction.enabled", False)
    
    msg = msg & vbCrLf & "---- FIELD EXTRACTION STEP ----" & vbCrLf
    
    If extractionEnabled Then
        msg = msg & "Status: ENABLED" & vbCrLf
        
        extractionProvider = OCRConfig.GetWorkflowSetting("extraction.provider", "")
        extractionModel = OCRConfig.GetWorkflowSetting("extraction.model", "")
        
        If extractionProvider <> "" Then
            msg = msg & "Provider: " & OCRConfig.GetProviderName(extractionProvider) & vbCrLf
            If extractionModel <> "" Then
                msg = msg & "Model: " & extractionModel & vbCrLf
            Else
                msg = msg & "Model: (No model selected)" & vbCrLf
            End If
        Else
            msg = msg & "Provider: (Not configured)" & vbCrLf
        End If
        
        ' Get selected fields
        msg = msg & vbCrLf & "Selected Fields:" & vbCrLf
        Set fieldDict = OCRConfig.GetAllFieldSelectionStates()
        fieldCount = 0
        
        If Not fieldDict Is Nothing Then
            For Each fieldName In fieldDict.keys
                If fieldDict(fieldName) = True Then
                    msg = msg & "  • " & fieldName & vbCrLf
                    fieldCount = fieldCount + 1
                End If
            Next fieldName
        End If
        
        If fieldCount = 0 Then
            msg = msg & "  (No fields selected)" & vbCrLf
        End If
    Else
        msg = msg & "Status: DISABLED" & vbCrLf
        msg = msg & "(OCR text will be placed in Body column only)" & vbCrLf
    End If
    
    msg = msg & vbCrLf & "==========================================" & vbCrLf
    msg = msg & vbCrLf & "To modify these settings, click 'Configuration' or 'Settings'."
    
    ' Show the message
    MessageBoxHelper.ShowMessage msg, vbInformation, "Current OCR Configuration"
    
    Exit Sub
    
ErrorHandler:
    MessageBoxHelper.ShowMessage "Error retrieving configuration: " & Err.Description, vbCritical, "Configuration Error"
    OCRUtils.LogToFile "RibbonCallbacks: ERROR in ShowConfiguration_Clicked: " & Err.Description
End Sub

'===============================================================================
' WORKFLOW SETUP GROUP CALLBACKS (DEPRECATED - TO BE REMOVED)
'===============================================================================

' --- ddPrimaryOCRSource Callbacks (DEPRECATED - kept for compatibility) ---

Public Sub GetItemCount_PrimaryOCRSource(control As IRibbonControl, ByRef returnedVal)
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "RibbonCallbacks: Entering GetItemCount_PrimaryOCRSource"
    returnedVal = 3 ' VLM Direct, Docling Server, OpenRouter OCR
    OCRUtils.LogToFile "RibbonCallbacks: Exiting GetItemCount_PrimaryOCRSource with count: " & returnedVal
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "RibbonCallbacks: ERROR in GetItemCount_PrimaryOCRSource: " & Err.Description & " (Err.Number: " & Err.Number & ")"
    returnedVal = 0 ' Safe default
End Sub

Public Sub GetItemLabel_PrimaryOCRSource(control As IRibbonControl, index As Integer, ByRef returnedVal)
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "RibbonCallbacks: Entering GetItemLabel_PrimaryOCRSource for index " & index
    Select Case index
        Case PRIMARY_OCR_VLM_IDX: returnedVal = "AI (AI OCR + AI Fields)"
        Case PRIMARY_OCR_DOCLING_IDX: returnedVal = "Docling (Direct OCR + AI Fields)"
        Case PRIMARY_OCR_OPENROUTER_IDX: returnedVal = "OpenRouter PDF Engine (Direct Processing)"
        Case Else: returnedVal = "Unknown Source " & index
    End Select
    OCRUtils.LogToFile "RibbonCallbacks: Exiting GetItemLabel_PrimaryOCRSource with label: " & returnedVal
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "RibbonCallbacks: ERROR in GetItemLabel_PrimaryOCRSource for index " & index & ": " & Err.Description & " (Err.Number: " & Err.Number & ")"
    returnedVal = "Error" ' Safe default
End Sub

Public Sub OnAction_PrimaryOCRSource(control As IRibbonControl, Id As String, index As Integer)
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "RibbonCallbacks: Entering OnAction_PrimaryOCRSource for index " & index
    
    Dim selectedProviderCategory As String
    
    Select Case index
        Case PRIMARY_OCR_VLM_IDX
            selectedProviderCategory = "VLM"
            ' Set the category to VLM - the specific provider will be determined from vlm_provider_id_for_ocr
            OCRConfig.SetCurrentPrimaryOCRSourceID "VLM"
            OCRUtils.LogToFile "RibbonCallbacks: PrimaryOCRSource category set to VLM. Using saved VLM provider/model from settings."
            
        Case PRIMARY_OCR_DOCLING_IDX
            selectedProviderCategory = "Docling"
            ' Set the category to Docling - the specific transport will be determined from docling_transport_id
            OCRConfig.SetCurrentPrimaryOCRSourceID "Docling"
            OCRUtils.LogToFile "RibbonCallbacks: PrimaryOCRSource category set to Docling."

        Case PRIMARY_OCR_OPENROUTER_IDX
            selectedProviderCategory = "OpenRouterOCR"
            OCRConfig.SetCurrentPrimaryOCRSourceID "OpenRouterOCR"
            OCRUtils.LogToFile "RibbonCallbacks: PrimaryOCRSource category set to OpenRouterOCR."
            
        Case Else
            OCRUtils.LogToFile "RibbonCallbacks: OnAction_PrimaryOCRSource - Invalid index: " & index
            Exit Sub
    End Select
    
    OCRConfig.SaveConfigurationToJSON
    RefreshRibbon ' Refresh to reflect any changes, though most are now in UserForm
    OCRUtils.LogToFile "RibbonCallbacks: Exiting OnAction_PrimaryOCRSource. Category selected: " & selectedProviderCategory
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "RibbonCallbacks: ERROR in OnAction_PrimaryOCRSource for index " & index & ": " & Err.Description & " (Err.Number: " & Err.Number & ")"
End Sub

Public Sub GetSelectedItemIndex_PrimaryOCRSource(control As IRibbonControl, ByRef returnedVal)
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "RibbonCallbacks: Entering GetSelectedItemIndex_PrimaryOCRSource"
    Dim currentSourceID As String
    currentSourceID = OCRConfig.GetWorkflowSetting("primary_ocr_source", "")
    Select Case currentSourceID
        Case "VLM"
            returnedVal = PRIMARY_OCR_VLM_IDX
        Case "Docling"
            returnedVal = PRIMARY_OCR_DOCLING_IDX
        Case "OpenRouterOCR"
            returnedVal = PRIMARY_OCR_OPENROUTER_IDX
        Case Else
            OCRUtils.LogToFile "RibbonCallbacks: GetSelectedItemIndex_PrimaryOCRSource - Unknown currentSourceID: " & currentSourceID & ". Defaulting."
            returnedVal = PRIMARY_OCR_VLM_IDX ' Default
    End Select
    OCRUtils.LogToFile "RibbonCallbacks: Exiting GetSelectedItemIndex_PrimaryOCRSource with index: " & returnedVal
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "RibbonCallbacks: ERROR in GetSelectedItemIndex_PrimaryOCRSource: " & Err.Description & " (Err.Number: " & Err.Number & ")"
    returnedVal = PRIMARY_OCR_VLM_IDX ' Safe default
End Sub

' --- Extract Fields Checkbox Callbacks (DEPRECATED - TO BE REMOVED) ---

' Get checkbox state for Extract Fields (DEPRECATED)
Public Sub GetPressed_ExtractFields(control As IRibbonControl, ByRef returnedVal)
    On Error GoTo ErrorHandler
    ' DEPRECATED - Always return False
    returnedVal = False
    Exit Sub
ErrorHandler:
    returnedVal = False
End Sub

' Handle checkbox press for Extract Fields (DEPRECATED)
Public Sub OnAction_ExtractFields(control As IRibbonControl, pressed As Boolean)
    ' DEPRECATED - No longer used
    OCRUtils.LogToFile "DEPRECATED: OnAction_ExtractFields called but no longer functional"
End Sub

'===============================================================================
' DIRECT OCR TOGGLE BUTTON CALLBACKS
'===============================================================================

' Get toggle button state for Direct OCR (Docling)
Public Sub GetPressed_DirectOCR(control As IRibbonControl, ByRef returnedVal)
    On Error Resume Next
    Dim currentSource As String
    currentSource = OCRConfig.GetWorkflowSetting("primary_ocr_source", "VLM")
    returnedVal = (currentSource = "Docling")
    OCRUtils.LogToFile "RibbonCallbacks: GetPressed_DirectOCR - returnedVal = " & returnedVal & " (currentSource = " & currentSource & ")"
    On Error GoTo 0
End Sub

' Handle toggle button press for Direct OCR
Public Sub OnAction_DirectOCR(control As IRibbonControl, pressed As Boolean)
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "RibbonCallbacks: OnAction_DirectOCR - pressed = " & pressed
    
    If pressed Then
        ' Switch to Docling (Direct OCR)
        OCRConfig.SetWorkflowSetting "primary_ocr_source", "Docling"
        OCRUtils.LogToFile "Switched to Direct OCR mode (Docling)"
        
        ' Show brief notification
        MessageBoxHelper.ShowMessage "Switched to Direct OCR mode (Docling)" & vbCrLf & _
                                   "OCR will be performed using Docling Server.", _
                                   vbInformation, "Direct OCR Enabled"
    Else
        ' Switch to VLM
        OCRConfig.SetWorkflowSetting "primary_ocr_source", "VLM"
        OCRUtils.LogToFile "Switched to AI Vision Model mode"
        
        ' Get the current VLM provider/model for the notification
        Dim vlmProvider As String
        Dim vlmModel As String
        vlmProvider = OCRConfig.GetWorkflowSetting("ocr_provider", "")
        vlmModel = OCRConfig.GetWorkflowSetting("ocr_model", "")
        
        Dim notifyMsg As String
        notifyMsg = "Switched to AI Vision Model mode"
        
        If vlmProvider <> "" Then
            notifyMsg = notifyMsg & vbCrLf & "Using: " & OCRConfig.GetProviderName(vlmProvider)
            If vlmModel <> "" Then
                notifyMsg = notifyMsg & " - " & vlmModel
            End If
        End If
        
        MessageBoxHelper.ShowMessage notifyMsg, vbInformation, "Vision Model Enabled"
    End If
    
    ' Save configuration
    OCRConfig.SaveConfigurationToJSON
    
    ' Refresh ribbon to update any dependent controls
    RefreshRibbon
    
    Exit Sub
    
ErrorHandler:
    MessageBoxHelper.ShowMessage "Error toggling Direct OCR mode: " & Err.Description, vbCritical, "Toggle Error"
    OCRUtils.LogToFile "RibbonCallbacks: ERROR in OnAction_DirectOCR: " & Err.Description
End Sub

'===============================================================================
' HELPER TO INVALIDATE RIBBON CONTROLS
'===============================================================================
Public Sub RefreshRibbonControl(ByVal controlID As String)
    If Not gRibbonUI Is Nothing Then
        gRibbonUI.InvalidateControl controlID
    End If
End Sub

Public Sub RefreshRibbon()
    If Not gRibbonUI Is Nothing Then
        gRibbonUI.Invalidate
    End If
End Sub

'===============================================================================
' MESSAGE BOX CONTROL CALLBACKS
'===============================================================================

' Toggle message boxes on/off
Public Sub ToggleMessageBoxes_Clicked(control As IRibbonControl)
    On Error GoTo ErrorHandler
    
    MessageBoxHelper.ToggleMessageBoxes
    
    ' Log the change
    If MessageBoxHelper.IsMessageBoxEnabled() Then
        OCRUtils.LogToFile "Message boxes ENABLED via ribbon toggle"
    Else
        OCRUtils.LogToFile "Message boxes DISABLED via ribbon toggle"
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "ERROR in ToggleMessageBoxes_Clicked: " & Err.Description
End Sub

' Set message box timeout
Public Sub SetMessageBoxTimeout_Clicked(control As IRibbonControl)
    On Error GoTo ErrorHandler
    
    Dim currentTimeout As Long
    Dim newTimeout As String
    
    currentTimeout = MessageBoxHelper.GetMessageBoxTimeout()
    
    newTimeout = InputBox("Enter message box timeout in seconds (0 = no timeout):", _
                         "Set Message Box Timeout", CStr(currentTimeout))
    
    If newTimeout <> "" And IsNumeric(newTimeout) Then
        MessageBoxHelper.SetMessageBoxTimeout CLng(newTimeout)
        
        If CLng(newTimeout) = 0 Then
            MessageBoxHelper.ShowMessage "Message box timeout disabled", vbInformation
        Else
            MessageBoxHelper.ShowMessage "Message box timeout set to " & newTimeout & " seconds", vbInformation
        End If
        
        OCRUtils.LogToFile "Message box timeout set to " & newTimeout & " seconds"
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "ERROR in SetMessageBoxTimeout_Clicked: " & Err.Description
End Sub

' Get checkbox state for message box toggle
Public Sub GetPressed_MessageBoxToggle(control As IRibbonControl, ByRef returnedVal)
    On Error Resume Next
    returnedVal = MessageBoxHelper.IsMessageBoxEnabled()
    On Error GoTo 0
End Sub

' Handle checkbox press for message box toggle
Public Sub OnAction_MessageBoxToggle(control As IRibbonControl, pressed As Boolean)
    On Error GoTo ErrorHandler
    
    MessageBoxHelper.SetMessageBoxEnabled pressed
    
    If pressed Then
        OCRUtils.LogToFile "Message boxes ENABLED via ribbon checkbox"
    Else
        OCRUtils.LogToFile "Message boxes DISABLED via ribbon checkbox"
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "ERROR in OnAction_MessageBoxToggle: " & Err.Description
End Sub


