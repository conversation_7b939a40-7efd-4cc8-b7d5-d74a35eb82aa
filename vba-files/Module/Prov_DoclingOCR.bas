Attribute VB_Name = "Prov_DoclingOCR"
'
' Docling OCR Module for Excel VBA
' Interfaces with local Docling server running at http://localhost:5001
' Author: AI Assistant
' Dependencies: VBA-JSON library, Docling server running locally
'

Option Explicit

' ================================
' CONSTANTS
' ================================

Private Const DOCLING_API_URL As String = "http://localhost:5001/v1alpha/convert/file"
Private Const DEFAULT_OUTPUT_FORMAT As String = "text"
Private Const DOCLING_OCR_ENGINE As String = "tesseract"
Private Const DOCLING_DO_OCR As Boolean = True
Private Const DOCLING_FORCE_OCR As Boolean = True
Private Const DOCLING_IMAGE_EXPORT_MODE As String = "placeholder"

' ================================
' PUBLIC INTERFACE
' ================================

' Main function to process PDF with Docling using robust multipart module
Public Function ProcessPDFWithDocling(ByVal pdfPath As String, Optional ByVal outputFormat As String = "text") As ocrResult
    On Error GoTo ErrorHandler
    
    Dim Result As ocrResult
    Dim multipartReq As MultipartRequest
    Dim fileBytes() As Byte
    Dim responseText As String
    
    ' Initialize result
    Result.Success = False
    Result.LetterReference = "Not Found"
    Result.LetterDate = "Not Found"
    Result.Subject = "Not Found"
    Result.References = "Not Found"
    Result.Body = ""
    
    ' Log the start of processing
    OCRUtils.LogToFile "DoclingOCR: Starting processing of: " & pdfPath
    OCRUtils.LogToFile "DoclingOCR: Output format: " & outputFormat
    
    ' Validate file exists
    If Not FileExists(pdfPath) Then
        Result.ErrorMessage = "File not found: " & pdfPath
        OCRUtils.LogToFile "DoclingOCR: ERROR - File not found: " & pdfPath
        GoTo ExitFunction
    End If
    
    ' Read file to bytes
    fileBytes = MultipartFormData.ReadFileToBytes(pdfPath)
    If UBound(fileBytes) < 0 Then
        Result.ErrorMessage = "Failed to read PDF file"
        OCRUtils.LogToFile "DoclingOCR: ERROR - Failed to read PDF file"
        GoTo ExitFunction
    End If
    
    OCRUtils.LogToFile "DoclingOCR: Read PDF file, size: " & (UBound(fileBytes) + 1) & " bytes"
    
    ' Create multipart request using robust module
    multipartReq = MultipartFormData.CreateMultipartRequest(DOCLING_API_URL)
    
    ' Add file field (must be first to match Python behavior)
    MultipartFormData.AddFileField multipartReq, "files", MultipartFormData.ExtractFileName(pdfPath), "application/pdf", fileBytes
    
    ' Add form fields in exact same order as working Python request
    MultipartFormData.AddTextField multipartReq, "to_formats", outputFormat
    MultipartFormData.AddTextField multipartReq, "do_ocr", IIf(DOCLING_DO_OCR, "true", "false")
    MultipartFormData.AddTextField multipartReq, "force_ocr", IIf(DOCLING_FORCE_OCR, "true", "false")
    MultipartFormData.AddTextField multipartReq, "ocr_engine", DOCLING_OCR_ENGINE
    MultipartFormData.AddTextField multipartReq, "image_export_mode", DOCLING_IMAGE_EXPORT_MODE
    
    OCRUtils.LogToFile "DoclingOCR: Built multipart request with boundary: " & multipartReq.Boundary
    OCRUtils.LogToFile "DoclingOCR: image_export_mode set to: " & DOCLING_IMAGE_EXPORT_MODE
    
    ' Send request using robust module
    responseText = MultipartFormData.SendMultipartRequest(multipartReq)
    If responseText = "" Then
        Result.ErrorMessage = "Failed to get response from Docling API"
        OCRUtils.LogToFile "DoclingOCR: ERROR - Empty response from Docling API"
        GoTo ExitFunction
    End If
    
    ' Parse response
    Result = ParseDoclingResponse(responseText, outputFormat)
    
    If Result.Success Then
        OCRUtils.LogToFile "DoclingOCR: Successfully processed PDF - extracted " & Len(Result.Body) & " characters"
    Else
        OCRUtils.LogToFile "DoclingOCR: ERROR - Failed to parse response: " & Result.ErrorMessage
    End If
    
ExitFunction:
    ProcessPDFWithDocling = Result
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error in ProcessPDFWithDocling: " & Err.Description
    OCRUtils.LogToFile "DoclingOCR: ERROR - " & Result.ErrorMessage
    ProcessPDFWithDocling = Result
End Function

' ================================
' PRIVATE HELPER FUNCTIONS
' ================================

' Build multipart form data for the request
Private Function BuildMultipartFormData(ByVal pdfPath As String, ByVal outputFormat As String, ByRef Boundary As String) As Byte()
    On Error GoTo ErrorHandler
    
    Dim fileBytes() As Byte
    Dim finalBody() As Byte
    Dim bodyParts As Collection
    Dim Part As Variant
    Dim totalSize As Long
    Dim currentPos As Long
    Dim i As Long
    
    ' Generate unique boundary
    Boundary = "VBAFormBoundary" & Format(Now, "yyyymmddhhnnssmmm")
    
    ' Read PDF file to bytes
    fileBytes = ReadFileToBytes(pdfPath)
    If UBound(fileBytes) < 0 Then
        OCRUtils.LogToFile "DoclingOCR: ERROR - Failed to read PDF file"
        Exit Function
    End If
    
    OCRUtils.LogToFile "DoclingOCR: Read PDF file, size: " & (UBound(fileBytes) + 1) & " bytes"
    
    ' Create collection to hold all parts
    Set bodyParts = New Collection
    
    ' Add file part
    bodyParts.Add CreateFormPart(Boundary, "files", ExtractFileNameFromPath(pdfPath), "application/pdf", fileBytes)
    
    ' Add other form fields - match EXACT curl order and parameter names
    bodyParts.Add CreateFormFieldPart(Boundary, "to_formats", outputFormat)
    bodyParts.Add CreateFormFieldPart(Boundary, "do_ocr", IIf(DOCLING_DO_OCR, "true", "false"))
    bodyParts.Add CreateFormFieldPart(Boundary, "force_ocr", IIf(DOCLING_FORCE_OCR, "true", "false"))
    bodyParts.Add CreateFormFieldPart(Boundary, "ocr_engine", DOCLING_OCR_ENGINE)
    bodyParts.Add CreateFormFieldPart(Boundary, "image_export_mode", DOCLING_IMAGE_EXPORT_MODE)
    
    ' Log each part for debugging
    Dim k As Long
    For k = 1 To bodyParts.Count
        Dim currentPartBytes() As Byte
        currentPartBytes = bodyParts(k)
        If UBound(currentPartBytes) >= 0 Then
            OCRUtils.LogToFile "DoclingOCR: Part " & k & " size: " & (UBound(currentPartBytes) + 1) & " bytes"
            ' Log first few bytes of the part if it's small enough to be a header/field
            If (UBound(currentPartBytes) + 1) < 200 Then ' Likely a field part
                 OCRUtils.LogToFile "DoclingOCR: Part " & k & " (ASCII): " & BytesToPrintableAscii(currentPartBytes)
            End If
        Else
            OCRUtils.LogToFile "DoclingOCR: Part " & k & " is empty or invalid"
        End If
    Next k
    
    ' Add closing boundary
    bodyParts.Add StringToBytes("--" & Boundary & "--" & vbCrLf)
    
    ' Calculate total size
    totalSize = 0
    For Each Part In bodyParts
        totalSize = totalSize + UBound(Part) + 1
    Next Part
    
    ' Combine all parts
    ReDim finalBody(totalSize - 1)
    currentPos = 0
    
    For Each Part In bodyParts
        For i = 0 To UBound(Part)
            finalBody(currentPos) = Part(i)
            currentPos = currentPos + 1
        Next i
    Next Part
    
    BuildMultipartFormData = finalBody
    OCRUtils.LogToFile "DoclingOCR: Built multipart form data, total size: " & totalSize & " bytes"
    
    ' Log first 500 bytes of the final request body for inspection
    If totalSize > 0 Then
        Dim previewBytes() As Byte
        Dim previewLength As Long
        previewLength = Application.WorksheetFunction.Min(500, totalSize)
        ReDim previewBytes(previewLength - 1)
        For i = 0 To previewLength - 1
            previewBytes(i) = finalBody(i)
        Next i
        OCRUtils.LogToFile "DoclingOCR: Final Request Body Preview (ASCII): " & BytesToPrintableAscii(previewBytes)
    End If
    
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "DoclingOCR: ERROR in BuildMultipartFormData: " & Err.Description
    ReDim BuildMultipartFormData(-1 To -2) ' Return empty array
End Function

' Create a form part for file upload
Private Function CreateFormPart(ByVal Boundary As String, ByVal fieldName As String, ByVal FileName As String, ByVal ContentType As String, ByRef fileBytes() As Byte) As Byte()
    Dim header As String
    Dim headerBytes() As Byte
    Dim Result() As Byte
    Dim totalSize As Long
    Dim i As Long
    
    header = "--" & Boundary & vbCrLf & _
             "Content-Disposition: form-data; name=""" & fieldName & """; filename=""" & FileName & """" & vbCrLf & _
             "Content-Type: " & ContentType & vbCrLf & vbCrLf
    
    headerBytes = StringToAsciiBytes(header) ' Use AsciiBytes for header structure
    totalSize = UBound(headerBytes) + 1 + UBound(fileBytes) + 1 + 2 ' +2 for CRLF
    
    ReDim Result(totalSize - 1)
    
    ' Copy header
    For i = 0 To UBound(headerBytes)
        Result(i) = headerBytes(i)
    Next i
    
    ' Copy file bytes
    For i = 0 To UBound(fileBytes)
        Result(UBound(headerBytes) + 1 + i) = fileBytes(i)
    Next i
    
    ' Add CRLF
    Result(totalSize - 2) = 13 ' CR
    Result(totalSize - 1) = 10 ' LF
    
    CreateFormPart = Result
End Function

' Create a form field part
Private Function CreateFormFieldPart(ByVal Boundary As String, ByVal fieldName As String, ByVal fieldValue As String) As Byte()
    Dim content As String
    content = "--" & Boundary & vbCrLf & _
              "Content-Disposition: form-data; name=""" & fieldName & """" & vbCrLf & vbCrLf & _
              fieldValue & vbCrLf
    CreateFormFieldPart = StringToAsciiBytes(content) ' Use ASCII to avoid UTF-8 BOM
End Function

' Send request to Docling API
Private Function SendDoclingRequest(ByVal apiURL As String, ByRef requestBody() As Byte, ByVal Boundary As String) As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim startTime As Double
    Dim adoStream As Object
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    Set adoStream = CreateObject("ADODB.Stream")
    
    ' Prepare the ADODB.Stream object with the request body
    adoStream.Type = 1 ' adTypeBinary
    adoStream.Open
    adoStream.Write requestBody
    adoStream.Position = 0 ' Reset position to the beginning of the stream
    
    OCRUtils.LogToFile "DoclingOCR: Sending request to: " & apiURL
    OCRUtils.LogToFile "DoclingOCR: Request body size (from byte array): " & (UBound(requestBody) + 1) & " bytes"
    OCRUtils.LogToFile "DoclingOCR: Request body size (from ADODB.Stream): " & adoStream.Size & " bytes"
    
    Http.Open "POST", apiURL, False
    Http.SetRequestHeader "Content-Type", "multipart/form-data; boundary=" & Boundary
    Http.SetRequestHeader "Accept", "application/json"
    
    startTime = Timer
    Http.Send adoStream ' Send the ADODB.Stream object
    
    ' Wait for response with timeout
    Do While Http.readyState <> 4 And (Timer - startTime) < 300 ' 5 minute timeout
        DoEvents
        WaitSeconds 0.1
    Loop
    
    If Http.readyState = 4 Then
        OCRUtils.LogToFile "DoclingOCR: Response status: " & Http.status
        OCRUtils.LogToFile "DoclingOCR: Processing time: " & Format(Timer - startTime, "0.0") & " seconds"
        
        If Http.status = 200 Then
            SendDoclingRequest = Http.responseText
            OCRUtils.LogToFile "DoclingOCR: Response length: " & Len(Http.responseText) & " characters"
        Else
            OCRUtils.LogToFile "DoclingOCR: ERROR - HTTP Status " & Http.status & ": " & Http.responseText
            SendDoclingRequest = ""
        End If
    Else
        OCRUtils.LogToFile "DoclingOCR: ERROR - Request timeout"
        SendDoclingRequest = ""
    End If
    
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "DoclingOCR: ERROR in SendDoclingRequest: " & Err.Description
    SendDoclingRequest = ""
End Function

' Parse Docling API response
Private Function ParseDoclingResponse(ByVal jsonString As String, ByVal outputFormat As String) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim Result As ocrResult
    Dim apiResponse As Object
    Dim document As Object
    Dim extractedText As String
    
    ' Initialize result
    Result.Success = False
    Result.LetterReference = "Not Found"
    Result.LetterDate = "Not Found"
    Result.Subject = "Not Found"
    Result.References = "Not Found"
    Result.Body = ""
    
    ' Log response for debugging
    OCRUtils.LogToFile "=== DOCLING API RESPONSE START ==="
    OCRUtils.LogToFile "Response length: " & Len(jsonString) & " characters"
    OCRUtils.LogToFile "First 500 chars: " & Left(jsonString, 500)
    OCRUtils.LogToFile "=== DOCLING API RESPONSE END ==="
    
    ' Parse JSON response
    Set apiResponse = JsonConverter.ParseJson(jsonString)
    
    If apiResponse Is Nothing Then
        Result.ErrorMessage = "Failed to parse JSON response"
        OCRUtils.LogToFile "DoclingOCR: ERROR - Failed to parse JSON response"
        GoTo ExitFunction
    End If
    
    ' Check status
    If apiResponse.Exists("status") Then
        If apiResponse("status") <> "success" Then
            Result.ErrorMessage = "Docling API returned error status: " & apiResponse("status")
            If apiResponse.Exists("errors") Then
                Result.ErrorMessage = Result.ErrorMessage & " - " & JsonToString(apiResponse("errors"))
            End If
            OCRUtils.LogToFile "DoclingOCR: ERROR - " & Result.ErrorMessage
            GoTo ExitFunction
        End If
    End If
    
    ' Extract document content
    If apiResponse.Exists("document") Then
        Set document = apiResponse("document")
        
        ' Extract text based on output format
        Select Case LCase(outputFormat)
            Case "text"
                If document.Exists("text_content") And Not IsNull(document("text_content")) Then
                    extractedText = document("text_content")
                End If
            Case "md", "markdown"
                If document.Exists("md_content") And Not IsNull(document("md_content")) Then
                    extractedText = document("md_content")
                End If
            Case "json"
                If document.Exists("json_content") And Not IsNull(document("json_content")) Then
                    extractedText = JsonToString(document("json_content"))
                End If
            Case Else
                Result.ErrorMessage = "Unsupported output format: " & outputFormat
                GoTo ExitFunction
        End Select
        
        If extractedText <> "" Then
            ' For Docling, we get raw OCR text without structured extraction
            ' Set only the Body content - structured fields remain empty
            Result.Body = CleanDoclingText(extractedText)
            Result.Success = True
            
            OCRUtils.LogToFile "DoclingOCR: Successfully extracted " & Len(Result.Body) & " characters of " & outputFormat & " content"
        Else
            Result.ErrorMessage = "No content found in " & outputFormat & " format"
            OCRUtils.LogToFile "DoclingOCR: ERROR - No content found in " & outputFormat & " format"
        End If
    Else
        Result.ErrorMessage = "No document object in response"
        OCRUtils.LogToFile "DoclingOCR: ERROR - No document object in response"
    End If
    
ExitFunction:
    ParseDoclingResponse = Result
    Exit Function
    
ErrorHandler:
    Result.Success = False
    Result.ErrorMessage = "Error parsing Docling response: " & Err.Description
    OCRUtils.LogToFile "DoclingOCR: ERROR - " & Result.ErrorMessage
    ParseDoclingResponse = Result
End Function

' ================================
' UTILITY FUNCTIONS
' ================================

' Read file to byte array
Private Function ReadFileToBytes(ByVal FilePath As String) As Byte()
    On Error GoTo ErrorHandler
    
    Dim FileNum As Integer
    Dim fileBytes() As Byte
    
    FileNum = FreeFile
    Open FilePath For Binary Access Read As FileNum
    
    If LOF(FileNum) > 0 Then
        ReDim fileBytes(LOF(FileNum) - 1)
        Get FileNum, , fileBytes
    Else
        ReDim fileBytes(-1 To -2) ' Empty array
    End If
    
    Close FileNum
    ReadFileToBytes = fileBytes
    Exit Function
    
ErrorHandler:
    If FileNum > 0 Then Close FileNum
    ReDim ReadFileToBytes(-1 To -2) ' Return empty array on error
End Function

' Convert string to UTF-8 byte array
Private Function StringToBytes(ByVal inputString As String) As Byte()
    On Error GoTo ErrorHandler
    
    Dim Stream As Object
    Set Stream = CreateObject("ADODB.Stream")
    
    Stream.Open
    Stream.Type = 2 ' Text
    Stream.Charset = "UTF-8" ' Keep UTF-8 for general purpose, handles ASCII too
    Stream.WriteText inputString
    Stream.Position = 0
    Stream.Type = 1 ' Binary
    
    StringToBytes = Stream.Read
    Stream.Close
    Exit Function
    
ErrorHandler:
    ReDim StringToBytes(-1 To -2) ' Return empty array on error
End Function

' Convert string to system ANSI byte array (safer for multipart headers)
Private Function StringToAsciiBytes(ByVal inputString As String) As Byte()
    On Error GoTo ErrorHandler
    StringToAsciiBytes = StrConv(inputString, vbFromUnicode)
    Exit Function
ErrorHandler:
    ReDim StringToAsciiBytes(-1 To -2)
End Function

' Convert byte array to printable ASCII string for logging
Private Function BytesToPrintableAscii(ByRef bytesArray() As Byte) As String
    On Error GoTo ErrorHandler
    Dim sOutput As String
    Dim i As Long
    Dim charCode As Integer
    
    If UBound(bytesArray) < LBound(bytesArray) Then
        BytesToPrintableAscii = "[EmptyBytes]"
        Exit Function
    End If
    
    For i = LBound(bytesArray) To UBound(bytesArray)
        charCode = bytesArray(i)
        If charCode >= 32 And charCode <= 126 Then ' Printable ASCII
            sOutput = sOutput & Chr(charCode)
        ElseIf charCode = 13 Then
            sOutput = sOutput & "[CR]"
        ElseIf charCode = 10 Then
            sOutput = sOutput & "[LF]"
        Else
            sOutput = sOutput & "[" & Hex(charCode) & "]"
        End If
    Next i
    BytesToPrintableAscii = sOutput
    Exit Function
ErrorHandler:
    BytesToPrintableAscii = "[ErrorInBytesToPrintableAscii]"
End Function


' Extract filename from full path
Private Function ExtractFileNameFromPath(ByVal fullPath As String) As String
    Dim pos As Integer
    pos = InStrRev(fullPath, "\")
    If pos > 0 Then
        ExtractFileNameFromPath = Mid(fullPath, pos + 1)
    Else
        ExtractFileNameFromPath = fullPath
    End If
End Function

' Clean Docling text content
Private Function CleanDoclingText(ByVal rawText As String) As String
    Dim cleanedText As String
    cleanedText = rawText
    
    ' Remove image placeholders
    cleanedText = Replace(cleanedText, "<!-- image -->", "")
    
    ' Clean up excessive line breaks (including those left by removed images)
    Do While InStr(cleanedText, vbCrLf & vbCrLf & vbCrLf) > 0
        cleanedText = Replace(cleanedText, vbCrLf & vbCrLf & vbCrLf, vbCrLf & vbCrLf)
    Loop
    
    ' Clean up double line breaks that might be left after image removal
    Do While InStr(cleanedText, vbCrLf & vbCrLf & vbCrLf) > 0
        cleanedText = Replace(cleanedText, vbCrLf & vbCrLf & vbCrLf, vbCrLf & vbCrLf)
    Loop
    
    ' Trim
    CleanDoclingText = Trim(cleanedText)
End Function

' Convert JSON object to string
Private Function JsonToString(ByVal jsonObj As Object) As String
    On Error Resume Next
    JsonToString = JsonConverter.ConvertToJson(jsonObj)
    If Err.Number <> 0 Then
        JsonToString = "[Complex JSON Object]"
    End If
    On Error GoTo 0
End Function

' Check if file exists
Private Function FileExists(ByVal FilePath As String) As Boolean
    On Error Resume Next
    FileExists = (Dir(FilePath) <> "")
    On Error GoTo 0
End Function

' Wait function
Private Sub WaitSeconds(ByVal seconds As Double)
    Dim endTime As Double
    endTime = Timer + seconds
    Do While Timer < endTime
        DoEvents
    Loop
End Sub

' ================================
' STANDARDIZED PROVIDER INTERFACE
' ================================

' Check if the Docling provider is available (i.e., server is running)
Private Function IsProviderAvailable() As Boolean
    On Error GoTo ErrorHandler
    Dim Http As Object
    Dim doclingHealthUrl As String
    
    ' Construct health URL from the base of DOCLING_API_URL
    ' Assuming DOCLING_API_URL is like "http://localhost:5001/v1alpha/convert/file"
    ' Health URL is typically at the root or /health
    Dim urlParts() As String
    Dim baseUrl As String
    
    urlParts = Split(DOCLING_API_URL, "/")
    If UBound(urlParts) >= 2 Then
        baseUrl = urlParts(0) & "//" & urlParts(2) ' Should give "http://localhost:5001"
        doclingHealthUrl = baseUrl & "/health" ' Common health check endpoint
    Else
        ' Fallback if URL format is unexpected
        IsProviderAvailable = False
        Exit Function
    End If

    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    Http.Open "GET", doclingHealthUrl, False
    Http.SetRequestHeader "Accept", "application/json"
    Http.Send
    
    IsProviderAvailable = (Http.status = 200)
    
    Exit Function
ErrorHandler:
    IsProviderAvailable = False
End Function

' Get a collection of available model names for this provider (output formats for Docling)
Private Function GetAvailableModels() As Collection
    Dim coll As Collection
    Set coll = New Collection
    coll.Add "text"
    coll.Add "markdown"
    coll.Add "json"
    Set GetAvailableModels = coll
End Function

' Get the type of this provider
Private Function GetProviderType() As String
    GetProviderType = "static" ' Docling models (formats) are static
End Function
