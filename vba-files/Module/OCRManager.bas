Attribute VB_Name = "OCRManager"
'
' OCR Manager Module
' Main orchestrator for OCR processing - replaces monolithic VLMOCR
' Handles provider routing, batch processing, and Excel integration
' Author: AI Assistant
'

Option Explicit

' ================================
' EXCEL INTEGRATION
' ================================

' Toggle Excel features for performance
Sub ExcelFeaturesOFF()
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    Application.Calculation = xlCalculationManual
End Sub

Sub ExcelFeaturesON()
    Application.Calculation = xlCalculationAutomatic
    Application.ScreenUpdating = True
    Application.EnableEvents = True
End Sub

' ================================
' MAIN PROCESSING FUNCTIONS
' ================================

' Main batch processing function
Public Sub ProcessPDFOCR(Optional ByVal workflowMode As String = "")
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim currentRow As Long
    Dim pdfPath As String
    Dim Result As ocrResult
    Dim fieldSelection As Object
    Dim lastCol As Long
    
    Call ExcelFeaturesOFF
    
    ' Initialize configuration
    OCRConfig.InitializeOCRConfig
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    ' Special validation for FIELDS_ONLY mode
    If workflowMode = "FIELDS_ONLY" Then
        Dim emptyBodyRows As Collection
        Set emptyBodyRows = New Collection
        
        ' Check all rows for empty Body cells
        For currentRow = 2 To lastRow
            pdfPath = Trim(ws.Cells(currentRow, 1).Value)
            If pdfPath <> "" Then
                Dim bodyContent As String
                bodyContent = Trim(ws.Cells(currentRow, 8).Value) ' Body column
                If bodyContent = "" Then
                    emptyBodyRows.Add currentRow
                End If
            End If
        Next currentRow
        
        ' If any rows have empty Body cells, show error and exit
        If emptyBodyRows.Count > 0 Then
            Dim ErrorMessage As String
            ErrorMessage = "Fields Only extraction requires existing text in the Body column." & vbCrLf & vbCrLf
            
            If emptyBodyRows.Count = 1 Then
                ErrorMessage = ErrorMessage & "Row " & emptyBodyRows(1) & " has an empty Body cell." & vbCrLf & vbCrLf
            Else
                ErrorMessage = ErrorMessage & emptyBodyRows.Count & " rows have empty Body cells." & vbCrLf & vbCrLf
            End If
            
            ErrorMessage = ErrorMessage & "Please run 'OCR Only' or 'OCR + Fields' first to extract text from your PDFs."
            
            Call ExcelFeaturesON
            MessageBoxHelper.ShowMessage ErrorMessage, vbExclamation, "Empty Body Cells Detected"
            Exit Sub
        End If
    End If
    
    ' Get field selection once
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates()
    
    For currentRow = 2 To lastRow
        pdfPath = Trim(ws.Cells(currentRow, 1).Value)
        
        If pdfPath <> "" And OCRUtils.FileExists(pdfPath) Then
            Application.StatusBar = "Processing: " & pdfPath
            
            ' Process the PDF
            Result = ProcessSinglePDF(pdfPath, currentRow, ws, workflowMode) ' Pass all required parameters
            
            ' Populate results
            If Result.Success Then
                lastCol = 1 ' Start with PDF Path column
                
                ' Determine if extraction was performed based on the result fields
                Dim hasExtractedFields As Boolean
                hasExtractedFields = (Result.FromText <> "" Or Result.ToText <> "" Or Result.LetterReference <> "" Or Result.Subject <> "")
                
                If hasExtractedFields Then
                    ' Only write if field is selected AND value is not "Not Found" or empty
                    If fieldSelection("From") And Result.FromText <> "Not Found" And Result.FromText <> "" Then ws.Cells(currentRow, 2).Value = Result.FromText: If 2 > lastCol Then lastCol = 2
                    If fieldSelection("To") And Result.ToText <> "Not Found" And Result.ToText <> "" Then ws.Cells(currentRow, 3).Value = Result.ToText: If 3 > lastCol Then lastCol = 3
                    If fieldSelection("LetterRef") And Result.LetterReference <> "Not Found" And Result.LetterReference <> "" Then ws.Cells(currentRow, 4).Value = Result.LetterReference: If 4 > lastCol Then lastCol = 4
                    If fieldSelection("LetterDate") And Result.LetterDate <> "Not Found" And Result.LetterDate <> "" Then ws.Cells(currentRow, 5).Value = Result.LetterDate: If 5 > lastCol Then lastCol = 5
                    If fieldSelection("Subject") And Result.Subject <> "Not Found" And Result.Subject <> "" Then ws.Cells(currentRow, 6).Value = Result.Subject: If 6 > lastCol Then lastCol = 6
                    If fieldSelection("References") And Result.References <> "Not Found" And Result.References <> "" Then ws.Cells(currentRow, 7).Value = Result.References: If 7 > lastCol Then lastCol = 7
                    If fieldSelection("Body") And Result.Body <> "Not Found" And Result.Body <> "" Then ws.Cells(currentRow, 8).Value = Result.Body: If 8 > lastCol Then lastCol = 8
                    If fieldSelection("Summary") And Result.Summary <> "Not Found" And Result.Summary <> "" Then ws.Cells(currentRow, 9).Value = Result.Summary: If 9 > lastCol Then lastCol = 9
                    If fieldSelection("Tags") And Result.Tags <> "Not Found" And Result.Tags <> "" Then ws.Cells(currentRow, 10).Value = Result.Tags: If 10 > lastCol Then lastCol = 10
                    
                    ' Green highlight for all processed cells up to the last written column
                    If lastCol > 1 Then
                        ws.Range(ws.Cells(currentRow, 2), ws.Cells(currentRow, lastCol)).Interior.Color = RGB(200, 255, 200)
                    End If
                Else ' If not extracting fields, only Body is typically available from basic OCR
                    If fieldSelection("Body") And Result.Body <> "Not Found" And Result.Body <> "" Then ' Check if Body is selected AND has valid content
                        ws.Cells(currentRow, 8).Value = Result.Body ' Assuming Body is at col 8
                        ws.Cells(currentRow, 8).Interior.Color = RGB(200, 255, 200)
                        If 8 > lastCol Then lastCol = 8
                    End If
                End If
            Else
                ' Only use red highlighting for errors, don't write error text to cells
                ' Highlight error across a default range or up to expected standard columns
                ws.Range(ws.Cells(currentRow, 2), ws.Cells(currentRow, 10)).Interior.Color = RGB(255, 200, 200)
                ' Log the error for debugging purposes
                OCRUtils.LogToFile "OCRManager: Processing failed for row " & currentRow & " - " & Result.ErrorMessage
            End If
            
            DoEvents
        End If
    Next currentRow
    
    Application.StatusBar = "Processing complete!"
    MessageBoxHelper.ShowMessage "OCR processing completed successfully!", vbInformation
    Call ExcelFeaturesON
    Exit Sub
    
ErrorHandler:
    Application.StatusBar = ""
    Call ExcelFeaturesON
    MessageBoxHelper.ShowMessage "Error in ProcessPDFOCR: " & Err.Description, vbCritical
End Sub

' Process a single PDF file with OCR and optional field extraction
Public Function ProcessSinglePDF(ByVal pdfPath As String, ByVal currentRow As Long, ByVal ws As Worksheet, Optional ByVal workflowMode As String = "") As ocrResult
    On Error GoTo ErrorHandler
    
    Dim ocrResult As ocrResult
    Dim extractionResult As ocrResult
    Dim extractionEnabled As Boolean
    
    ' Determine extraction based on workflow mode or config
    Select Case workflowMode
        Case "OCR_ONLY"
            extractionEnabled = False
        Case "FIELDS_ONLY"
            extractionEnabled = True
        Case "OCR_AND_FIELDS"
            extractionEnabled = True
        Case Else
            ' Legacy behavior: default to extracting fields
            extractionEnabled = True
    End Select
    
    ' Handle FIELDS_ONLY mode - get text from Excel cell
    If workflowMode = "FIELDS_ONLY" Then
        ' Get existing text from Body column (column 8)
        Dim existingText As String
        existingText = Trim(ws.Cells(currentRow, 8).Value)
        
        If existingText = "" Then
            ocrResult.Success = False
            ocrResult.ErrorMessage = "No text found in Body column for field extraction"
            ProcessSinglePDF = ocrResult
            Exit Function
        End If
        
        ' Create a dummy OCR result with the existing text
        ocrResult.Success = True
        ocrResult.Body = existingText
    Else
        ' STEP 1: Perform OCR for OCR_ONLY and OCR_AND_FIELDS modes
        ocrResult = PerformOCRStep(pdfPath)
    End If
    
    ' Write OCR results to worksheet
    WriteResultsToWorksheet ocrResult, currentRow, ws, extractionEnabled
    
    ' STEP 2: Perform extraction if enabled and OCR succeeded
    If extractionEnabled And ocrResult.Success Then
        ' Get extraction provider and model
        Dim extractionProvider As String
        Dim extractionModel As String
        
        extractionProvider = OCRConfig.GetExtractionProviderID()
        extractionModel = OCRConfig.GetExtractionModel()
        
        ' Perform extraction
        extractionResult = PerformExtractionStep(ocrResult.Body, extractionProvider, extractionModel)
        
        If extractionResult.Success Then
            ' Combine results: structured fields from extraction + raw text from OCR
            extractionResult.Body = ocrResult.Body ' Preserve original OCR text
            ProcessSinglePDF = extractionResult
            OCRUtils.LogToFile "OCRManager: Two-step workflow completed successfully"
        Else
            ' Extraction failed, return OCR result with error note
            ocrResult.ErrorMessage = "OCR succeeded but extraction failed: " & extractionResult.ErrorMessage
            ProcessSinglePDF = ocrResult
            OCRUtils.LogToFile "OCRManager: Extraction step failed, returning OCR result"
        End If
    Else
        ' No extraction requested, return OCR result as-is
        ProcessSinglePDF = ocrResult
        OCRUtils.LogToFile "OCRManager: OCR-only workflow completed successfully"
    End If

    Exit Function
    
ErrorHandler:
    Dim Result As ocrResult
    Result.Success = False
    Result.ErrorMessage = "Error in ProcessSinglePDF: " & Err.Description
    OCRUtils.LogToFile "OCRManager: ERROR - " & Result.ErrorMessage
    ProcessSinglePDF = Result
End Function

' Write OCR results to worksheet using dynamic column mapping
Private Sub WriteResultsToWorksheet(ByRef Result As ocrResult, ByVal currentRow As Long, ByVal ws As Worksheet, ByVal extractionEnabled As Boolean)
    On Error GoTo ErrorHandler
    
    ' Get field selection states (for extraction fields)
    Dim fieldSelection As Object
    Set fieldSelection = OCRConfig.GetAllFieldSelectionStates()
    
    ' Get log field configurations (for column mapping and enabled state)
    Dim logConfig As Object
    Set logConfig = OCRConfig.GetAllLogFieldConfigurations()
    
    Dim writtenColumns As Collection
    Set writtenColumns = New Collection
    
    If Result.Success Then
        ' Write results to worksheet based on field selection and log configuration
        If extractionEnabled Then
            ' Write structured fields if extraction was enabled
            Call WriteFieldToWorksheet(ws, currentRow, Result.FromText, "From", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.ToText, "To", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.LetterReference, "LetterRef", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.LetterDate, "LetterDate", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.Subject, "Subject", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.References, "References", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.Body, "Body", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.Summary, "Summary", fieldSelection, logConfig, writtenColumns)
            Call WriteFieldToWorksheet(ws, currentRow, Result.Tags, "Tags", fieldSelection, logConfig, writtenColumns)
        Else
            ' If not extracting fields, only Body is typically available from basic OCR
            Call WriteFieldToWorksheet(ws, currentRow, Result.Body, "Body", fieldSelection, logConfig, writtenColumns)
        End If
        
        ' Apply green highlighting to all written columns
        Call HighlightWrittenColumns(ws, currentRow, writtenColumns, RGB(200, 255, 200))
        
    Else
        ' Error case: highlight enabled log columns in red
        Call HighlightEnabledLogColumns(ws, currentRow, logConfig, RGB(255, 200, 200))
        ' Log the error for debugging purposes
        OCRUtils.LogToFile "OCRManager: Processing failed for row " & currentRow & " - " & Result.ErrorMessage
    End If
    
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRManager: Error in WriteResultsToWorksheet: " & Err.Description
End Sub

' Helper function to write a single field to the worksheet
Private Sub WriteFieldToWorksheet(ByRef ws As Worksheet, ByVal currentRow As Long, ByVal fieldValue As String, ByVal fieldName As String, ByRef fieldSelection As Object, ByRef logConfig As Object, ByRef writtenColumns As Collection)
    On Error GoTo ErrorHandler
    
    ' Check if field is selected for extraction AND enabled in log configuration
    If fieldSelection.Exists(fieldName) And logConfig.Exists(fieldName) Then
        Dim fieldConfig As Object
        Set fieldConfig = logConfig(fieldName)
        
        If fieldSelection(fieldName) And fieldConfig("enabled") And fieldValue <> "Not Found" And fieldValue <> "" Then
            Dim columnNumber As Integer
            columnNumber = OCRConfig.ColumnLetterToNumber(fieldConfig("column"))
            
            If columnNumber > 0 Then
                ws.Cells(currentRow, columnNumber).Value = fieldValue
                writtenColumns.Add columnNumber
                OCRUtils.LogToFile "OCRManager: Wrote " & fieldName & " to column " & fieldConfig("column") & " (row " & currentRow & ")"
            End If
        End If
    End If
    
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRManager: Error writing field " & fieldName & ": " & Err.Description
End Sub

' Helper function to highlight all written columns
Private Sub HighlightWrittenColumns(ByRef ws As Worksheet, ByVal currentRow As Long, ByRef writtenColumns As Collection, ByVal color As Long)
    On Error GoTo ErrorHandler
    
    Dim i As Integer
    For i = 1 To writtenColumns.Count
        ws.Cells(currentRow, writtenColumns(i)).Interior.color = color
    Next i
    
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRManager: Error highlighting written columns: " & Err.Description
End Sub

' Helper function to highlight all enabled log columns (for error cases)
Private Sub HighlightEnabledLogColumns(ByRef ws As Worksheet, ByVal currentRow As Long, ByRef logConfig As Object, ByVal color As Long)
    On Error GoTo ErrorHandler
    
    Dim fieldName As Variant
    Dim fieldConfig As Object
    Dim columnNumber As Integer
    
    For Each fieldName In logConfig.Keys
        Set fieldConfig = logConfig(fieldName)
        If fieldConfig("enabled") Then
            columnNumber = OCRConfig.ColumnLetterToNumber(fieldConfig("column"))
            If columnNumber > 1 Then ' Skip column A (PDF Path)
                ws.Cells(currentRow, columnNumber).Interior.color = color
            End If
        End If
    Next fieldName
    
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRManager: Error highlighting enabled log columns: " & Err.Description
End Sub

' ================================
' TWO-STEP WORKFLOW IMPLEMENTATION
' ================================

' STEP 1: Perform OCR to extract raw text from PDF
Private Function PerformOCRStep(ByVal pdfPath As String) As ocrResult
    On Error GoTo ErrorHandler

    ' Get OCR configuration from settings
    Dim primaryOCRSource As String
    Dim ocrProvider As String
    Dim ocrModel As String
    
    primaryOCRSource = OCRConfig.GetCurrentPrimaryOCRSourceID()
    ocrProvider = OCRConfig.GetOCRProviderID()
    ocrModel = OCRConfig.GetPrimaryOCRModel()

    OCRUtils.LogToFile "OCRManager: === STEP 1: OCR ==="
    OCRUtils.LogToFile "OCRManager: Source: " & primaryOCRSource & ", Provider: " & ocrProvider & ", Model: " & ocrModel

    ' Route to appropriate OCR provider based on primary source
    Select Case primaryOCRSource
        Case "VLM"
            ' Use VLM provider for direct image-to-text processing
            PerformOCRStep = PerformVLMOCR(pdfPath, ocrProvider, ocrModel)

        Case "Docling", OCRConfig.PID_DOCLING
            ' Use Docling for document parsing (returns raw text)
            ' For unified Docling provider, use the Web transport by default
            PerformOCRStep = PerformDoclingOCR(pdfPath, OCRConfig.PID_DOCLING_WEB)

        Case "OpenRouterOCR"
            ' Use OpenRouter in OCR-only mode
            PerformOCRStep = Prov_OpenRouter.ProcessPDFWithOpenRouter(pdfPath, False) ' Text mode for OCR

        Case Else
            ' Handle direct provider IDs for backward compatibility
            Select Case primaryOCRSource
                Case OCRConfig.PID_OLLAMA
                    PerformOCRStep = PerformVLMOCR(pdfPath, OCRConfig.PID_OLLAMA, ocrModel)
                Case OCRConfig.PID_OPENROUTER
                    PerformOCRStep = PerformVLMOCR(pdfPath, OCRConfig.PID_OPENROUTER, ocrModel)
                Case OCRConfig.PID_LMSTUDIO
                    PerformOCRStep = PerformVLMOCR(pdfPath, OCRConfig.PID_LMSTUDIO, ocrModel)
                Case OCRConfig.PID_OPENROUTER_OCR
                    PerformOCRStep = Prov_OpenRouter.ProcessPDFWithOpenRouter(pdfPath, False)
                Case OCRConfig.PID_DOCLING, OCRConfig.PID_DOCLING_CUSTOM, OCRConfig.PID_DOCLING_WEB
                    PerformOCRStep = PerformDoclingOCR(pdfPath, primaryOCRSource)
                Case Else
                    PerformOCRStep.Success = False
                    PerformOCRStep.ErrorMessage = "Unsupported OCR source: " & primaryOCRSource
            End Select
    End Select

    Exit Function
    
ErrorHandler:
    Dim Result As ocrResult
    Result.Success = False
    Result.ErrorMessage = "Error in PerformOCRStep: " & Err.Description
    OCRUtils.LogToFile "OCRManager: ERROR - " & Result.ErrorMessage
    PerformOCRStep = Result
End Function

' STEP 2: Perform field extraction from raw text
Private Function PerformExtractionStep(ByVal rawText As String, ByVal extractionProvider As String, ByVal extractionModel As String) As ocrResult
    On Error GoTo ErrorHandler

    OCRUtils.LogToFile "OCRManager: === STEP 2: EXTRACTION ==="
    OCRUtils.LogToFile "OCRManager: Provider: " & extractionProvider & ", Model: " & extractionModel
    OCRUtils.LogToFile "OCRManager: Input text length: " & Len(rawText)    ' Create extraction prompt using the new two-step workflow prompt
    Dim extractionPrompt As String
    extractionPrompt = PromptTemplates.BuildFieldExtractionPrompt(rawText)

    ' Route to appropriate extraction provider
    Dim Response As String
    Select Case extractionProvider
        Case OCRConfig.PID_OLLAMA
            Response = Prov_Ollama.SendTextToOllama(extractionPrompt, extractionModel)
            If Response <> "" Then
                Dim cleanedOllamaJson As String
                cleanedOllamaJson = OCRUtils.CleanJsonFromMarkdown(Response)
                
                ' Parse the cleaned JSON directly as OCR fields, not as API response
                Dim ocrDataOllama As Object
                Set ocrDataOllama = JsonConverter.ParseJson(cleanedOllamaJson)
                If Not ocrDataOllama Is Nothing Then
                    Dim fieldSelectionOllama As Object
                    Set fieldSelectionOllama = OCRConfig.GetAllFieldSelectionStates()
                    PerformExtractionStep = ResponseParser.ParseOCRFields(ocrDataOllama, fieldSelectionOllama)
                    PerformExtractionStep.Success = True
                Else
                    PerformExtractionStep.Success = False
                    PerformExtractionStep.ErrorMessage = "Failed to parse JSON from Ollama extraction response"
                End If
            End If

        Case OCRConfig.PID_OPENROUTER
            Response = Prov_OpenRouter.SendTextToOpenRouter(extractionPrompt, extractionModel)
            If Response <> "" Then
                PerformExtractionStep = ResponseParser.ExtractDataFromOpenRouterResponse(Response, False) ' Text mode
            End If

        Case OCRConfig.PID_OPENROUTER_OCR
            Response = Prov_OpenRouter.SendTextToOpenRouter(extractionPrompt, extractionModel)
            If Response <> "" Then
                PerformExtractionStep = ResponseParser.ExtractDataFromOpenRouterResponse(Response, False) ' Text mode
            End If

        Case OCRConfig.PID_LMSTUDIO
            Response = Prov_LMStudio.SendTextToLMStudio(extractionPrompt, extractionModel)
            If Response <> "" Then
                Dim cleanedLMStudioJson As String
                cleanedLMStudioJson = OCRUtils.CleanJsonFromMarkdown(Response)
                OCRUtils.LogToFile "OCRManager: Raw LMStudio response length: " & Len(Response)
                OCRUtils.LogToFile "OCRManager: Cleaned JSON length: " & Len(cleanedLMStudioJson)
                OCRUtils.LogToFile "OCRManager: First 100 chars of cleaned JSON: " & Left(cleanedLMStudioJson, 100)
                
                ' Use the proper response parser for LM Studio
                PerformExtractionStep = ResponseParser.ExtractDataFromLMStudioResponse(cleanedLMStudioJson)
                
                ' If parsing failed, log the error
                If Not PerformExtractionStep.Success Then
                    OCRUtils.LogToFile "OCRManager: Step 2 (Extraction) failed: " & PerformExtractionStep.ErrorMessage
                End If
            Else
                PerformExtractionStep.Success = False
                PerformExtractionStep.ErrorMessage = "Empty response from LM Studio"
                OCRUtils.LogToFile "OCRManager: Empty response from LM Studio"
            End If

        Case Else
            PerformExtractionStep.Success = False
            PerformExtractionStep.ErrorMessage = "Unsupported extraction provider: " & extractionProvider
            Exit Function
    End Select

    If Response = "" Then
        PerformExtractionStep.Success = False
        PerformExtractionStep.ErrorMessage = "No response from extraction provider"
    ElseIf PerformExtractionStep.Success Then
        OCRUtils.LogToFile "OCRManager: Step 2 (Extraction) completed successfully"
    Else
        OCRUtils.LogToFile "OCRManager: Step 2 (Extraction) failed: " & PerformExtractionStep.ErrorMessage
    End If

    Exit Function

ErrorHandler:
    PerformExtractionStep.Success = False
    PerformExtractionStep.ErrorMessage = "Error in PerformExtractionStep: " & Err.Description
    OCRUtils.LogToFile "OCRManager: ERROR in PerformExtractionStep - " & PerformExtractionStep.ErrorMessage
End Function

' Helper: Perform VLM-based OCR (image to text)
Private Function PerformVLMOCR(ByVal pdfPath As String, ByVal vlmProvider As String, ByVal vlmModel As String) As ocrResult
    On Error GoTo ErrorHandler
    OCRUtils.LogToFile "OCRManager: PerformVLMOCR with provider: " & vlmProvider & ", model: " & vlmModel

    ' For VLM OCR, we want raw text extraction (no field parsing)
    ' Use extractionMode = False to get OCR-only response
    Select Case vlmProvider
        Case OCRConfig.PID_OLLAMA
            PerformVLMOCR = Prov_Ollama.ProcessPDFWithOllama(pdfPath, vlmModel, False) ' False = OCR-only mode
        Case OCRConfig.PID_OPENROUTER
            PerformVLMOCR = Prov_OpenRouter.ProcessPDFWithOpenRouter(pdfPath, True) ' Vision mode
        Case OCRConfig.PID_LMSTUDIO
            PerformVLMOCR = Prov_LMStudio.ProcessPDFWithLMStudio(pdfPath, vlmModel, False) ' False = OCR-only mode
        Case Else
            PerformVLMOCR.Success = False
            PerformVLMOCR.ErrorMessage = "Unsupported VLM provider: " & vlmProvider
    End Select
    Exit Function

ErrorHandler:
    PerformVLMOCR.Success = False
    PerformVLMOCR.ErrorMessage = "Error in PerformVLMOCR: " & Err.Description
End Function

' Helper: Perform Docling-based OCR (document parsing)
Private Function PerformDoclingOCR(ByVal pdfPath As String, ByVal doclingTransportID As String) As ocrResult
    On Error GoTo ErrorHandler

    OCRUtils.LogToFile "OCRManager: PerformDoclingOCR with transport: " & doclingTransportID

    Select Case doclingTransportID
        Case OCRConfig.PID_DOCLING
            ' Unified Docling provider uses DoclingWeb implementation
            PerformDoclingOCR = Prov_DoclingOCRWeb.ProcessPDFWithDoclingWeb(pdfPath)
        Case OCRConfig.PID_DOCLING_CUSTOM
            PerformDoclingOCR = Prov_DoclingOCR.ProcessPDFWithDocling(pdfPath:=pdfPath, outputFormat:="text")
        Case OCRConfig.PID_DOCLING_WEB
            PerformDoclingOCR = Prov_DoclingOCRWeb.ProcessPDFWithDoclingWeb(pdfPath)
        Case Else
            PerformDoclingOCR.Success = False
            PerformDoclingOCR.ErrorMessage = "Unsupported Docling transport: " & doclingTransportID
    End Select

    Exit Function

ErrorHandler:
    PerformDoclingOCR.Success = False
    PerformDoclingOCR.ErrorMessage = "Error in PerformDoclingOCR: " & Err.Description
End Function

' ================================
' DOCLING PROCESSING WITH POST-PROCESSING (LEGACY)
' ================================

' Process PDF with Docling and optional VLM post-processing
Private Function ProcessWithDocling(ByVal pdfPath As String, ByVal useVBAWeb As Boolean) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim doclingResult As ocrResult
    Dim finalResult As ocrResult
    
    ' Step 1: Get OCR text from Docling
    If useVBAWeb Then
        doclingResult = Prov_DoclingOCRWeb.ProcessPDFWithDoclingWeb(pdfPath) ' Assuming outputFormat is handled internally or defaults to "text"
    Else
        doclingResult = Prov_DoclingOCR.ProcessPDFWithDocling(pdfPath:=pdfPath, outputFormat:="text")
    End If
    
    If Not doclingResult.Success Then
        ProcessWithDocling = doclingResult
        Exit Function
    End If
    
    ' Step 2: Check if we should do VLM post-processing for field extraction
    ' Always perform VLM post-processing for Docling (controlled by ribbon buttons)
    If True Then ' Extraction controlled by workflow mode, not config setting
        OCRUtils.LogToFile "OCRManager: Docling succeeded, 'Extract Fields' is TRUE. Starting VLM post-processing."
        
        Dim vlmExtractionProviderID As String
        vlmExtractionProviderID = OCRConfig.GetVLMProviderIDForExtraction()
        OCRUtils.LogToFile "OCRManager: VLM Provider for Extraction: " & vlmExtractionProviderID
        
        ' Map provider ID string to APIProviderType enum for ProcessDoclingWithVLM
        Dim vlmExtractionProviderEnum As APIProviderType
        Select Case vlmExtractionProviderID
            Case OCRConfig.PID_OLLAMA
                vlmExtractionProviderEnum = OllamaProvider
            Case OCRConfig.PID_OPENROUTER
                vlmExtractionProviderEnum = OpenRouterProvider
            Case OCRConfig.PID_LMSTUDIO
                vlmExtractionProviderEnum = LMStudioProvider
            Case Else
                OCRUtils.LogToFile "OCRManager: ERROR - Invalid VLM Provider ID for extraction: " & vlmExtractionProviderID
                ProcessWithDocling = doclingResult ' Fallback to raw docling result
                Exit Function
        End Select

        finalResult = ResponseParser.ProcessDoclingWithVLM(doclingResult.Body, vlmExtractionProviderEnum)
        
        If finalResult.Success Then
            ' Use structured fields from VLM but keep Docling body (which has image placeholders)
            finalResult.Body = doclingResult.Body  ' Preserve original Docling body with placeholders
            OCRUtils.LogToFile "OCRManager: VLM post-processing succeeded, combined with Docling body."
        Else
            ' VLM post-processing failed, return Docling result as-is
            finalResult = doclingResult
            OCRUtils.LogToFile "OCRManager: VLM post-processing failed, using raw Docling result"
        End If
    Else
        ' No post-processing requested, return Docling result as-is
        finalResult = doclingResult
        OCRUtils.LogToFile "OCRManager: No VLM post-processing requested, using raw Docling result"
    End If
    
    ProcessWithDocling = finalResult
    Exit Function
    
ErrorHandler:
    ProcessWithDocling.ErrorMessage = "Error in ProcessWithDocling: " & Err.Description
    OCRUtils.LogToFile "OCRManager: ERROR - " & ProcessWithDocling.ErrorMessage
End Function

' ================================
' LM STUDIO INTEGRATION (PLACEHOLDER)
' ================================

Private Function ProcessWithLMStudio(ByVal pdfPath As String) As ocrResult
    ProcessWithLMStudio.Success = False
    ProcessWithLMStudio.ErrorMessage = "LM Studio provider not yet implemented"
    OCRUtils.LogToFile "OCRManager: LM Studio provider not yet implemented"
End Function

' ================================
' POST-PROCESSING CONFIGURATION
' ================================

' Check if VLM post-processing should be used for Docling results (Now driven by OCRConfig.GetExtractFields)
' Private Function ShouldUseVLMPostProcessing() As Boolean
    ' GetExtractFields from OCRConfig now controls this
' End Function

' Get provider to use for post-processing (Now driven by OCRConfig.GetVLMProviderIDForExtraction)
' Private Function GetPostProcessingProvider() As APIProviderType
    ' GetVLMProviderIDForExtraction from OCRConfig now controls this
' End Function

' ================================
' QUICK SETUP FUNCTIONS
' ================================

' Run with Ollama vision model
Sub RunWithOllama()
    OCRConfig.ConfigureForOllama
    ProcessPDFOCR
End Sub

' Run with OpenRouter vision model
Sub RunWithOpenRouter()
    OCRConfig.ConfigureForOpenRouter
    ProcessPDFOCR
End Sub

' Run with OpenRouter OCR
Sub RunWithOpenRouterOCR()
    OCRConfig.ConfigureForOpenRouterOCR
    ProcessPDFOCR
End Sub

' Run with Docling OCR server (custom HTTP)
Sub RunWithDocling()
    OCRConfig.ConfigureForDocling
    ProcessPDFOCR
End Sub

' Run with Docling OCR server (VBA-Web)
Sub RunWithDoclingWeb()
    OCRConfig.ConfigureForDoclingWeb
    ProcessPDFOCR
End Sub

' ================================
' TESTING AND COMPARISON FUNCTIONS
' ================================

' Compare all available OCR methods
Public Sub CompareAllMethods()
    Dim testPath As String
    Dim results() As ocrResult ' Make dynamic
    Dim providerNames() As String ' Make dynamic
    Dim i As Integer
    
    ' Store original settings (using new config structure)
    Dim originalWorkflow As Object
    Set originalWorkflow = OCRConfig.GetCurrentWorkflowSettingsObject()
    
    testPath = "\sample_pdf\BNDP3-ZZ-CSCEC-MMD-LT-00938 Phase 3 ? Additional Cost for Supply of ADDC Energy Meters.pdf"
    
    If Not OCRUtils.FileExists(testPath) Then
        testPath = InputBox("Enter PDF path:", "Select Test PDF", testPath)
        If testPath = "" Or Not OCRUtils.FileExists(testPath) Then
            MessageBoxHelper.ShowMessage "Invalid file path", vbExclamation
            Exit Sub
        End If
    End If
    
    ' Define test configurations based on new provider IDs
    Dim testConfigs As Collection
    Set testConfigs = New Collection
    
    testConfigs.Add CreateTestConfig(OCRConfig.PID_OLLAMA, "Local Ollama")
    testConfigs.Add CreateTestConfig(OCRConfig.PID_OPENROUTER, "OpenRouter (Vision)")
    testConfigs.Add CreateTestConfig(OCRConfig.PID_OPENROUTER_OCR, "OpenRouter OCR")
    testConfigs.Add CreateTestConfig(OCRConfig.PID_DOCLING_CUSTOM, "Docling (Custom HTTP)")
    testConfigs.Add CreateTestConfig(OCRConfig.PID_DOCLING_WEB, "Docling (VBA-Web)")

    ReDim results(1 To testConfigs.Count)
    ReDim providerNames(1 To testConfigs.Count)

    ' Test each configuration
    Dim currentTestConfig As Object
    For i = 1 To testConfigs.Count
        Set currentTestConfig = testConfigs(i)
        providerNames(i) = currentTestConfig("name")
        Debug.Print vbCrLf & "=== Testing Method " & i & ": " & providerNames(i) & " ==="
        
        ' Apply test configuration
        OCRConfig.SetCurrentPrimaryOCRSourceID currentTestConfig("primary_ocr_source_id")
        ' For CompareAllMethods, we want to test specific extraction configurations
        If currentTestConfig.Exists("extraction_provider_id") Then
            OCRConfig.SetExtractionProviderID currentTestConfig("extraction_provider_id")
        Else ' Fallback to ensure it's not using a stale global setting if not specified for test
            OCRConfig.SetExtractionProviderID OCRConfig.GetProviderDefaultModel(currentTestConfig("primary_ocr_source_id")) ' Or a sensible default
        End If
        If currentTestConfig.Exists("extraction_model") Then
            OCRConfig.SetExtractionModel currentTestConfig("extraction_model")
        Else
            OCRConfig.SetExtractionModel OCRConfig.GetProviderDefaultModel(currentTestConfig("extraction_provider_id")) ' Or default for that provider
        End If
        
        If currentTestConfig.Exists("docling_transport_id") Then
            OCRConfig.SetDoclingTransportID currentTestConfig("docling_transport_id")
        End If
        ' For OpenRouter Vision/Text, the ProcessSinglePDF will handle it based on primary_ocr_source_id
        
        ' Create a temporary worksheet for test results if needed
        Dim tempWs As Worksheet
        If Not WorksheetExists("OCR_Test_Results") Then
            Set tempWs = ThisWorkbook.Worksheets.Add
            tempWs.Name = "OCR_Test_Results"
            SetupWorksheetHeaders ' Set up headers on the new worksheet
        Else
            Set tempWs = ThisWorkbook.Worksheets("OCR_Test_Results")
        End If
        
        ' Process with all required parameters
        results(i) = ProcessSinglePDF(testPath, i + 1, tempWs)
    Next i
    
    ' Show comprehensive results
    ShowComparisonResults results, providerNames
    
    ' Restore original settings
    OCRConfig.RestoreWorkflowSettingsObject originalWorkflow
End Sub

' Helper function to create a test configuration dictionary
Private Function CreateTestConfig(primaryOCRSourceID As String, Name As String, Optional vlmExtractionProviderID As String = "", Optional doclingTransportID As String = "") As Object
    Dim dict As Object
    Set dict = CreateObject("Scripting.Dictionary")
    dict("primary_ocr_source_id") = primaryOCRSourceID
    dict("name") = Name
    If vlmExtractionProviderID <> "" Then dict("vlm_provider_id_for_extraction") = vlmExtractionProviderID
    If doclingTransportID <> "" Then dict("docling_transport_id") = doclingTransportID
    Set CreateTestConfig = dict
End Function

' Show comparison results for all methods
Private Sub ShowComparisonResults(ByRef results() As ocrResult, ByRef providerNames() As String)
    Dim msg As String
    Dim i As Integer
    Dim maxLength As Long
    Dim winner As String
    
    msg = "Complete OCR Method Comparison Results" & vbCrLf & vbCrLf
    
    msg = msg & "SUCCESS STATUS:" & vbCrLf
    For i = 1 To UBound(results)
        msg = msg & i & ". " & providerNames(i) & ": " & IIf(results(i).Success, "Success", "Failed") & vbCrLf
    Next i
    
    msg = msg & vbCrLf & "BODY TEXT LENGTH:" & vbCrLf
    For i = 1 To UBound(results)
        If results(i).Success Then
            msg = msg & i & ". " & providerNames(i) & ": " & Len(results(i).Body) & " characters" & vbCrLf
            If Len(results(i).Body) > maxLength Then
                maxLength = Len(results(i).Body)
                winner = providerNames(i)
            End If
        Else
            msg = msg & i & ". " & providerNames(i) & ": Failed" & vbCrLf
        End If
    Next i
    
    If winner <> "" Then
        msg = msg & vbCrLf & "BEST RESULT: " & winner & " (" & maxLength & " characters)"
    End If
    
    MessageBoxHelper.ShowMessage msg, vbInformation, "Complete OCR Comparison"
    
    ' Print detailed results to Immediate window
    Debug.Print vbCrLf & "=== DETAILED COMPARISON ==="
    For i = 1 To UBound(results)
        If results(i).Success Then
            Debug.Print "Method " & i & " (" & providerNames(i) & ") - Reference: " & results(i).LetterReference
            Debug.Print "Method " & i & " (" & providerNames(i) & ") - Date: " & results(i).LetterDate
            Debug.Print "Method " & i & " (" & providerNames(i) & ") - Subject: " & Left(results(i).Subject, 100)
        End If
    Next i
End Sub

' ================================
' CONNECTION TESTING
' ================================

' Test all connections
Public Sub TestAllConnections()
    Dim msg As String
    
    msg = "Connection Test Results:" & vbCrLf & vbCrLf
    
    ' Test Ollama
    On Error Resume Next
    Prov_Ollama.TestOllamaConnection
    If Err.Number = 0 Then
        msg = msg & "? Ollama: Connected" & vbCrLf
    Else
        msg = msg & "? Ollama: Failed (" & Err.Description & ")" & vbCrLf
    End If
    Err.Clear
    
    ' Test OpenRouter
    Prov_OpenRouter.TestOpenRouterConnection
    If Err.Number = 0 Then
        msg = msg & "? OpenRouter: Connected" & vbCrLf
    Else
        msg = msg & "? OpenRouter: Failed (" & Err.Description & ")" & vbCrLf
    End If
    Err.Clear
    
    ' Test Docling
    TestDoclingConnection
    If Err.Number = 0 Then
        msg = msg & "? Docling: Connected" & vbCrLf
    Else
        msg = msg & "? Docling: Failed (" & Err.Description & ")" & vbCrLf
    End If
    Err.Clear
    
    ' Test Ghostscript
    TestGhostscriptInstallation
    If Err.Number = 0 Then
        msg = msg & "? Ghostscript: Available" & vbCrLf
    Else
        msg = msg & "? Ghostscript: Not found (" & Err.Description & ")" & vbCrLf
    End If
    
    On Error GoTo 0
    
    MessageBoxHelper.ShowMessage msg, vbInformation, "Connection Test Results"
End Sub

' Test Docling connection
Public Sub TestDoclingConnection()
    Dim Http As Object
    Dim Response As String
    
    On Error GoTo ErrorHandler
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    Http.Open "GET", "http://localhost:5001/health", False
    Http.Send
    
    If Http.status = 200 Then
        MessageBoxHelper.ShowMessage "Docling server connection successful!" & vbCrLf & _
               "Server is running on localhost:5001", vbInformation
        Debug.Print "Docling server response: " & Http.responseText
    Else
        MessageBoxHelper.ShowMessage "Docling server connection failed. Status: " & Http.status & vbCrLf & _
               "Make sure Docling server is running on localhost:5001", vbCritical
    End If
    
    Exit Sub
    
ErrorHandler:
    MessageBoxHelper.ShowMessage "Error connecting to Docling server: " & Err.Description & vbCrLf & _
           "Make sure Docling server is running on localhost:5001", vbCritical
End Sub

' Test Ghostscript
Public Sub TestGhostscriptInstallation()
    Dim gsPath As String
    gsPath = OCRUtils.GetGhostscriptPath()
    
    If OCRUtils.FileExists(gsPath) Or InStr(gsPath, ":\") = 0 Then
        MessageBoxHelper.ShowMessage "Ghostscript found at: " & gsPath, vbInformation
    Else
        MessageBoxHelper.ShowMessage "Ghostscript not found. Required for Ollama processing.", vbCritical
    End If
End Sub

' ================================
' UTILITY FUNCTIONS
' ================================

' Setup worksheet headers using dynamic log configuration
Public Sub SetupWorksheetHeaders()
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "OCRManager: SetupWorksheetHeaders - Starting dynamic header setup"
    
    ' Use the new dynamic log configuration system
    OCRConfig.ApplyLogConfigurationToWorksheet
    
    ' Get the active worksheet for AutoFit
    Dim ws As Worksheet
    Set ws = ActiveSheet
    
    If Not ws Is Nothing Then
        ' AutoFit all columns that might contain data (A through Z should be sufficient)
        ws.Columns("A:Z").AutoFit
        OCRUtils.LogToFile "OCRManager: Applied AutoFit to columns A:Z"
    End If
    
    MessageBoxHelper.ShowMessage "Headers set up using log configuration. Add PDF paths in column A starting from row 2.", vbInformation
    OCRUtils.LogToFile "OCRManager: SetupWorksheetHeaders - Completed successfully"
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "OCRManager: ERROR in SetupWorksheetHeaders: " & Err.Description
    MessageBoxHelper.ShowMessage "Error setting up headers: " & Err.Description, vbCritical
End Sub

' Get system status
Public Function GetSystemStatus() As String
    Dim status As String
    
    status = "OCR System Status:" & vbCrLf & vbCrLf
    status = status & "Primary OCR Source: " & OCRConfig.GetCurrentPrimaryOCRSourceName() & vbCrLf
    status = status & "Extract Fields: Controlled by ribbon buttons" & vbCrLf
    
    If Left(OCRConfig.GetCurrentPrimaryOCRSourceID(), Len("OpenRouter")) = "OpenRouter" Or OCRConfig.GetCurrentPrimaryOCRSourceID() = OCRConfig.PID_OLLAMA Or OCRConfig.GetCurrentPrimaryOCRSourceID() = OCRConfig.PID_LMSTUDIO Then
        status = status & "VLM for Extraction: " & OCRConfig.GetProviderName(OCRConfig.GetVLMProviderIDForExtraction()) & vbCrLf
        status = status & "VLM Model for Extraction: " & OCRConfig.GetVLMModelForExtraction() & vbCrLf
    End If
    
    If Left(OCRConfig.GetCurrentPrimaryOCRSourceID(), Len("Docling")) = "Docling" Then
        status = status & "Docling Transport: " & OCRConfig.GetProviderName(OCRConfig.GetDoclingTransportID()) & vbCrLf
    End If
    
    GetSystemStatus = status
End Function

' Public Function ProcessPDFOCR(ByVal pdfPath As String) As OCRResult
'     On Error GoTo ErrorHandler
'
'     Dim Result As OCRResult
'     Dim ocrText As String
'     Dim finalResult As OCRResult
'
'     ' STEP 1: OCR - Extract raw text using OCR settings
'     Dim primarySource As String
'     Dim ocrProvider As String
'     Dim ocrModel As String
'
'     primarySource = OCRConfig.GetWorkflowSetting("primary_ocr_source")
'     ocrProvider = OCRConfig.GetWorkflowSetting("ocr_provider")
'     ocrModel = OCRConfig.GetWorkflowSetting("ocr_model")
'
'     OCRUtils.LogToFile "OCRManager: Step 1 - OCR with " & primarySource & " (" & ocrProvider & "/" & ocrModel & ")"
'
'     Select Case primarySource
'         Case "VLM"
'             ' Use VLM provider for direct OCR
'             Result = ProcessWithVLMProvider(pdfPath, ocrProvider, ocrModel, False) ' False = OCR-only mode
'
'         Case "Docling"
'             ' Use Docling for OCR
'             Result = ProcessWithDoclingProvider(pdfPath, ocrProvider)
'
'         Case "OpenRouterOCR"
'             ' Use OpenRouter OCR mode
'             Result = ProcessWithOpenRouterOCR(pdfPath, ocrProvider, ocrModel)
'
'         Case Else
'             Result.Success = False
'             Result.ErrorMessage = "Unknown primary OCR source: " & primarySource
'             ProcessPDFOCR = Result
'             Exit Function
'     End Select
'
'     If Not Result.Success Then
'         OCRUtils.LogToFile "OCRManager: Step 1 failed - " & Result.ErrorMessage
'         ProcessPDFOCR = Result
'         Exit Function
'     End If
'
'     ' STEP 2: Extraction (if enabled)
'     Dim extractionEnabled As Boolean
'     extractionEnabled = CBool(OCRConfig.GetWorkflowSetting("extraction.enabled"))
'
'     If extractionEnabled Then
'         Dim extractionProvider As String
'         Dim extractionModel As String
'
'         extractionProvider = OCRConfig.GetWorkflowSetting("extraction.provider")
'         extractionModel = OCRConfig.GetWorkflowSetting("extraction.model")
'
'         OCRUtils.LogToFile "OCRManager: Step 2 - Extraction with " & extractionProvider & "/" & extractionModel
'
'         ' Get the raw OCR text from Step 1
'         If primarySource = "VLM" Then
'             ocrText = Result.Body ' VLM puts raw text in Body field
'         Else
'             ocrText = Result.Body ' All providers should put raw text in Body
'         End If
'
'         If ocrText <> "" Then
'             ' Send raw OCR text to extraction provider for field parsing
'             finalResult = ProcessFieldExtraction(ocrText, extractionProvider, extractionModel)
'
'             If finalResult.Success Then
'                 OCRUtils.LogToFile "OCRManager: Step 2 completed successfully"
'                 ProcessPDFOCR = finalResult
'             Else
'                 OCRUtils.LogToFile "OCRManager: Step 2 failed - " & finalResult.ErrorMessage
'                 ' Return Step 1 result if Step 2 fails
'                 ProcessPDFOCR = Result
'             End If
'         Else
'             OCRUtils.LogToFile "OCRManager: No OCR text from Step 1, skipping extraction"
'             ProcessPDFOCR = Result
'         End If
'     Else
'         OCRUtils.LogToFile "OCRManager: Extraction disabled, returning OCR-only result"
'         ProcessPDFOCR = Result
'     End If
'
'     Exit Function
'
' ErrorHandler:
'     Result.Success = False
'     Result.ErrorMessage = "Error in ProcessPDFOCR: " & Err.Description
'     OCRUtils.LogToFile "OCRManager: ERROR - " & Result.ErrorMessage
'     ProcessPDFOCR = Result
' End Function

' ' New helper function for Step 2: Field Extraction
' Private Function ProcessFieldExtraction(ByVal ocrText As String, ByVal provider As String, ByVal model As String) As OCRResult
'     On Error GoTo ErrorHandler
'
'     Dim prompt As String
'     Dim response As String
'     Dim Result As OCRResult
'
'     ' Build extraction prompt
'     prompt = PromptTemplates.BuildFieldExtractionPrompt(ocrText)
'
'     ' Send to extraction provider
'     Select Case provider
'         Case OCRConfig.PID_OLLAMA
'             response = Prov_Ollama.SendTextToOllama(prompt, model)
'             Result = ResponseParser.ExtractDataFromOllamaResponse(JsonConverter.ParseJson(response))
'
'         Case OCRConfig.PID_OPENROUTER
'             response = Prov_OpenRouter.SendTextToOpenRouter(prompt, model)
'             Result = ResponseParser.ExtractDataFromOpenRouterResponse(response, False) ' False = text mode
'
'         Case OCRConfig.PID_LMSTUDIO
'             response = Prov_LMStudio.SendTextToLMStudio(prompt, model)
'             Result = ResponseParser.ExtractDataFromLMStudioResponse(JsonConverter.ParseJson(response))
'
'         Case Else
'             Result.Success = False
'             Result.ErrorMessage = "Unsupported extraction provider: " & provider
'     End Select
'
'     ProcessFieldExtraction = Result
'     Exit Function
'
' ErrorHandler:
'     Result.Success = False
'     Result.ErrorMessage = "Error in ProcessFieldExtraction: " & Err.Description
'     ProcessFieldExtraction = Result
' End Function

' ' Helper function for VLM processing with mode control
' Private Function ProcessWithVLMProvider(ByVal pdfPath As String, ByVal provider As String, ByVal model As String, ByVal extractionMode As Boolean) As OCRResult
'     Select Case provider
'         Case OCRConfig.PID_OLLAMA
'             If extractionMode Then
'                 ProcessWithVLMProvider = Prov_Ollama.ProcessPDFWithOllama(pdfPath, model, True) ' True = extraction mode
'             Else
'                 ProcessWithVLMProvider = Prov_Ollama.ProcessPDFWithOllama(pdfPath, model, False) ' False = OCR-only mode
'             End If
'
'         Case OCRConfig.PID_OPENROUTER
'             ProcessWithVLMProvider = Prov_OpenRouter.ProcessPDFWithOpenRouter(pdfPath, True, model) ' Vision mode
'
'         Case OCRConfig.PID_LMSTUDIO
'             ProcessWithVLMProvider = Prov_LMStudio.ProcessPDFWithLMStudio(pdfPath, model)
'
'         Case Else
'             Dim Result As OCRResult
'             Result.Success = False
'             Result.ErrorMessage = "Unsupported VLM provider: " & provider
'             ProcessWithVLMProvider = Result
'     End Select
' End Function

' Helper function to check if a worksheet exists
Private Function WorksheetExists(ByVal wsName As String) As Boolean
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(wsName)
    On Error GoTo 0
    WorksheetExists = Not ws Is Nothing
End Function

