Attribute VB_Name = "MessageBoxHelper"
'
' Message Box Helper Module
' Provides configurable message box functionality with timeout support
' Author: AI Assistant
'

Option Explicit

' Windows API declarations for timed message box
#If VBA7 Then
    Private Declare PtrSafe Function MessageBoxTimeout Lib "user32" Alias "MessageBoxTimeoutA" ( _
        ByVal hwnd As LongPtr, _
        ByVal lpText As String, _
        ByVal lpCaption As String, _
        ByVal uType As Long, _
        ByVal wLanguageId As Long, _
        ByVal dwMilliseconds As Long) As Long
        
    Private Declare PtrSafe Function GetActiveWindow Lib "user32" () As LongPtr
#Else
    Private Declare Function MessageBoxTimeout Lib "user32" Alias "MessageBoxTimeoutA" ( _
        ByVal hwnd As Long, _
        ByVal lpText As String, _
        ByVal lpCaption As String, _
        ByVal uType As Long, _
        ByVal wLanguageId As Long, _
        ByVal dwMilliseconds As Long) As Long
        
    Private Declare Function GetActiveWindow Lib "user32" () As Long
#End If

' Message box timeout result
Private Const MB_TIMEDOUT = 32000

' Configuration cache
Private messageBoxEnabled As Boolean
Private messageBoxTimeoutMS As Long  ' Renamed to avoid conflict with API function
Private configLoaded As Boolean

' Initialize configuration
Private Sub LoadMessageBoxConfig()
    On Error Resume Next
    
    ' Default values
    messageBoxEnabled = True
    messageBoxTimeoutMS = 0 ' 0 means no timeout
    
    ' Try to load from config
    Dim configValue As Variant
    
    ' Check if message boxes are enabled
    configValue = OCRConfig.GetWorkflowSetting("ui.message_boxes_enabled", True)
    If Not IsEmpty(configValue) Then
        messageBoxEnabled = CBool(configValue)
    End If
    
    ' Check timeout setting (in seconds)
    configValue = OCRConfig.GetWorkflowSetting("ui.message_box_timeout", 0)
    If Not IsEmpty(configValue) Then
        messageBoxTimeoutMS = CLng(configValue) * 1000 ' Convert to milliseconds
    End If
    
    configLoaded = True
    On Error GoTo 0
End Sub

' Show a configurable message box
Public Function ShowMessage(ByVal message As String, _
                          Optional ByVal buttons As VbMsgBoxStyle = vbOKOnly, _
                          Optional ByVal title As String = "VBA OCR System") As VbMsgBoxResult
    
    ' Load config if not already loaded
    If Not configLoaded Then LoadMessageBoxConfig
    
    ' If message boxes are disabled, return default OK result
    If Not messageBoxEnabled Then
        ' Log the message instead
        OCRUtils.LogToFile "MESSAGE BOX (Suppressed): " & title & " - " & message
        ShowMessage = vbOK
        Exit Function
    End If
    
    ' If no timeout configured, use standard message box
    If messageBoxTimeoutMS = 0 Then
        ShowMessage = MsgBox(message, buttons, title)
        Exit Function
    End If
    
    ' Use timeout message box
    #If VBA7 Then
        Dim hwnd As LongPtr
    #Else
        Dim hwnd As Long
    #End If
    
    hwnd = GetActiveWindow()
    
    Dim Result As Long
    Result = MessageBoxTimeout(hwnd, message, title, buttons, 0, messageBoxTimeoutMS)
    
    ' Handle timeout
    If Result = MB_TIMEDOUT Then
        OCRUtils.LogToFile "MESSAGE BOX (Timed out after " & (messageBoxTimeoutMS / 1000) & "s): " & title & " - " & message
        ' Return OK for timeout
        ShowMessage = vbOK
    Else
        ShowMessage = Result
    End If
    
End Function

' Enable/disable message boxes programmatically
Public Sub SetMessageBoxEnabled(ByVal enabled As Boolean)
    messageBoxEnabled = enabled
    configLoaded = True
    
    ' Optionally save to config
    On Error Resume Next
    OCRConfig.SetWorkflowSetting "ui.message_boxes_enabled", enabled
    On Error GoTo 0
End Sub

' Set message box timeout (in seconds)
Public Sub SetMessageBoxTimeout(ByVal timeoutSeconds As Long)
    messageBoxTimeoutMS = timeoutSeconds * 1000
    configLoaded = True
    
    ' Optionally save to config
    On Error Resume Next
    OCRConfig.SetWorkflowSetting "ui.message_box_timeout", timeoutSeconds
    On Error GoTo 0
End Sub

' Get current message box enabled state
Public Function IsMessageBoxEnabled() As Boolean
    If Not configLoaded Then LoadMessageBoxConfig
    IsMessageBoxEnabled = messageBoxEnabled
End Function

' Get current timeout in seconds
Public Function GetMessageBoxTimeout() As Long
    If Not configLoaded Then LoadMessageBoxConfig
    GetMessageBoxTimeout = messageBoxTimeoutMS / 1000
End Function

' Quick toggle for debugging
Public Sub ToggleMessageBoxes()
    SetMessageBoxEnabled Not IsMessageBoxEnabled()
    
    If IsMessageBoxEnabled() Then
        ShowMessage "Message boxes are now ENABLED", vbInformation, "Debug Mode"
    Else
        Debug.Print "Message boxes are now DISABLED"
    End If
End Sub

' Test the message box functionality
Public Sub TestMessageBox()
    Dim originalTimeout As Long
    
    ' Save current timeout
    originalTimeout = GetMessageBoxTimeout()
    
    ' Test 1: Normal message box
    ShowMessage "Test 1: Normal message box (no timeout)", vbInformation, "Test"
    
    ' Test 2: 3-second timeout
    SetMessageBoxTimeout 3
    ShowMessage "Test 2: This message will auto-close in 3 seconds", vbInformation, "Timeout Test"
    
    ' Test 3: Disabled message boxes
    SetMessageBoxEnabled False
    ShowMessage "Test 3: You should NOT see this message", vbExclamation, "Disabled Test"
    Debug.Print "Test 3 completed - message was suppressed"
    
    ' Restore settings
    SetMessageBoxEnabled True
    SetMessageBoxTimeout originalTimeout
    
    ShowMessage "Test complete! Check the debug log for suppressed messages.", vbInformation, "Test Complete"
End Sub
