Attribute VB_Name = "Prov_OpenRouter"
'
' OpenRouter Provider Module
' Handles OpenRouter-specific OCR processing (both vision and text modes)
' Author: AI Assistant
'

Option Explicit

' PDF Engine Constants
Private Const PDF_ENGINE_MISTRAL_OCR As String = "mistral-ocr"
Private Const PDF_ENGINE_PDF_TEXT As String = "pdf-text"
Private Const PDF_ENGINE_NATIVE As String = "native"

' ================================
' PUBLIC INTERFACE
' ================================

' Process PDF with OpenRouter (determines mode automatically or via parameter)
Public Function ProcessPDFWithOpenRouter(ByVal pdfPath As String, Optional useVisionModeOverride As Variant, Optional ByVal extractionMode As Boolean = False) As ocrResult
    On Error GoTo ErrorHandler
    Dim useVision As Boolean
    
    ProcessPDFWithOpenRouter.Success = False
    
    If IsMissing(useVisionModeOverride) Then
        ' Determine mode from config if not overridden
        Dim currentProviderID As String
        currentProviderID = OCRConfig.GetCurrentPrimaryOCRSourceID() ' This might be OpenRouterVision or OpenRouterText
        If currentProviderID = OCRConfig.PID_OPENROUTER Then
            useVision = True
        ElseIf currentProviderID = OCRConfig.PID_OPENROUTER_OCR Then
            useVision = False
        Else ' Fallback if called directly without proper primary source set, or for post-processing
            Dim vlmExtractionProviderID As String
            vlmExtractionProviderID = OCRConfig.GetVLMProviderIDForExtraction()
            If vlmExtractionProviderID = OCRConfig.PID_OPENROUTER Then
                useVision = True
            ElseIf vlmExtractionProviderID = OCRConfig.PID_OPENROUTER_OCR Then
                useVision = False
            Else ' Default to vision mode for unified OpenRouter if ambiguous
                useVision = True
            End If
        End If
        OCRUtils.LogToFile "Prov_OpenRouter: Mode determined from config: " & IIf(useVision, "Vision", "Text")
    Else
        useVision = CBool(useVisionModeOverride)
        OCRUtils.LogToFile "Prov_OpenRouter: Mode overridden by parameter: " & IIf(useVision, "Vision", "Text")
    End If
    
    If useVision Then
        ' Vision Mode: Convert PDF to images, send to vision model
        ProcessPDFWithOpenRouter = ProcessPDFWithOpenRouterVision(pdfPath, extractionMode)
    Else
        ' Text Mode: Use PDF text extraction + text model
        ProcessPDFWithOpenRouter = ProcessPDFWithOpenRouterText(pdfPath, extractionMode)
    End If
    
    Exit Function
    
ErrorHandler:
    ProcessPDFWithOpenRouter.ErrorMessage = "Error: " & Err.Description
End Function

' Test all three OpenRouter PDF Engines
Public Sub TestAllOpenRouterPDFEngines(Optional pdfPath As String = "")
    On Error GoTo ErrorHandler
    
    ' If no PDF path provided, ask user to select one
    If pdfPath = "" Then
        pdfPath = Application.GetOpenFilename("PDF Files (*.pdf), *.pdf", , "Select a PDF to test all engines")
        If pdfPath = "False" Then Exit Sub
    End If
    
    Dim originalEngine As String
    originalEngine = OCRConfig.GetOpenRouterPDFEngine()
    
    Dim engines As Variant
    engines = Array("mistral-ocr", "pdf-text", "native")
    
    Dim results As String
    results = "OpenRouter PDF Engine Test Results:" & vbCrLf & vbCrLf
    
    Dim i As Integer
    For i = 0 To UBound(engines)
        Dim currentEngine As String
        currentEngine = engines(i)
        
        results = results & "Testing " & currentEngine & "..." & vbCrLf
        
        ' Set engine and test
        OCRConfig.SetOpenRouterPDFEngine currentEngine
        Dim Result As ocrResult
        Result = ProcessPDFWithOpenRouterText(pdfPath, False)
          If Result.Success Then
            results = results & "✓ SUCCESS - Extracted " & Len(Result.Body) & " characters" & vbCrLf
        Else
            results = results & "✗ FAILED - " & Result.ErrorMessage & vbCrLf
        End If
        results = results & vbCrLf
    Next i
    
    ' Restore original engine
    OCRConfig.SetOpenRouterPDFEngine originalEngine
    
    ' Show results
    MsgBox results, vbInformation, "OpenRouter PDF Engine Tests"
    
    Exit Sub
ErrorHandler:
    OCRConfig.SetOpenRouterPDFEngine originalEngine
    MsgBox "Error during test: " & Err.Description, vbCritical
End Sub

' Test OpenRouter PDF Engine
Public Sub TestOpenRouterPDFEngine(Optional pdfPath As String = "", Optional testEngine As String = "")
    On Error GoTo ErrorHandler
    
    ' If no PDF path provided, ask user to select one
    If pdfPath = "" Then
        pdfPath = Application.GetOpenFilename("PDF Files (*.pdf), *.pdf", , "Select a PDF to test")
        If pdfPath = "False" Then Exit Sub
    End If
    
    ' If no engine specified, get from config
    If testEngine = "" Then
        testEngine = OCRConfig.GetOpenRouterPDFEngine()
    End If
    
    ' Temporarily set the PDF engine
    Dim originalEngine As String
    originalEngine = OCRConfig.GetOpenRouterPDFEngine()
    OCRConfig.SetOpenRouterPDFEngine testEngine
    
    Debug.Print "Testing OpenRouter with PDF engine: " & testEngine
    Debug.Print "PDF Path: " & pdfPath
    
    ' Test the OpenRouter PDF Engine (OpenRouterOCR path)
    Dim Result As ocrResult
    Result = ProcessPDFWithOpenRouterText(pdfPath, False) ' Direct PDF engine test
    
    ' Restore original engine
    OCRConfig.SetOpenRouterPDFEngine originalEngine
      If Result.Success Then
        MsgBox "Test successful with engine: " & testEngine & vbCrLf & vbCrLf & _
               "Body Text (first 500 chars):" & vbCrLf & _
               Left(Result.Body, 500), vbInformation, "OpenRouter PDF Engine Test"
    Else
        MsgBox "Test failed with engine: " & testEngine & vbCrLf & vbCrLf & _
               "Error: " & Result.ErrorMessage, vbCritical, "OpenRouter PDF Engine Test"
    End If
    
    Exit Sub
ErrorHandler:
    OCRConfig.SetOpenRouterPDFEngine originalEngine
    MsgBox "Error during test: " & Err.Description, vbCritical
End Sub

' Test OpenRouter connection
Public Sub TestOpenRouterConnection()
    Dim Http As Object
    Dim Response As String
    Dim apiKey As String
    Dim visionModel As String
    Dim textModel As String
    
    On Error GoTo ErrorHandler
    
    ' Get API key and models from config, using new unified provider IDs
    apiKey = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER)
    If apiKey = "" Then apiKey = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER_OCR)
    
    visionModel = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER)
    textModel = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER_OCR)

    If apiKey = "" Or apiKey = "YOUR_OPENROUTER_API_KEY_HERE" Then
        MsgBox "Please set your OpenRouter API key in config.json or Provider Settings!", vbCritical
        Exit Sub
    End If
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    Http.Open "GET", "https://openrouter.ai/api/v1/models", False
    Http.SetRequestHeader "Authorization", "Bearer " & apiKey
    Http.Send
    
    If Http.status = 200 Then
        MsgBox "OpenRouter connection successful!" & vbCrLf & _
               "Configured Vision Model: " & visionModel & vbCrLf & _
               "Configured Text Model: " & textModel, vbInformation
    Else
        MsgBox "OpenRouter connection failed. Status: " & Http.status, vbCritical
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error: " & Err.Description, vbCritical
End Sub

' ================================
' VISION MODE IMPLEMENTATION
' ================================

' Process PDF with OpenRouter Vision Mode (PDF?Images)
Private Function ProcessPDFWithOpenRouterVision(ByVal pdfPath As String, Optional ByVal extractionMode As Boolean = False) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim tempImagePaths() As String
    Dim base64Images() As String
    Dim pageCount As Integer
    Dim i As Integer
    Dim jsonResponse As String
    
    ProcessPDFWithOpenRouterVision.Success = False
    
    ' Get page count
    pageCount = OCRUtils.GetPDFPageCount(pdfPath)
    If pageCount = 0 Then
        ProcessPDFWithOpenRouterVision.ErrorMessage = "Failed to get PDF page count"
        Exit Function
    End If
    
    ' Convert all pages to images
    ReDim tempImagePaths(1 To pageCount)
    ReDim base64Images(1 To pageCount)
    
    For i = 1 To pageCount
        tempImagePaths(i) = OCRUtils.ConvertPDFPageToImage(pdfPath, i)
        ' Log each converted page image path
        OCRUtils.LogToFile "OpenRouter Vision: Converted page " & i & " to image: " & tempImagePaths(i)
        If tempImagePaths(i) = "" Then
            ProcessPDFWithOpenRouterVision.ErrorMessage = "Failed to convert page " & i
            GoTo Cleanup
        End If
        
        base64Images(i) = OCRUtils.EncodeImageToBase64(tempImagePaths(i))
        If base64Images(i) = "" Then
            ProcessPDFWithOpenRouterVision.ErrorMessage = "Failed to encode page " & i
            GoTo Cleanup
        End If
    Next i
    
    ' Send images to OpenRouter vision model
    jsonResponse = SendImagesToOpenRouterVision(base64Images, pageCount, extractionMode)
    If jsonResponse = "" Then
        ProcessPDFWithOpenRouterVision.ErrorMessage = "Failed to get response from OpenRouter Vision"
        GoTo Cleanup
    End If
    
    ' Parse response based on extraction mode parameter
    If extractionMode Then
        ' Structured extraction mode
        ProcessPDFWithOpenRouterVision = ResponseParser.ExtractDataFromOpenRouterResponse(jsonResponse, True)
        ProcessPDFWithOpenRouterVision.Success = True
        OCRUtils.LogToFile "OpenRouter Vision: Using structured field extraction (Extraction Mode = TRUE)"
    Else
        ' OCR-only mode - extract plain text from response
        ProcessPDFWithOpenRouterVision = ResponseParser.ExtractDataFromOpenRouterPlainTextResponse(jsonResponse, True)
        ProcessPDFWithOpenRouterVision.Success = True
        OCRUtils.LogToFile "OpenRouter Vision: Using plain text OCR mode (Extraction Mode = FALSE)"
    End If
    
Cleanup:
    ' Clean up temporary files
    For i = 1 To pageCount
        If i <= UBound(tempImagePaths) Then
            If OCRUtils.FileExists(tempImagePaths(i)) Then Kill tempImagePaths(i)
        End If
    Next i
    
    Exit Function
    
ErrorHandler:
    ProcessPDFWithOpenRouterVision.ErrorMessage = "Error: " & Err.Description
    GoTo Cleanup
End Function

' Send images to OpenRouter Vision API
Private Function SendImagesToOpenRouterVision(ByRef base64Images() As String, ByVal pageCount As Integer, Optional ByVal extractionMode As Boolean = False) As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim requestBody As String
    Dim prompt As String
    Dim contentArray As String
    Dim i As Integer
    Dim apiKey As String
    Dim visionModelName As String

    apiKey = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER)
    visionModelName = OCRConfig.GetVLMModelForExtraction() ' Get currently selected model for vision
    If visionModelName = "" Or OCRConfig.GetVLMProviderIDForExtraction() <> OCRConfig.PID_OPENROUTER Then
        visionModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER) ' Fallback to default
    End If
    
    If apiKey = "" Or apiKey = "YOUR_OPENROUTER_API_KEY_HERE" Then
        MsgBox "Please set your OpenRouter API key for Vision provider in config.json or Provider Settings!", vbCritical
        SendImagesToOpenRouterVision = ""
        Exit Function
    End If
    If visionModelName = "" Then
        MsgBox "OpenRouter Vision Model not configured!", vbCritical
        SendImagesToOpenRouterVision = ""
        Exit Function
    End If
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    ' Build prompt based on extraction mode
    If extractionMode Then
        prompt = PromptTemplates.GetUniversalOCRExtractionPrompt(pageCount, OCRConfig.GetAllFieldSelectionStates())
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR+EXTRACTION prompt (Extraction Mode = TRUE)"
    Else
        prompt = PromptTemplates.GetUniversalOCRPrompt(pageCount)
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR-ONLY prompt (Extraction Mode = FALSE)"
    End If
    
    ' Build content array with text first, then images
    contentArray = "[{""type"": ""text"", ""text"": """ & OCRUtils.EscapeJsonString(prompt) & """}"
    For i = 1 To pageCount
        contentArray = contentArray & ",{""type"": ""image_url"", ""image_url"": {""url"": ""data:image/png;base64," & base64Images(i) & """}}"
    Next i
    contentArray = contentArray & "]"
    
    requestBody = "{" & _
                 """model"": """ & visionModelName & """," & _
                 """messages"": [{" & _
                 """role"": ""user""," & _
                 """content"": " & contentArray & _
                 "}]," & _
                 """temperature"": 0.1," & _
                 """max_tokens"": 4000" & _
                 "}"
    
    OCRUtils.LogToFile "Converting " & pageCount & " pages to images for OpenRouter Vision"
    Debug.Print "Sending " & pageCount & " images to OpenRouter Vision model: " & visionModelName
    
    ' Get base URL and build full endpoint
    Dim baseUrl As String
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_OPENROUTER)
    If baseUrl = "" Then baseUrl = "https://openrouter.ai" ' Fallback
    
    Dim fullUrl As String
    fullUrl = baseUrl & "/api/v1/chat/completions"

    Http.Open "POST", fullUrl, False
    Http.SetRequestHeader "Authorization", "Bearer " & apiKey
    Http.SetRequestHeader "Content-Type", "application/json"
    Http.SetRequestHeader "HTTP-Referer", "https://github.com/construction-ocr/excel-vba"
    Http.SetRequestHeader "X-Title", "Construction Letter OCR"
    
    Http.Send requestBody
    
    ' Wait for response
    Dim startTime As Single
    startTime = Timer
    
    Do While Http.readyState <> 4 And (Timer - startTime) < 180
        DoEvents
        OCRUtils.WaitSeconds 0.1
    Loop
    
    If Http.readyState = 4 Then
        Debug.Print "OpenRouter Vision Response Status: " & Http.status
        
        ' Log the full API response for debugging
        OCRUtils.LogToFile "=== FULL OPENROUTER VISION API RESPONSE START ==="
        OCRUtils.LogToFile "Status: " & Http.status
        OCRUtils.LogToFile "Response Text: " & Http.responseText
        OCRUtils.LogToFile "=== FULL OPENROUTER VISION API RESPONSE END ==="
        
        If Http.status = 200 Then
            SendImagesToOpenRouterVision = Http.responseText
        Else
            Debug.Print "OpenRouter Vision Error: " & Http.responseText
            OCRUtils.ParseOpenRouterError Http.responseText
            SendImagesToOpenRouterVision = ""
        End If
    Else
        Debug.Print "Request timeout"
        SendImagesToOpenRouterVision = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "Error in SendImagesToOpenRouterVision: " & Err.Description
    SendImagesToOpenRouterVision = ""
End Function

' ================================
' TEXT MODE IMPLEMENTATION
' ================================

' Process PDF with OpenRouter Text Mode (PDF?Text Engine)
Private Function ProcessPDFWithOpenRouterText(ByVal pdfPath As String, Optional ByVal extractionMode As Boolean = False) As ocrResult
    On Error GoTo ErrorHandler
    
    Dim base64PDF As String
    Dim jsonResponse As String
    
    ProcessPDFWithOpenRouterText.Success = False
    
    ' Encode entire PDF to Base64
    base64PDF = OCRUtils.EncodePDFToBase64(pdfPath)
    If base64PDF = "" Then
        ProcessPDFWithOpenRouterText.ErrorMessage = "Failed to encode PDF"
        Exit Function
    End If
    
    ' Send PDF to OpenRouter text model with PDF engine
    jsonResponse = SendPDFToOpenRouterText(base64PDF, extractionMode, "")
    If jsonResponse = "" Then
        ProcessPDFWithOpenRouterText.ErrorMessage = "Failed to get response from OpenRouter Text"
        Exit Function
    End If
    
    ' Parse response based on extraction mode parameter
    If extractionMode Then
        ' Structured extraction mode
        ProcessPDFWithOpenRouterText = ResponseParser.ExtractDataFromOpenRouterResponse(jsonResponse, False)
        ProcessPDFWithOpenRouterText.Success = True
        OCRUtils.LogToFile "OpenRouter Text: Using structured field extraction (Extraction Mode = TRUE)"
    Else
        ' OCR-only mode - extract plain text from response
        ProcessPDFWithOpenRouterText = ResponseParser.ExtractDataFromOpenRouterPlainTextResponse(jsonResponse, False)
        ProcessPDFWithOpenRouterText.Success = True
        OCRUtils.LogToFile "OpenRouter Text: Using plain text OCR mode (Extraction Mode = FALSE)"
    End If
    
    Exit Function
    
ErrorHandler:
    ProcessPDFWithOpenRouterText.ErrorMessage = "Error: " & Err.Description
End Function

' Send PDF to OpenRouter Text API with PDF engine
Private Function SendPDFToOpenRouterText(ByVal base64PDF As String, Optional ByVal extractionMode As Boolean = False, Optional ByVal pdfEngine As String = "") As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim requestBody As String
    Dim prompt As String
    Dim contentArray As String
    Dim apiKey As String
    Dim textModelName As String

    apiKey = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER_OCR)    ' Get PDF engine from config if not specified
    If pdfEngine = "" Then
        pdfEngine = OCRConfig.GetProviderDetail(OCRConfig.PID_OPENROUTER_OCR, "pdf_engine", PDF_ENGINE_PDF_TEXT)
    End If    ' Get model name - required for all engines (including mistral-ocr)
    textModelName = OCRConfig.GetOpenRouterPDFModel()
    
    ' If no PDF model configured, try extraction model as fallback
    If textModelName = "" Then
        textModelName = OCRConfig.GetVLMModelForExtraction()
        If textModelName = "" Or OCRConfig.GetVLMProviderIDForExtraction() <> OCRConfig.PID_OPENROUTER_OCR Then
            textModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER_OCR)
        End If
    End If
    If apiKey = "" Or apiKey = "YOUR_OPENROUTER_API_KEY_HERE" Then
        MsgBox "Please set your OpenRouter API key for Text provider in config.json or Provider Settings!", vbCritical
        SendPDFToOpenRouterText = ""
        Exit Function
    End If
    ' Model is required for all engines (including mistral-ocr for text processing)
    If textModelName = "" Then
        MsgBox "OpenRouter Text Model not configured for engine: " & pdfEngine, vbCritical
        SendPDFToOpenRouterText = ""
        Exit Function
    End If

    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")

    ' Build prompt based on extraction mode
    If extractionMode Then
        prompt = PromptTemplates.GetUniversalOCRExtractionPrompt(1, OCRConfig.GetAllFieldSelectionStates()) ' PDF, so pageCount = 1
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR+EXTRACTION prompt (Extraction Mode = TRUE)"
    Else
        prompt = PromptTemplates.GetUniversalOCRPrompt(1) ' PDF, so pageCount = 1
        OCRUtils.LogToFile "PromptTemplates: Using UNIVERSAL OCR-ONLY prompt (Extraction Mode = FALSE)"
    End If
      ' Build content array with text first, then PDF (using correct OpenRouter format)
    contentArray = "[" & _
                  "{""type"": ""text"", ""text"": """ & OCRUtils.EscapeJsonString(prompt) & """}," & _
                  "{""type"": ""file"", ""file"": {""filename"": ""document.pdf"", ""file_data"": ""data:application/pdf;base64," & base64PDF & """}}" & _
                  "]"
    
    ' Build request body based on PDF engine
    Select Case pdfEngine
        Case PDF_ENGINE_MISTRAL_OCR
            ' mistral-ocr: Uses plugin for PDF processing, but still needs a model for text processing
            OCRUtils.LogToFile "OpenRouter: Using mistral-ocr engine with model: " & textModelName
            requestBody = "{" & _
                         """model"": """ & textModelName & """," & _
                         """messages"": [{" & _
                         """role"": ""user""," & _
                         """content"": " & contentArray & _
                         "}]," & _
                         """plugins"": [{""id"": ""file-parser"", ""pdf"": {""engine"": ""mistral-ocr""}}]," & _
                         """temperature"": 0.1," & _
                         """max_tokens"": 8000" & _
                         "}"
            OCRUtils.LogToFile "OpenRouter: mistral-ocr request body prepared (WITH model field for text processing)"
                         
        Case PDF_ENGINE_PDF_TEXT
            ' pdf-text: requires model, pdf-text plugin
            requestBody = "{" & _
                         """model"": """ & textModelName & """," & _
                         """messages"": [{" & _
                         """role"": ""user""," & _
                         """content"": " & contentArray & _
                         "}]," & _
                         """plugins"": [{""id"": ""file-parser"", ""pdf"": {""engine"": ""pdf-text""}}]," & _
                         """temperature"": 0.1," & _
                         """max_tokens"": 8000" & _
                         "}"
                         
        Case PDF_ENGINE_NATIVE
            ' native: requires model with native file support, no plugins
            requestBody = "{" & _
                         """model"": """ & textModelName & """," & _
                         """messages"": [{" & _
                         """role"": ""user""," & _
                         """content"": " & contentArray & _
                         "}]," & _
                         """temperature"": 0.1," & _
                         """max_tokens"": 8000" & _
                         "}"
                         
        Case Else
            ' Default to pdf-text
            requestBody = "{" & _
                         """model"": """ & textModelName & """," & _
                         """messages"": [{" & _
                         """role"": ""user""," & _
                         """content"": " & contentArray & _
                         "}]," & _
                         """plugins"": [{""id"": ""file-parser"", ""pdf"": {""engine"": ""pdf-text""}}]," & _
                         """temperature"": 0.1," & _
                         """max_tokens"": 8000" & _
                         "}"
    End Select
    
    Debug.Print "Sending PDF to OpenRouter with engine: " & pdfEngine & IIf(textModelName <> "", " and model: " & textModelName, "")
    OCRUtils.LogToFile "=== OpenRouter PDF Request DEBUG START ==="
    OCRUtils.LogToFile "Engine: " & pdfEngine
    OCRUtils.LogToFile "Model: " & textModelName & " (required for all engines)"
    OCRUtils.LogToFile "Request Body: " & Left(requestBody, 500) & "..." ' Log first 500 chars
    OCRUtils.LogToFile "=== OpenRouter PDF Request DEBUG END ==="
    
    ' Get base URL and build full endpoint
    Dim baseUrl As String
    baseUrl = OCRConfig.GetProviderAPIURL(OCRConfig.PID_OPENROUTER_OCR)
    If baseUrl = "" Then baseUrl = "https://openrouter.ai" ' Fallback
    
    Dim fullUrl As String
    fullUrl = baseUrl & "/api/v1/chat/completions"

    Http.Open "POST", fullUrl, False
    Http.SetRequestHeader "Authorization", "Bearer " & apiKey
    Http.SetRequestHeader "Content-Type", "application/json"
    Http.SetRequestHeader "HTTP-Referer", "https://github.com/construction-ocr/excel-vba"
    Http.SetRequestHeader "X-Title", "Construction Letter OCR"
    
    Http.Send requestBody
    
    ' Wait for response
    Dim startTime As Single
    startTime = Timer
    
    Do While Http.readyState <> 4 And (Timer - startTime) < 180
        DoEvents
        OCRUtils.WaitSeconds 0.1
    Loop
      If Http.readyState = 4 Then
        OCRUtils.LogToFile "OpenRouter PDF Response Status: " & Http.status
        OCRUtils.LogToFile "OpenRouter PDF Response Text: " & Http.responseText
        Debug.Print "OpenRouter Text Response Status: " & Http.status
        If Http.status = 200 Then
            SendPDFToOpenRouterText = Http.responseText
        Else
            Debug.Print "OpenRouter Text Error: " & Http.responseText
            OCRUtils.LogToFile "OpenRouter PDF Error Response: " & Http.responseText
            OCRUtils.ParseOpenRouterError Http.responseText
            SendPDFToOpenRouterText = ""
        End If
    Else
        Debug.Print "Request timeout"
        SendPDFToOpenRouterText = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "Error in SendPDFToOpenRouterText: " & Err.Description
    SendPDFToOpenRouterText = ""
End Function

' ================================
' TEXT-ONLY PROCESSING (FOR DOCLING POST-PROCESSING)
' ================================

' Send text to OpenRouter for post-processing
Public Function SendTextToOpenRouter(ByVal prompt As String, Optional ByVal modelName As String = "") As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim requestBody As String
    Dim apiKey As String
    Dim targetModel As String
    Dim configuredExtractionProviderID As String
    Dim apiURL As String

    configuredExtractionProviderID = OCRConfig.GetExtractionProviderID()
    
    ' Determine API Key and URL based on the configured extraction provider
    apiKey = OCRConfig.GetProviderAPIKey(configuredExtractionProviderID)
    
    ' Get base URL and build full endpoint
    Dim baseUrl As String
    baseUrl = OCRConfig.GetProviderAPIURL(configuredExtractionProviderID)
    If baseUrl = "" Then baseUrl = "https://openrouter.ai" ' Default OpenRouter base URL
    apiURL = baseUrl & "/api/v1/chat/completions"

    ' Determine the model to use
    If modelName <> "" Then
        targetModel = modelName
        OCRUtils.LogToFile "Prov_OpenRouter.SendTextToOpenRouter: Using specified model for extraction: '" & targetModel & "'"
    Else
        targetModel = OCRConfig.GetExtractionModel()
        If targetModel = "" Then
            targetModel = OCRConfig.GetProviderDefaultModel(configuredExtractionProviderID)
            OCRUtils.LogToFile "Prov_OpenRouter.SendTextToOpenRouter: Extraction model not set, using default for provider " & configuredExtractionProviderID & ": '" & targetModel & "'"
        Else
            OCRUtils.LogToFile "Prov_OpenRouter.SendTextToOpenRouter: Using configured extraction model: '" & targetModel & "'"
        End If
    End If

    If apiKey = "" Or apiKey = "YOUR_OPENROUTER_API_KEY_HERE" Then
        MsgBox "Please set your OpenRouter API key for provider '" & configuredExtractionProviderID & "' in config or Settings!", vbCritical
        SendTextToOpenRouter = ""
        Exit Function
    End If
    If targetModel = "" Then
        MsgBox "OpenRouter model for extraction (provider: " & configuredExtractionProviderID & ") not configured or determined!", vbCritical
        SendTextToOpenRouter = ""
        Exit Function
    End If
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    requestBody = "{" & _
                 """model"": """ & targetModel & """," & _
                 """messages"": [{" & _
                 """role"": ""user""," & _
                 """content"": """ & OCRUtils.EscapeJsonString(prompt) & """" & _
                 "}]," & _
                 """temperature"": 0.1," & _
                 """max_tokens"": 4000" & _
                 "}"
    
    OCRUtils.LogToFile "OpenRouter: Sending text-only request for post-processing to model: " & targetModel & " via provider " & configuredExtractionProviderID
    
    Http.Open "POST", apiURL, False
    Http.SetRequestHeader "Authorization", "Bearer " & apiKey
    Http.SetRequestHeader "Content-Type", "application/json"
    Http.SetRequestHeader "HTTP-Referer", "https://github.com/construction-ocr/excel-vba"
    Http.SetRequestHeader "X-Title", "Construction Letter OCR Post-Processing"
    
    Http.Send requestBody
    
    ' Wait for response
    Dim startTime As Single
    startTime = Timer
    
    Do While Http.readyState <> 4 And (Timer - startTime) < 120
        DoEvents
        OCRUtils.WaitSeconds 0.1
    Loop
    
    If Http.readyState = 4 Then
        If Http.status = 200 Then
            SendTextToOpenRouter = Http.responseText
            OCRUtils.LogToFile "OpenRouter: Successfully received text processing response"
        Else
            SendTextToOpenRouter = ""
            OCRUtils.LogToFile "OpenRouter: Text request failed - Status: " & Http.status
            OCRUtils.ParseOpenRouterError Http.responseText
        End If
    Else
        SendTextToOpenRouter = ""
        OCRUtils.LogToFile "OpenRouter: Text request timeout"
    End If
    
    Exit Function
    
ErrorHandler:
    SendTextToOpenRouter = ""
    OCRUtils.LogToFile "OpenRouter: Error in SendTextToOpenRouter: " & Err.Description
End Function

' ================================
' UTILITY FUNCTIONS
' ================================

' Get OpenRouter model capabilities
Public Function GetOpenRouterModels() As String
    On Error GoTo ErrorHandler
    
    Dim Http As Object
    Dim apiKey As String
    
    apiKey = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER) ' Use unified or OCR key, should be same
    If apiKey = "" Then apiKey = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER_OCR)

    If apiKey = "" Or apiKey = "YOUR_OPENROUTER_API_KEY_HERE" Then
        GetOpenRouterModels = ""
        Exit Function
    End If
    
    Set Http = CreateObject("MSXML2.XMLHTTP.6.0")
    
    Http.Open "GET", "https://openrouter.ai/api/v1/models", False
    Http.SetRequestHeader "Authorization", "Bearer " & apiKey
    Http.Send
    
    If Http.status = 200 Then
        GetOpenRouterModels = Http.responseText
    Else
        GetOpenRouterModels = ""
    End If
    
    Exit Function
    
ErrorHandler:
    GetOpenRouterModels = ""
End Function

' Check if vision model is available
Private Function IsVisionModelAvailable() As Boolean
    Dim models As String
    models = GetOpenRouterModels()
    Dim visionModelName As String
    visionModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER)
    ' More robust: check against the currently selected model if applicable
    If OCRConfig.GetVLMProviderIDForExtraction() = OCRConfig.PID_OPENROUTER Then
        visionModelName = OCRConfig.GetVLMModelForExtraction()
    End If
    If visionModelName = "" Then visionModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER)

    IsVisionModelAvailable = (InStr(models, visionModelName) > 0)
End Function

' Check if text model is available
Public Function IsTextModelAvailable() As Boolean
    Dim models As String
    models = GetOpenRouterModels()
    Dim textModelName As String
    textModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER_OCR)
    ' More robust: check against the currently selected model if applicable
    If OCRConfig.GetVLMProviderIDForExtraction() = OCRConfig.PID_OPENROUTER_OCR Then
        textModelName = OCRConfig.GetVLMModelForExtraction()
    End If
    If textModelName = "" Then textModelName = OCRConfig.GetProviderDefaultModel(OCRConfig.PID_OPENROUTER_OCR)
    
    IsTextModelAvailable = (InStr(models, textModelName) > 0)
End Function

' ================================
' STANDARDIZED PROVIDER INTERFACE
' ================================

' Check if the OpenRouter provider is available (e.g., API key is configured)
Private Function IsProviderAvailable() As Boolean
    Dim apiKeyVision As String
    Dim apiKeyText As String
    apiKeyVision = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER)
    apiKeyText = OCRConfig.GetProviderAPIKey(OCRConfig.PID_OPENROUTER_OCR)
    
    ' Considered available if at least one of its configurations has an API key
    IsProviderAvailable = (apiKeyVision <> "" And apiKeyVision <> "YOUR_OPENROUTER_API_KEY_HERE") Or _
                          (apiKeyText <> "" And apiKeyText <> "YOUR_OPENROUTER_API_KEY_HERE")
End Function

' Check if a model supports file input by checking config file
Public Function IsFileCompatibleModel(modelName As String) As Boolean
    ' Get file-compatible models from config
    Dim compatibleModels As Variant
    compatibleModels = OCRConfig.GetOpenRouterFileCompatibleModels()
    
    IsFileCompatibleModel = False
    
    If IsArray(compatibleModels) Then
        Dim model As Variant
        For Each model In compatibleModels
            If CStr(model) = modelName Then
                IsFileCompatibleModel = True
                Exit Function
            End If
        Next model
    End If
End Function

' Get file-compatible models from available models
Public Function GetFileCompatibleModels() As Collection
    Dim compatibleModels As Collection
    Set compatibleModels = New Collection
    
    ' Get file-compatible models directly from config
    Dim configModels As Variant
    configModels = OCRConfig.GetOpenRouterFileCompatibleModels()
    
    If IsArray(configModels) Then
        Dim model As Variant
        For Each model In configModels
            compatibleModels.Add CStr(model)
        Next model
    End If
    
    ' If no models found in config, fall back to checking all available models
    If compatibleModels.Count = 0 Then
        Dim allModels As Variant
        allModels = OCRConfig.GetProviderModels(OCRConfig.PID_OPENROUTER_OCR)
        
        If IsArray(allModels) Then
            For Each model In allModels
                If IsFileCompatibleModel(CStr(model)) Then
                    compatibleModels.Add CStr(model)
                End If
            Next model
        End If
    End If
    
    Set GetFileCompatibleModels = compatibleModels
End Function

' Get a collection of available model names for this provider from config.json
Private Function GetAvailableModels() As Collection
    Dim modelsArray As Variant
    Dim modelName As Variant
    Dim coll As Collection
    Set coll = New Collection
    
    ' Both PID_OPENROUTER and PID_OPENROUTER_OCR use the same OpenRouter API and models.
    ' We'll return models from whichever provider has them configured.
    ' Priority: PID_OPENROUTER_OCR first (since it's the primary OCR provider), then PID_OPENROUTER
    
    ' Try OpenRouterOCR first
    modelsArray = OCRConfig.GetProviderModels(OCRConfig.PID_OPENROUTER_OCR)
    If IsArray(modelsArray) And UBound(modelsArray) >= 0 Then
        ' Check if it has actual models (not just placeholder)
        If UBound(modelsArray) = 0 And InStr(CStr(modelsArray(0)), "not available") > 0 Then
            ' Has placeholder, try the other provider
            modelsArray = OCRConfig.GetProviderModels(OCRConfig.PID_OPENROUTER)
            OCRUtils.LogToFile "GetAvailableModels: OpenRouterOCR has placeholder, using OpenRouter models"
        Else
            OCRUtils.LogToFile "GetAvailableModels: Using OpenRouterOCR models"
        End If
    Else
        ' Try regular OpenRouter
        modelsArray = OCRConfig.GetProviderModels(OCRConfig.PID_OPENROUTER)
        OCRUtils.LogToFile "GetAvailableModels: OpenRouterOCR empty, using OpenRouter models"
    End If
    
    If IsArray(modelsArray) Then
        For Each modelName In modelsArray
            If CStr(modelName) <> "" Then coll.Add CStr(modelName)
        Next modelName
        OCRUtils.LogToFile "GetAvailableModels: Returning " & coll.Count & " models"
    Else
        OCRUtils.LogToFile "GetAvailableModels: No models found in either provider"
    End If
    
    Set GetAvailableModels = coll
End Function

' Get the type of this provider
Private Function GetProviderType() As String
    GetProviderType = "cloud_api" ' OpenRouter is a cloud API provider
End Function
