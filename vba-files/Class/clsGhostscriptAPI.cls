VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
END
Attribute VB_Name = "clsGhostscriptAPI"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'
' clsGhostscriptAPI - Ghostscript DLL API Class
' Provides direct API access to Ghostscript DLL to eliminate command-line screen flashing
' Refactored from GhostscriptAPI module for better encapsulation and resource management
' Author: AI Assistant
'

Option Explicit

' ================================
' API DECLARATIONS
' ================================

' Ghostscript API constants
Private Const GS_ERROR_OK As Long = 0
Private Const GS_ERROR_UNKNOWNERROR As Long = -1
Private Const GS_ERROR_DICTFULL As Long = -2
Private Const GS_ERROR_DICTSTACKOVERFLOW As Long = -3
Private Const GS_ERROR_DICTSTACKUNDERFLOW As Long = -4
Private Const GS_ERROR_EXECSTACKOVERFLOW As Long = -5
Private Const GS_ERROR_INTERRUPT As Long = -6
Private Const GS_ERROR_INVALIDACCESS As Long = -7
Private Const GS_ERROR_INVALIDEXIT As Long = -8
Private Const GS_ERROR_INVALIDFILEACCESS As Long = -9
Private Const GS_ERROR_INVALIDFONT As Long = -10
Private Const GS_ERROR_INVALIDRESTORE As Long = -11
Private Const GS_ERROR_IOERROR As Long = -12
Private Const GS_ERROR_LIMITCHECK As Long = -13
Private Const GS_ERROR_NOCURRENTPOINT As Long = -14
Private Const GS_ERROR_RANGECHECK As Long = -15
Private Const GS_ERROR_STACKOVERFLOW As Long = -16
Private Const GS_ERROR_STACKUNDERFLOW As Long = -17
Private Const GS_ERROR_SYNTAXERROR As Long = -18
Private Const GS_ERROR_TIMEOUT As Long = -19
Private Const GS_ERROR_TYPECHECK As Long = -20
Private Const GS_ERROR_UNDEFINED As Long = -21
Private Const GS_ERROR_UNDEFINEDFILENAME As Long = -22
Private Const GS_ERROR_UNDEFINEDRESULT As Long = -23
Private Const GS_ERROR_UNMATCHEDMARK As Long = -24
Private Const GS_ERROR_VMERROR As Long = -25
Private Const GS_ERROR_CONFIGURATIONERROR As Long = -26
Private Const GS_ERROR_UNDEFINEDRESOURCE As Long = -27
Private Const GS_ERROR_UNREGISTERED As Long = -28
Private Const GS_ERROR_INVALIDCONTEXT As Long = -29
Private Const GS_ERROR_INVALIDID As Long = -30
Private Const GS_ERROR_HIT As Long = -99
Private Const GS_ERROR_FATAL As Long = -100

#If VBA7 Then
    ' 64-bit VBA declarations
    Private Declare PtrSafe Function gsapi_revision Lib "gsdll64.dll" ( _
        ByRef pRevision As Any, _
        ByVal Length As Long) As Long
        
    Private Declare PtrSafe Function gsapi_new_instance Lib "gsdll64.dll" ( _
        ByRef pInstance As LongPtr, _
        ByVal caller_handle As LongPtr) As Long
        
    Private Declare PtrSafe Function gsapi_delete_instance Lib "gsdll64.dll" ( _
        ByVal Instance As LongPtr) As Long
        
    Private Declare PtrSafe Function gsapi_set_stdio Lib "gsdll64.dll" ( _
        ByVal Instance As LongPtr, _
        ByVal stdin_fn As LongPtr, _
        ByVal stdout_fn As LongPtr, _
        ByVal stderr_fn As LongPtr) As Long
        
    Private Declare PtrSafe Function gsapi_init_with_args Lib "gsdll64.dll" ( _
        ByVal Instance As LongPtr, _
        ByVal argc As Long, _
        ByVal argv As LongPtr) As Long
        
    Private Declare PtrSafe Function gsapi_exit Lib "gsdll64.dll" ( _
        ByVal Instance As LongPtr) As Long
        
    ' Memory management
    Private Declare PtrSafe Function GlobalAlloc Lib "kernel32" ( _
        ByVal uFlags As Long, _
        ByVal dwBytes As LongPtr) As LongPtr
        
    Private Declare PtrSafe Function GlobalFree Lib "kernel32" ( _
        ByVal hMem As LongPtr) As LongPtr
        
    Private Declare PtrSafe Function GlobalLock Lib "kernel32" ( _
        ByVal hMem As LongPtr) As LongPtr
        
    Private Declare PtrSafe Function GlobalUnlock Lib "kernel32" ( _
        ByVal hMem As LongPtr) As Long
        
    Private Declare PtrSafe Sub CopyMemory Lib "kernel32" Alias "RtlMoveMemory" ( _
        ByVal Destination As LongPtr, _
        ByVal Source As LongPtr, _
        ByVal Length As LongPtr)
        
    Private Declare PtrSafe Function lstrlen Lib "kernel32" Alias "lstrlenA" ( _
        ByVal lpString As String) As Long
        
    Private Declare PtrSafe Function LoadLibrary Lib "kernel32" Alias "LoadLibraryA" ( _
        ByVal lpLibFileName As String) As LongPtr
        
    Private Declare PtrSafe Function FreeLibrary Lib "kernel32" ( _
        ByVal hLibModule As LongPtr) As Long
        
#Else
    ' 32-bit VBA declarations
    Private Declare Function gsapi_revision Lib "gsdll32.dll" ( _
        ByRef pRevision As Any, _
        ByVal length As Long) As Long
        
    Private Declare Function gsapi_new_instance Lib "gsdll32.dll" ( _
        ByRef pInstance As Long, _
        ByVal caller_handle As Long) As Long
        
    Private Declare Function gsapi_delete_instance Lib "gsdll32.dll" ( _
        ByVal instance As Long) As Long
        
    Private Declare Function gsapi_set_stdio Lib "gsdll32.dll" ( _
        ByVal instance As Long, _
        ByVal stdin_fn As Long, _
        ByVal stdout_fn As Long, _
        ByVal stderr_fn As Long) As Long
        
    Private Declare Function gsapi_init_with_args Lib "gsdll32.dll" ( _
        ByVal instance As Long, _
        ByVal argc As Long, _
        ByVal argv As Long) As Long
        
    Private Declare Function gsapi_exit Lib "gsdll32.dll" ( _
        ByVal instance As Long) As Long
        
    ' Memory management
    Private Declare Function GlobalAlloc Lib "kernel32" ( _
        ByVal uFlags As Long, _
        ByVal dwBytes As Long) As Long
        
    Private Declare Function GlobalFree Lib "kernel32" ( _
        ByVal hMem As Long) As Long
        
    Private Declare Function GlobalLock Lib "kernel32" ( _
        ByVal hMem As Long) As Long
        
    Private Declare Function GlobalUnlock Lib "kernel32" ( _
        ByVal hMem As Long) As Long
        
    Private Declare Sub CopyMemory Lib "kernel32" Alias "RtlMoveMemory" ( _
        ByVal Destination As Long, _
        ByVal Source As Long, _
        ByVal Length As Long)
        
    Private Declare Function lstrlen Lib "kernel32" Alias "lstrlenA" ( _
        ByVal lpString As String) As Long
        
    Private Declare Function LoadLibrary Lib "kernel32" Alias "LoadLibraryA" ( _
        ByVal lpLibFileName As String) As Long
        
    Private Declare Function FreeLibrary Lib "kernel32" ( _
        ByVal hLibModule As Long) As Long
#End If

' Memory allocation constants
Private Const GMEM_FIXED As Long = &H0
Private Const GMEM_ZEROINIT As Long = &H40

' ================================
' CLASS VARIABLES
' ================================

Private m_DllHandle As LongPtr
Private m_IsInitialized As Boolean
Private m_IsAvailable As Boolean
Private m_LastError As String
Private m_DllPath As String

' ================================
' CLASS EVENTS
' ================================

Private Sub Class_Initialize()
    m_DllHandle = 0
    m_IsInitialized = False
    m_IsAvailable = False
    m_LastError = ""
    m_DllPath = ""
End Sub

Private Sub Class_Terminate()
    If m_DllHandle <> 0 Then
        FreeLibrary m_DllHandle
        m_DllHandle = 0
    End If
End Sub

' ================================
' PUBLIC PROPERTIES
' ================================

' Check if Ghostscript DLL is available (lazy loading)
Public Property Get isAvailable() As Boolean
    If Not m_IsInitialized Then
        Call InitializeDLL
    End If
    isAvailable = m_IsAvailable
End Property

' Get the loaded DLL path
Public Property Get DllPath() As String
    DllPath = m_DllPath
End Property

' Get the last error message
Public Property Get LastError() As String
    LastError = m_LastError
End Property

' ================================
' PUBLIC METHODS
' ================================

' Convert PDF to images using Ghostscript API
Public Function ConvertPDFToImages(ByVal pdfPath As String, _
                                  ByVal outputPattern As String, _
                                  Optional ByVal dpi As Long = 150, _
                                  Optional ByVal firstPage As Long = 1, _
                                  Optional ByVal lastPage As Long = -1) As Boolean
    On Error GoTo ErrorHandler
    
    m_LastError = ""
    
    If Not isAvailable Then
        m_LastError = "Ghostscript DLL not available"
        OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
        ConvertPDFToImages = False
        Exit Function
    End If
    
    Dim Instance As LongPtr
    Dim Result As Long
    Dim argCount As Long
    Dim args() As String
    Dim argPtr As LongPtr
    
    ' Create new Ghostscript instance
    Result = gsapi_new_instance(Instance, 0)
    If Result <> GS_ERROR_OK Then
        m_LastError = "Failed to create instance, error: " & Result
        OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
        ConvertPDFToImages = False
        Exit Function
    End If
    
    OCRUtils.LogToFile "clsGhostscriptAPI: Created instance successfully"
    
    ' Build argument array
    Call BuildArgumentArray(args, argCount, pdfPath, outputPattern, dpi, firstPage, lastPage)
    
    ' Convert VBA string array to C-style string array
    argPtr = CreateCStringArray(args, argCount)
    If argPtr = 0 Then
        m_LastError = "Failed to create argument array"
        OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
        GoTo Cleanup
    End If
    
    ' Initialize Ghostscript with arguments
    OCRUtils.LogToFile "clsGhostscriptAPI: Initializing with " & argCount & " arguments"
    Result = gsapi_init_with_args(Instance, argCount, argPtr)
    
    If Result = GS_ERROR_OK Then
        OCRUtils.LogToFile "clsGhostscriptAPI: PDF conversion completed successfully"
        ConvertPDFToImages = True
    Else
        m_LastError = "PDF conversion failed, error: " & Result & " (" & GetErrorDescription(Result) & ")"
        OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
        ConvertPDFToImages = False
    End If
    
Cleanup:
    ' Clean up
    If argPtr <> 0 Then
        Call FreeCStringArray(argPtr, argCount)
    End If
    
    If Instance <> 0 Then
        gsapi_exit Instance
        gsapi_delete_instance Instance
    End If
    
    Exit Function
    
ErrorHandler:
    m_LastError = "Error in ConvertPDFToImages: " & Err.Description
    OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
    ConvertPDFToImages = False
    Resume Cleanup
End Function

' Convert single PDF page to image using API (optimized version)
Public Function ConvertPDFPageToImage(ByVal pdfPath As String, _
                                     ByVal pageNum As Integer) As String
    On Error GoTo ErrorHandler
    
    m_LastError = ""
    
    If Not isAvailable Then
        m_LastError = "Ghostscript DLL not available"
        ConvertPDFPageToImage = ""
        Exit Function
    End If
    
    Dim tempDir As String
    Dim outputPath As String
    Dim timestamp As String
    
    tempDir = Environ("TMP")
    If Right(tempDir, 1) <> "\" Then tempDir = tempDir & "\"
    
    timestamp = Format(Now, "yyyymmddhhnnss")
    outputPath = tempDir & "ocr_api_" & timestamp & "_" & CStr(pageNum) & ".png"
    
    ' Convert specific page
    If ConvertPDFToImages(pdfPath, outputPath, OCRConfig.GetImageDPI(), pageNum, pageNum) Then
        If OCRUtils.FileExists(outputPath) Then
            ConvertPDFPageToImage = outputPath
            OCRUtils.LogToFile "clsGhostscriptAPI: Successfully converted page " & pageNum & " to: " & outputPath
        Else
            m_LastError = "Conversion reported success but output file not found: " & outputPath
            OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
            ConvertPDFPageToImage = ""
        End If
    Else
        OCRUtils.LogToFile "clsGhostscriptAPI: Failed to convert page " & pageNum
        ConvertPDFPageToImage = ""
    End If
    
    Exit Function
    
ErrorHandler:
    m_LastError = "Error in ConvertPDFPageToImage: " & Err.Description
    OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
    ConvertPDFPageToImage = ""
End Function

' Get PDF page count using Ghostscript API
Public Function GetPDFPageCount(ByVal pdfPath As String) As Long
    On Error GoTo ErrorHandler
    
    m_LastError = ""
    
    If Not isAvailable Then
        m_LastError = "Ghostscript DLL not available"
        GetPDFPageCount = -1
        Exit Function
    End If
    
    ' Use a PostScript approach that writes page count to a file
    Dim tempFile As String
    Dim timestamp As String
    Dim tempDir As String
    
    tempDir = Environ("TMP")
    If Right(tempDir, 1) <> "\" Then tempDir = tempDir & "\"
    timestamp = Format(Now, "yyyymmddhhnnss")
    tempFile = tempDir & "gs_pagecount_" & timestamp & ".txt"
    
    Dim Instance As LongPtr
    Dim Result As Long
    Dim argCount As Long
    Dim args() As String
    Dim argPtr As LongPtr
    
    ' Create new Ghostscript instance
    Result = gsapi_new_instance(Instance, 0)
    If Result <> GS_ERROR_OK Then
        m_LastError = "Failed to create instance for page count, error: " & Result
        OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
        GetPDFPageCount = -1
        Exit Function
    End If
    
    ' Build argument array for page counting
    Call BuildPageCountArgumentArray(args, argCount, pdfPath, tempFile)
    
    ' Convert VBA string array to C-style string array
    argPtr = CreateCStringArray(args, argCount)
    If argPtr = 0 Then
        m_LastError = "Failed to create argument array for page count"
        OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
        GoTo Cleanup
    End If
    
    ' Execute Ghostscript with arguments
    OCRUtils.LogToFile "clsGhostscriptAPI: Getting page count for: " & pdfPath
    Result = gsapi_init_with_args(Instance, argCount, argPtr)
    
    If Result = GS_ERROR_OK Then
        ' Read page count from temp file
        If OCRUtils.FileExists(tempFile) Then
            Dim pageCountStr As String
            pageCountStr = ReadTextFile(tempFile)
            If IsNumeric(Trim(pageCountStr)) Then
                GetPDFPageCount = CLng(Trim(pageCountStr))
                OCRUtils.LogToFile "clsGhostscriptAPI: Successfully got page count: " & GetPDFPageCount
            Else
                m_LastError = "Invalid page count in output file: " & pageCountStr
                OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
                GetPDFPageCount = -1
            End If
            ' Clean up temp file
            On Error Resume Next
            Kill tempFile
            On Error GoTo ErrorHandler
        Else
            ' If file approach fails, fall back to terminal method
            OCRUtils.LogToFile "clsGhostscriptAPI: PostScript file write failed, falling back to terminal method"
            GetPDFPageCount = -1
        End If
    Else
        ' If PostScript approach fails entirely, fall back to terminal method
        OCRUtils.LogToFile "clsGhostscriptAPI: PostScript approach failed (" & GetErrorDescription(Result) & "), falling back to terminal method"
        GetPDFPageCount = -1
    End If
    
Cleanup:
    ' Clean up
    If argPtr <> 0 Then
        Call FreeCStringArray(argPtr, argCount)
    End If
    
    If Instance <> 0 Then
        gsapi_exit Instance
        gsapi_delete_instance Instance
    End If
    
    ' Ensure temp file is deleted
    On Error Resume Next
    If OCRUtils.FileExists(tempFile) Then Kill tempFile
    On Error GoTo 0
    
    Exit Function
    
ErrorHandler:
    m_LastError = "Error in GetPDFPageCount: " & Err.Description
    OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
    GetPDFPageCount = -1
    Resume Cleanup
End Function

' Test function to verify API integration
Public Sub TestAPI()
    On Error GoTo ErrorHandler
    
    OCRUtils.SetLogging True
    
    OCRUtils.LogToFile "=== Ghostscript API Test Started ==="
    
    ' Test DLL availability
    If isAvailable Then
        OCRUtils.LogToFile "✓ Ghostscript DLL is available at: " & m_DllPath
        
        ' Test with a sample file if available
        Dim testPdf As String
        testPdf = InputBox("Enter path to a test PDF file (or cancel to skip conversion test):", "Test PDF Path")
        
        If testPdf <> "" And OCRUtils.FileExists(testPdf) Then
            OCRUtils.LogToFile "Testing conversion with: " & testPdf
            
            ' Try simple test first
            If TestSimpleGhostscriptCall() Then
                OCRUtils.LogToFile "✓ Simple Ghostscript test passed"
                
                ' Test page count functionality
                Dim pageCount As Long
                pageCount = GetPDFPageCount(testPdf)
                If pageCount > 0 Then
                    OCRUtils.LogToFile "✓ Page count test successful: " & pageCount & " pages"
                Else
                    OCRUtils.LogToFile "✗ Page count test failed: " & m_LastError
                End If
                
                ' Test image conversion
                Dim Result As String
                Result = ConvertPDFPageToImage(testPdf, 1)
                
                If Result <> "" Then
                    OCRUtils.LogToFile "✓ API conversion successful: " & Result
                    
                    Dim testResults As String
                    testResults = "API test successful!" & vbCrLf & vbCrLf
                    If pageCount > 0 Then
                        testResults = testResults & "Page count: " & pageCount & vbCrLf
                    End If
                    testResults = testResults & "Output: " & Result & vbCrLf & vbCrLf & "Check logs for details."
                    
                    MsgBox testResults, vbInformation
                Else
                    OCRUtils.LogToFile "✗ API conversion failed: " & m_LastError
                    MsgBox "API conversion test failed: " & m_LastError & vbCrLf & "Check logs for details.", vbExclamation
                End If
            Else
                OCRUtils.LogToFile "✗ Simple Ghostscript test failed"
                MsgBox "Basic Ghostscript API test failed. Check logs for details.", vbExclamation
            End If
        Else
            OCRUtils.LogToFile "No test file provided or file doesn't exist"
            MsgBox "DLL is available but no file test performed.", vbInformation
        End If
    Else
        OCRUtils.LogToFile "✗ Ghostscript DLL is NOT available: " & m_LastError
        MsgBox "Ghostscript DLL is not available: " & m_LastError & vbCrLf & "Ensure Ghostscript is installed.", vbExclamation
    End If
    
    OCRUtils.LogToFile "=== Ghostscript API Test Completed ==="
    Exit Sub
    
ErrorHandler:
    OCRUtils.LogToFile "ERROR in TestAPI: " & Err.Description
    MsgBox "Test failed with error: " & Err.Description, vbCritical
End Sub

' ================================
' PRIVATE METHODS
' ================================

' Initialize DLL (lazy loading)
Private Sub InitializeDLL()
    On Error GoTo ErrorHandler
    
    If m_IsInitialized Then Exit Sub
    
    ' Try to load the appropriate DLL
    #If VBA7 Then
        m_DllPath = "gsdll64.dll"
        m_DllHandle = LoadLibrary(m_DllPath)
        If m_DllHandle = 0 Then
            m_DllPath = "gsdll32.dll"
            m_DllHandle = LoadLibrary(m_DllPath)
        End If
    #Else
        m_DllPath = "gsdll32.dll"
        m_DllHandle = LoadLibrary(m_DllPath)
    #End If
    
    m_IsInitialized = True
    
    If m_DllHandle <> 0 Then
        ' Test basic API functionality
        Dim revision(0 To 199) As Byte
        Dim Result As Long
        Result = gsapi_revision(revision(0), 200)
        m_IsAvailable = (Result = GS_ERROR_OK)
        
        If m_IsAvailable Then
            OCRUtils.LogToFile "clsGhostscriptAPI: DLL available at " & m_DllPath & ", revision: " & ByteArrayToString(revision)
        Else
            m_LastError = "DLL loaded but API test failed"
        End If
    Else
        m_IsAvailable = False
        m_LastError = "Failed to load Ghostscript DLL"
        m_DllPath = ""
    End If
    
    Exit Sub
    
ErrorHandler:
    m_IsInitialized = True
    m_IsAvailable = False
    m_LastError = "Error initializing DLL: " & Err.Description
    OCRUtils.LogToFile "clsGhostscriptAPI: " & m_LastError
End Sub

' Build argument array for Ghostscript
Private Sub BuildArgumentArray(ByRef args() As String, _
                              ByRef argCount As Long, _
                              ByVal pdfPath As String, _
                              ByVal outputPattern As String, _
                              ByVal dpi As Long, _
                              ByVal firstPage As Long, _
                              ByVal lastPage As Long)
    
    Dim i As Long
    i = 0
    
    ReDim args(0 To 15) ' Start with reasonable size
    
    args(i) = "gs": i = i + 1
    args(i) = "-dNOPAUSE": i = i + 1
    args(i) = "-dBATCH": i = i + 1
    args(i) = "-dSAFER": i = i + 1
    args(i) = "-sDEVICE=png16m": i = i + 1
    args(i) = "-r" & CStr(dpi): i = i + 1
    args(i) = "-dQUIET": i = i + 1  ' Suppress stdout output
    
    If firstPage > 0 Then
        args(i) = "-dFirstPage=" & CStr(firstPage): i = i + 1
    End If
    
    If lastPage > 0 And lastPage >= firstPage Then
        args(i) = "-dLastPage=" & CStr(lastPage): i = i + 1
    End If
    
    args(i) = "-sOutputFile=" & outputPattern: i = i + 1
    args(i) = pdfPath: i = i + 1
    
    argCount = i
    
    ' Resize array to actual size
    ReDim Preserve args(0 To argCount - 1)
    
    ' Log the arguments
    Dim logArgs As String
    For i = 0 To argCount - 1
        logArgs = logArgs & """" & args(i) & """ "
    Next i
    OCRUtils.LogToFile "clsGhostscriptAPI: Arguments: " & logArgs
End Sub

' Build argument array for page count operation
Private Sub BuildPageCountArgumentArray(ByRef args() As String, _
                                       ByRef argCount As Long, _
                                       ByVal pdfPath As String, _
                                       ByVal outputFile As String)
    
    Dim i As Long
    i = 0
    
    ReDim args(0 To 10) ' Start with reasonable size
    
    args(i) = "gs": i = i + 1
    args(i) = "-dNOPAUSE": i = i + 1
    args(i) = "-dBATCH": i = i + 1
    args(i) = "-dSAFER": i = i + 1
    args(i) = "-dQUIET": i = i + 1
    args(i) = "-dNODISPLAY": i = i + 1
    
    ' PostScript command to count pages and write to file
    ' The command reads the PDF, counts pages, writes to output file
    args(i) = "-c": i = i + 1
    
    ' Build a PostScript program that writes page count to the output file
    ' Escape paths for PostScript
    Dim psOutputFile As String
    Dim psPdfPath As String
    psOutputFile = EscapePostScriptString(outputFile)
    psPdfPath = EscapePostScriptString(pdfPath)
    
    Dim psCommand As String
    psCommand = "(" & psOutputFile & ") (w) file " & _
                "(" & psPdfPath & ") (r) file runpdfbegin " & _
                "pdfpagecount " & _
                "16 string cvs " & _
                "writestring " & _
                "closefile " & _
                "quit"
    
    args(i) = psCommand: i = i + 1
    
    argCount = i
    
    ' Resize array to actual size
    ReDim Preserve args(0 To argCount - 1)
    
    ' Log the arguments
    Dim logArgs As String
    Dim j As Long
    For j = 0 To argCount - 1
        logArgs = logArgs & """" & args(j) & """ "
    Next j
    OCRUtils.LogToFile "clsGhostscriptAPI: Page count arguments: " & logArgs
End Sub

' Read text from file
Private Function ReadTextFile(ByVal FilePath As String) As String
    On Error GoTo ErrorHandler
    
    Dim fNum As Integer
    Dim fileContent As String
    
    fNum = FreeFile
    Open FilePath For Input As #fNum
    fileContent = Input$(LOF(fNum), fNum)
    Close #fNum
    
    ReadTextFile = fileContent
    Exit Function
    
ErrorHandler:
    ReadTextFile = ""
    OCRUtils.LogToFile "clsGhostscriptAPI: Error reading file " & FilePath & ": " & Err.Description
End Function

' Escape string for PostScript
Private Function EscapePostScriptString(ByVal inputStr As String) As String
    Dim Result As String
    Dim i As Long
    Dim char As String
    
    Result = ""
    For i = 1 To Len(inputStr)
        char = Mid(inputStr, i, 1)
        Select Case char
            Case "\"
                Result = Result & "\\"
            Case "("
                Result = Result & "\("
            Case ")"
                Result = Result & "\)"
            Case vbCr
                Result = Result & "\r"
            Case vbLf
                Result = Result & "\n"
            Case vbTab
                Result = Result & "\t"
            Case Else
                Result = Result & char
        End Select
    Next i
    
    EscapePostScriptString = Result
End Function

' NOTE: Binary search approach disabled due to reliability issues
' Ghostscript API returns success even for non-existent pages, making
' page existence detection unreliable. Fall back to terminal method for now.

' ' Get PDF page count using binary search approach (DISABLED)
' Private Function GetPDFPageCountBinarySearch(ByVal pdfPath As String) As Long
'     GetPDFPageCountBinarySearch = -1 ' Always fall back to terminal method
' End Function

' Create C-style string array from VBA string array
Private Function CreateCStringArray(ByRef vbArray() As String, ByVal Count As Long) As LongPtr
    On Error GoTo ErrorHandler
    
    Dim i As Long
    Dim ptrArray As LongPtr
    Dim stringPtr As LongPtr
    Dim stringLen As Long
    Dim stringBytes() As Byte
    
    ' Allocate array of pointers
    ptrArray = GlobalAlloc(GMEM_FIXED Or GMEM_ZEROINIT, Count * LenB(stringPtr))
    If ptrArray = 0 Then
        OCRUtils.LogToFile "clsGhostscriptAPI: Failed to allocate pointer array"
        CreateCStringArray = 0
        Exit Function
    End If
    
    ' Allocate and copy each string
    For i = 0 To Count - 1
        ' Convert string to byte array with null terminator
        stringBytes = StrConv(vbArray(i), vbFromUnicode)
        stringLen = UBound(stringBytes) - LBound(stringBytes) + 2 ' +1 for null terminator, +1 for array bounds
        
        stringPtr = GlobalAlloc(GMEM_FIXED, stringLen)
        If stringPtr = 0 Then
            OCRUtils.LogToFile "clsGhostscriptAPI: Failed to allocate string " & i & ": " & vbArray(i)
            ' Clean up previously allocated strings
            Call FreeCStringArray(ptrArray, i)
            CreateCStringArray = 0
            Exit Function
        End If
        
        ' Copy string data with null terminator
        CopyMemory stringPtr, VarPtr(stringBytes(LBound(stringBytes))), UBound(stringBytes) - LBound(stringBytes) + 1
        ' Add null terminator
        CopyMemory stringPtr + (UBound(stringBytes) - LBound(stringBytes) + 1), VarPtr(CByte(0)), 1
        
        ' Store pointer in array
        CopyMemory ptrArray + (i * LenB(stringPtr)), VarPtr(stringPtr), LenB(stringPtr)
        
        OCRUtils.LogToFile "clsGhostscriptAPI: Allocated arg " & i & ": " & vbArray(i) & " (len=" & stringLen & ")"
    Next i
    
    CreateCStringArray = ptrArray
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "clsGhostscriptAPI: Error in CreateCStringArray: " & Err.Description
    If ptrArray <> 0 Then
        Call FreeCStringArray(ptrArray, i)
    End If
    CreateCStringArray = 0
End Function

' Free C-style string array
Private Sub FreeCStringArray(ByVal ptrArray As LongPtr, ByVal Count As Long)
    On Error Resume Next
    
    Dim i As Long
    Dim stringPtr As LongPtr
    
    If ptrArray = 0 Then Exit Sub
    
    ' Free individual strings
    For i = 0 To Count - 1
        CopyMemory VarPtr(stringPtr), ptrArray + (i * LenB(stringPtr)), LenB(stringPtr)
        If stringPtr <> 0 Then
            GlobalFree stringPtr
        End If
    Next i
    
    ' Free the pointer array
    GlobalFree ptrArray
End Sub

' Convert byte array to string (for revision info)
Private Function ByteArrayToString(ByRef Bytes() As Byte) As String
    Dim i As Long
    Dim Result As String
    For i = LBound(Bytes) To UBound(Bytes)
        If Bytes(i) = 0 Then Exit For
        ' Only add printable ASCII characters
        If Bytes(i) >= 32 And Bytes(i) <= 126 Then
            Result = Result & Chr(Bytes(i))
        End If
    Next i
    If Result = "" Then Result = "[Binary Data]"
    ByteArrayToString = Result
End Function

' Get error description
Private Function GetErrorDescription(ByVal errorCode As Long) As String
    Select Case errorCode
        Case GS_ERROR_OK: GetErrorDescription = "No error"
        Case GS_ERROR_UNKNOWNERROR: GetErrorDescription = "Unknown error"
        Case GS_ERROR_DICTFULL: GetErrorDescription = "Dictionary full"
        Case GS_ERROR_DICTSTACKOVERFLOW: GetErrorDescription = "Dictionary stack overflow"
        Case GS_ERROR_DICTSTACKUNDERFLOW: GetErrorDescription = "Dictionary stack underflow"
        Case GS_ERROR_EXECSTACKOVERFLOW: GetErrorDescription = "Execution stack overflow"
        Case GS_ERROR_INTERRUPT: GetErrorDescription = "Interrupt"
        Case GS_ERROR_INVALIDACCESS: GetErrorDescription = "Invalid access"
        Case GS_ERROR_INVALIDEXIT: GetErrorDescription = "Invalid exit"
        Case GS_ERROR_INVALIDFILEACCESS: GetErrorDescription = "Invalid file access"
        Case GS_ERROR_INVALIDFONT: GetErrorDescription = "Invalid font"
        Case GS_ERROR_INVALIDRESTORE: GetErrorDescription = "Invalid restore"
        Case GS_ERROR_IOERROR: GetErrorDescription = "I/O error"
        Case GS_ERROR_LIMITCHECK: GetErrorDescription = "Limit check"
        Case GS_ERROR_NOCURRENTPOINT: GetErrorDescription = "No current point"
        Case GS_ERROR_RANGECHECK: GetErrorDescription = "Range check"
        Case GS_ERROR_STACKOVERFLOW: GetErrorDescription = "Stack overflow"
        Case GS_ERROR_STACKUNDERFLOW: GetErrorDescription = "Stack underflow"
        Case GS_ERROR_SYNTAXERROR: GetErrorDescription = "Syntax error"
        Case GS_ERROR_TIMEOUT: GetErrorDescription = "Timeout"
        Case GS_ERROR_TYPECHECK: GetErrorDescription = "Type check"
        Case GS_ERROR_UNDEFINED: GetErrorDescription = "Undefined"
        Case GS_ERROR_UNDEFINEDFILENAME: GetErrorDescription = "Undefined filename"
        Case GS_ERROR_UNDEFINEDRESULT: GetErrorDescription = "Undefined result"
        Case GS_ERROR_UNMATCHEDMARK: GetErrorDescription = "Unmatched mark"
        Case GS_ERROR_VMERROR: GetErrorDescription = "VM error"
        Case GS_ERROR_CONFIGURATIONERROR: GetErrorDescription = "Configuration error"
        Case GS_ERROR_UNDEFINEDRESOURCE: GetErrorDescription = "Undefined resource"
        Case GS_ERROR_UNREGISTERED: GetErrorDescription = "Unregistered"
        Case GS_ERROR_INVALIDCONTEXT: GetErrorDescription = "Invalid context"
        Case GS_ERROR_INVALIDID: GetErrorDescription = "Invalid ID"
        Case GS_ERROR_HIT: GetErrorDescription = "Hit"
        Case GS_ERROR_FATAL: GetErrorDescription = "Fatal error"
        Case Else: GetErrorDescription = "Unknown error code: " & errorCode
    End Select
End Function

' Simple test to verify basic Ghostscript API functionality
Private Function TestSimpleGhostscriptCall() As Boolean
    On Error GoTo ErrorHandler
    
    Dim Instance As LongPtr
    Dim Result As Long
    
    OCRUtils.LogToFile "clsGhostscriptAPI: Testing simple API call..."
    
    ' Create new Ghostscript instance
    Result = gsapi_new_instance(Instance, 0)
    If Result <> GS_ERROR_OK Then
        OCRUtils.LogToFile "clsGhostscriptAPI: Simple test - Failed to create instance, error: " & Result
        TestSimpleGhostscriptCall = False
        Exit Function
    End If
    
    OCRUtils.LogToFile "clsGhostscriptAPI: Simple test - Instance created successfully"
    
    ' Just test initialization with minimal args
    Dim args(0 To 2) As String
    args(0) = "gs"
    args(1) = "-dNODISPLAY"
    args(2) = "-dBATCH"
    
    Dim argPtr As LongPtr
    argPtr = CreateCStringArray(args, 3)
    
    If argPtr <> 0 Then
        Result = gsapi_init_with_args(Instance, 3, argPtr)
        OCRUtils.LogToFile "clsGhostscriptAPI: Simple test - Init result: " & Result
        
        Call FreeCStringArray(argPtr, 3)
        
        If Result = GS_ERROR_OK Then
            TestSimpleGhostscriptCall = True
        Else
            TestSimpleGhostscriptCall = False
            OCRUtils.LogToFile "clsGhostscriptAPI: Simple test failed with error: " & Result & " (" & GetErrorDescription(Result) & ")"
        End If
    Else
        TestSimpleGhostscriptCall = False
        OCRUtils.LogToFile "clsGhostscriptAPI: Simple test - Failed to create argument array"
    End If
    
    ' Clean up
    If Instance <> 0 Then
        gsapi_exit Instance
        gsapi_delete_instance Instance
    End If
    
    Exit Function
    
ErrorHandler:
    OCRUtils.LogToFile "clsGhostscriptAPI: Error in TestSimpleGhostscriptCall: " & Err.Description
    TestSimpleGhostscriptCall = False
    
    If Instance <> 0 Then
        gsapi_exit Instance
        gsapi_delete_instance Instance
    End If
End Function
