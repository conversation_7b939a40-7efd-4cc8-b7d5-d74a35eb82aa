VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
END
Attribute VB_Name = "ThisWorkbook"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = True

' In ThisWorkbook module
' This event is triggered when the workbook is opened.
' It initializes the VBA OCR project by setting the status bar message,
' loading OCR configurations, and handling any errors.
' The ribbon itself will call its Get* callbacks on load, and Ribbon_OnLoad will handle initial refresh if needed.
Private Sub Workbook_Open()
    Application.StatusBar = "Initializing VBA OCR Project for workbook: " & ThisWorkbook.Name
    On Error GoTo ErrorHandler
    
    ' Initialize OCR Configuration (loads from config.json)
    OCRConfig.InitializeOCRConfig
    ' Enable debug logging globally
    OCRUtils.SetLogging True
    
    ' The Ribbon_OnLoad callback in RibbonCallbacks.bas will handle gRibbonUI.Invalidate
    ' No need to call RefreshRibbon directly here, as Ribbon_OnLoad might not have fired yet.
    
    Application.StatusBar = False ' Clear status bar after successful init
    Exit Sub

ErrorHandler:
    MsgBox "An error occurred during Workbook_Open initialization: " & Err.Description, vbCritical, "Initialization Error"
    Application.StatusBar = False ' Clear status bar on error too
End Sub

