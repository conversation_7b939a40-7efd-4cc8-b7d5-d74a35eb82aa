VERSION 5.00
Begin {C62A69F0-16DC-11CE-9E98-00AA00574A4F} frmSettings 
   Caption         =   "VBA-OCR Settings"
   ClientHeight    =   2440
   ClientLeft      =   90
   ClientTop       =   300
   ClientWidth     =   3520
   OleObjectBlob   =   "frmSettings.frx":0000
   StartUpPosition =   1  'CenterOwner
End
Attribute VB_Name = "frmSettings"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

' Event handler for when OCR Provider selection changes
Private Sub lstOCRProvider_Change()
    On Error GoTo ErrorHandler
    
    ' Only update if initialization is complete (form is fully loaded)
    If Me.Visible Then
        OCRUtils.LogToFile "FrmSettings: OCR Provider changed to: " & Me.lstOCRProvider.Value
        UserFromSettings.UpdateModelListForOCR Me
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in lstOCRProvider_Change: " & Err.Description
End Sub

' Event handler for when Extraction Provider selection changes
Private Sub lstExtractionProvider_Change()
    On Error GoTo ErrorHandler
    
    ' Only update if initialization is complete (form is fully loaded)
    If Me.Visible Then
        OCRUtils.LogToFile "FrmSettings: Extraction Provider changed to: " & Me.lstExtractionProvider.Value
        UserFromSettings.UpdateModelListForExtraction Me
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in lstExtractionProvider_Change: " & Err.Description
End Sub

' Extract Log Fields checkbox removed - extraction now controlled by ribbon buttons

' Form initialization
Private Sub UserForm_Initialize()
    On Error GoTo ErrorHandler
    
    ' Use quick initialization with lazy loading
    FormLoadingHelpers.QuickInitializeSettingsForm Me
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in UserForm_Initialize: " & Err.Description
    MsgBox "Error initializing settings form: " & Err.Description, vbCritical
End Sub

Private Sub UserForm_Activate()
    On Error GoTo ErrorHandler
    
    OCRUtils.LogToFile "FrmSettings: UserForm_Activate triggered"
    
    ' Ensure the form reference is set for the helper module
    FormLoadingHelpers.SetFormForLazyLoad Me
    
    ' Call the main data loading routine
    FormLoadingHelpers.LazyLoadProviderData
    
    OCRUtils.LogToFile "FrmSettings: LazyLoadProviderData called from UserForm_Activate"
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in UserForm_Activate: " & Err.Description
    MsgBox "Error activating settings form: " & Err.Description, vbCritical
End Sub

' Refresh button click handler
Private Sub cmdRefresh_Click()
    On Error GoTo ErrorHandler

    ' Clear caches to force fresh data
    OCRConfig.ClearAllCaches
    
    ' Speed up refresh: disable logging
    OCRUtils.SetLogging False
    ' Show progress to user
    ProgressHelpers.ShowProgress "Refreshing providers and models..."

    ' Refresh local models (Ollama, LM Studio)
    UserFromSettings.RefreshLocalModels Me

    ' Update the UI lists after refresh
    PopulateProviderList Me.lstOCRProvider, False
    PopulateProviderList Me.lstExtractionProvider, True
    UserFromSettings.UpdateModelListForOCR Me
    UserFromSettings.UpdateModelListForExtraction Me

    ' Done: hide progress and restore logging
    ProgressHelpers.HideProgress
    OCRUtils.SetLogging True
    Exit Sub

ErrorHandler:
    ' Ensure progress dialog is closed and logging restored
    ProgressHelpers.HideProgress
    OCRUtils.SetLogging True
    OCRUtils.LogToFile "FrmSettings: Error in cmdRefresh_Click: " & Err.Description
    MsgBox "Error refreshing models: " & Err.Description, vbCritical
End Sub

' OK button click handler
Private Sub cmdOK_Click()
    On Error GoTo ErrorHandler
    
    ' Save settings and close form
    UserFromSettings.SaveSettingsFromForm Me
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in cmdOK_Click: " & Err.Description
    MsgBox "Error saving settings: " & Err.Description, vbCritical
End Sub

' Cancel button click handler
Private Sub cmdCancel_Click()
    On Error GoTo ErrorHandler
    
    ' Close form without saving
    Unload Me
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in cmdCancel_Click: " & Err.Description
End Sub

' Event handlers for PDF engine radio buttons
Private Sub optMistralOCR_Click()
    On Error GoTo ErrorHandler
    
    If Me.Visible Then
        OCRUtils.LogToFile "FrmSettings: PDF Engine changed to Mistral OCR"
        UserFromSettings.UpdateOpenRouterPDFModelVisibility Me
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in optMistralOCR_Click: " & Err.Description
End Sub

Private Sub optPDFText_Click()
    On Error GoTo ErrorHandler
    
    If Me.Visible Then
        OCRUtils.LogToFile "FrmSettings: PDF Engine changed to PDF-Text"
        UserFromSettings.UpdateOpenRouterPDFModelVisibility Me
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in optPDFText_Click: " & Err.Description
End Sub

Private Sub optNative_Click()
    On Error GoTo ErrorHandler
    
    If Me.Visible Then
        OCRUtils.LogToFile "FrmSettings: PDF Engine changed to Native"
        UserFromSettings.UpdateOpenRouterPDFModelVisibility Me
    End If
    
    Exit Sub
ErrorHandler:
    OCRUtils.LogToFile "FrmSettings: Error in optNative_Click: " & Err.Description
End Sub

'===============================================================================
' LOG CONFIGURATION EVENT HANDLERS (Page 2)
'===============================================================================

' Essential Fields checkbox events
Private Sub chkLogFrom_Click()
    On Error Resume Next
    refFrom.Enabled = chkLogFrom.Value
    txtFrom.Enabled = chkLogFrom.Value
    OCRUtils.LogToFile "FrmSettings: chkLogFrom changed to: " & chkLogFrom.Value
End Sub

Private Sub chkLogTo_Click()
    On Error Resume Next
    refTo.Enabled = chkLogTo.Value
    txtTo.Enabled = chkLogTo.Value
    OCRUtils.LogToFile "FrmSettings: chkLogTo changed to: " & chkLogTo.Value
End Sub

Private Sub chkLogReference_Click()
    On Error Resume Next
    refReference.Enabled = chkLogReference.Value
    txtReference.Enabled = chkLogReference.Value
    OCRUtils.LogToFile "FrmSettings: chkLogReference changed to: " & chkLogReference.Value
End Sub

Private Sub chkLogDate_Click()
    On Error Resume Next
    refDate.Enabled = chkLogDate.Value
    txtDate.Enabled = chkLogDate.Value
    OCRUtils.LogToFile "FrmSettings: chkLogDate changed to: " & chkLogDate.Value
End Sub

Private Sub chkLogSubject_Click()
    On Error Resume Next
    refSubject.Enabled = chkLogSubject.Value
    txtSubject.Enabled = chkLogSubject.Value
    OCRUtils.LogToFile "FrmSettings: chkLogSubject changed to: " & chkLogSubject.Value
End Sub

Private Sub chkLogReferences_Click()
    On Error Resume Next
    refReferences.Enabled = chkLogReferences.Value
    txtReferences.Enabled = chkLogReferences.Value
    OCRUtils.LogToFile "FrmSettings: chkLogReferences changed to: " & chkLogReferences.Value
End Sub

Private Sub chkLogBody_Click()
    On Error Resume Next
    refBody.Enabled = chkLogBody.Value
    txtBody.Enabled = chkLogBody.Value
    OCRUtils.LogToFile "FrmSettings: chkLogBody changed to: " & chkLogBody.Value
End Sub

' Helper Fields checkbox events
Private Sub chkLogSummary_Click()
    On Error Resume Next
    refSummary.Enabled = chkLogSummary.Value
    txtSummary.Enabled = chkLogSummary.Value
    OCRUtils.LogToFile "FrmSettings: chkLogSummary changed to: " & chkLogSummary.Value
End Sub

Private Sub chkLLogTags_Click()
    On Error Resume Next
    refTags.Enabled = chkLLogTags.Value
    txtTags.Enabled = chkLLogTags.Value
    OCRUtils.LogToFile "FrmSettings: chkLLogTags changed to: " & chkLLogTags.Value
End Sub

' Tracing Fields checkbox events
Private Sub chkResponse_Click()
    On Error Resume Next
    refResponse.Enabled = chkResponse.Value
    txtResponse.Enabled = chkResponse.Value
    OCRUtils.LogToFile "FrmSettings: chkResponse changed to: " & chkResponse.Value
End Sub

Private Sub chkResponseDate_Click()
    On Error Resume Next
    refResponseDate.Enabled = chkResponseDate.Value
    txtResponseDate.Enabled = chkResponseDate.Value
    OCRUtils.LogToFile "FrmSettings: chkResponseDate changed to: " & chkResponseDate.Value
End Sub

Private Sub chkReferring_Click()
    On Error Resume Next
    refReferring.Enabled = chkReferring.Value
    txtRefering.Enabled = chkReferring.Value
    OCRUtils.LogToFile "FrmSettings: chkReferring changed to: " & chkReferring.Value
End Sub

Private Sub chkReferringDate_Click()
    On Error Resume Next
    refReferringDate.Enabled = chkReferringDate.Value
    txtReferingDate.Enabled = chkReferringDate.Value
    OCRUtils.LogToFile "FrmSettings: chkReferringDate changed to: " & chkReferringDate.Value
End Sub

